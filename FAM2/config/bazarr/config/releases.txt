[{"name": "v1.5.3-beta.11", "body": "From newest to oldest:\r\n- Added support for .mk3d video file extension. [ced0eee](https://github.com/morpheus65535/bazarr/commit/ced0eee66b57dec3f834770704dc615bdefd0c41) https://github.com/morpheus65535/bazarr/issues/2952\r\n- Improved greeksubs provider to prevent reuse of the content from the first result when using interactive search modal. [9bed1bd](https://github.com/morpheus65535/bazarr/commit/9bed1bd3766f4a5a23d1afd7febbf729d27e71b4) https://github.com/morpheus65535/bazarr/issues/2858", "date": "2025-07-15T10:50:54Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.11/bazarr.zip"}, {"name": "v1.5.3-beta.8", "body": "From newest to oldest:\n- Fixed upgrade logic to prevent upgrade loop. [d334190](https://github.com/morpheus65535/bazarr/commit/d3341901163796c7392fa8d0f0864d78c90a0c85)\n- Fixed (again) the shutdown/restart of Bazarr. #2941 [aeaaeb6](https://github.com/morpheus65535/bazarr/commit/aeaaeb6b5352d472680b5329ba7b38f4a838af18)", "date": "2025-07-07T06:07:02Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.8/bazarr.zip"}, {"name": "v1.5.3-beta.7", "body": "From newest to oldest:\n- Improved Gemini translator with some hotfixes [99f596e](https://github.com/morpheus65535/bazarr/commit/99f596e30bea466d98cdfdf247880bca105afc32)", "date": "2025-06-17T06:06:56Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.7/bazarr.zip"}, {"name": "v1.5.3-beta.6", "body": "From newest to oldest:\n- Fixed shutdown/restart command not acting properly on some Linux and macOS operating system. #2941 [3d57a12](https://github.com/morpheus65535/bazarr/commit/3d57a1231cc0b89ce489d9def00b3eafd221e477)", "date": "2025-06-12T06:06:54Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.6/bazarr.zip"}, {"name": "v1.5.3-beta.5", "body": "From newest to oldest:\n- Fix infinite loop during restoration of backup on certain non-Windows platforms [cc5fa42](https://github.com/morpheus65535/bazarr/commit/cc5fa4200c056b06d9df5470985bb0e6237aa948)", "date": "2025-06-09T06:07:13Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.5/bazarr.zip"}, {"name": "v1.5.3-beta.4", "body": "From newest to oldest:\n- Added Gemini AI translation option [ff8466c](https://github.com/morpheus65535/bazarr/commit/ff8466cfbad76f766ae98f98ea4b223993c6c798)\n- Fixed anidb to handle one-to-many ID mapping [ccd667d](https://github.com/morpheus65535/bazarr/commit/ccd667d9537c2587b0aa436810658ae02ff372ee)", "date": "2025-06-07T06:05:55Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.4/bazarr.zip"}, {"name": "v1.5.3-beta.3", "body": "From newest to oldest:\n- Added language mapping to WhisperAI (thanks to GitHub user v3DJG6G<PERSON>) [6794eeb](https://github.com/morpheus65535/bazarr/commit/6794eeb2a4f0db37b5f5f06b657563ab057f80aa)\n- Fixed long block of text being unnecessary removed from subtitles files [fa9b0f0](https://github.com/morpheus65535/bazarr/commit/fa9b0f074866f74655595f1549dc00802a8c22a6)", "date": "2025-05-26T06:07:00Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.3/bazarr.zip"}, {"name": "v1.5.3-beta.2", "body": "From newest to oldest:\n- Refactored Sonarr and Radarr hook. It may be a breaking change so users should review webhook parameters following information in Bazarr's settings. [a3102e8](https://github.com/morpheus65535/bazarr/commit/a3102e8a19ee74a00a6f25c3a78c93077029cf5b)", "date": "2025-05-23T06:06:42Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.2/bazarr.zip"}, {"name": "v1.5.3-beta.1", "body": "From newest to oldest:\n- Fixed losing titulky VIP status during an active login session [f2cf1c0](https://github.com/morpheus65535/bazarr/commit/f2cf1c066c7b4487e29e6ef9deabd4eafb9259ef)", "date": "2025-05-15T06:06:28Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.1/bazarr.zip"}, {"name": "v1.5.3-beta.0", "body": "From newest to oldest:\n- Added option to include language only when matching audio [ac19e37](https://github.com/morpheus65535/bazarr/commit/ac19e3743eb827f3c258f7d6e384333532224ef5)", "date": "2025-05-14T06:06:29Z", "prerelease": true, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.3-beta.0/bazarr.zip"}, {"name": "v1.5.2", "body": "From newest to oldest:\n- Added frontend only Docker Image to support developpers [eacb8b7](https://github.com/morpheus65535/bazarr/commit/eacb8b70682363b33db74ca4beee1be5d4a09c6d)\n- Fixed profiles sync with Sonarr and Radarr. #2932 [71cd0c8](https://github.com/morpheus65535/bazarr/commit/71cd0c80c7e99b079aa7abee573bc6e959dbcf18)\n- Fixed titrari provider advanced search endpoint. #2930 [794b006](https://github.com/morpheus65535/bazarr/commit/794b0067b075b1060258fc99bc20cabf8d8a2e77)\n- Fixed subzero mods hearing-impaired removal regex broken in a82dca9. #2849 [3a248b3](https://github.com/morpheus65535/bazarr/commit/3a248b374181997b4157dfc3c5ed67297390897e)\n- Fixed small issues with subdl to improve throttling conditions [c853de8](https://github.com/morpheus65535/bazarr/commit/c853de8bd76b22cb394ebe88152aadc0f5743fd9)\n- Fixed Sub-Zero Hearing Impaired subtitle content modifications. #2849 [a82dca9](https://github.com/morpheus65535/bazarr/commit/a82dca914a6e9bce9e4ea7a91ba1ec62030910e7)\n- Improved backup restore to help fix long-standing issue. #2850 [17024a9](https://github.com/morpheus65535/bazarr/commit/17024a9899fe75047f903df41827b4820cd273a6)\n- Updated keyboard interrupt handling code [db49fd6](https://github.com/morpheus65535/bazarr/commit/db49fd6b03281a1f1738bc28f1f2f1cd79b3b61b)\n- Updated regex to not remove sign in ASS subtitles in embedded subtitles [2964a0c](https://github.com/morpheus65535/bazarr/commit/2964a0c9ec57102a23163f21d2f8f6f65c60715e)\n- Changed notification position to bottom-left to not obscure page navigation [c752796](https://github.com/morpheus65535/bazarr/commit/c752796b829303d856b1e6c880c489674b35dc2f)\n- Fixed improper detection of Chinese subtitles for embedded subtitles provider. #2914 [ee817c2](https://github.com/morpheus65535/bazarr/commit/ee817c2b7e2e346d35475674e2a55a66aed21cb1)\n- Fixed improper uppercase for some release groups in matching routine [82dad60](https://github.com/morpheus65535/bazarr/commit/82dad607c1a9f63f209a76178acb8a8f55a91de7)\n- Additional multiline log file fixes [b4e4ea5](https://github.com/morpheus65535/bazarr/commit/b4e4ea5bb9a9b216f12af6ea8ae9f842a40d9d47)\n- Handle multiline log output without proper log file timestamp headers like produced by KnowIt errors [e91b71c](https://github.com/morpheus65535/bazarr/commit/e91b71c6ec17f1587e6f8a49b381c518df63a0e8)\n- Whisper - eliminate \"missing/bad audio track\" errors when files have multiple audio streams [d6e7773](https://github.com/morpheus65535/bazarr/commit/d6e7773a0f1731e178fca9d925319a87e824dc0f)\n- Added an announcement about upcoming deprecation of Python 3.8 in Bazarr v1.6 [85d75a7](https://github.com/morpheus65535/bazarr/commit/85d75a72b81a8899046b47a1c299ecb51113b188)\n- Added experimental Python 3.13 compatibility. #2803 [759da8a](https://github.com/morpheus65535/bazarr/commit/759da8a111b5e403512bf581aba037575a92516f)\n- Replaced pipes with shlex in custom_libs/libfilebot/main.py [4642db3](https://github.com/morpheus65535/bazarr/commit/4642db334a8b94a865cbf811ab90fc5316c74db0)\n- Replaced imghdr with filetype for image detection [9825a3a](https://github.com/morpheus65535/bazarr/commit/9825a3a10959dc6196fd4bfd3a5ec24a9c8eb8f8)\n- Added an unauthenticated API endpoint to test Bazarr availability: /api/system/ping [2c7294d](https://github.com/morpheus65535/bazarr/commit/2c7294de0de5620e9c33ff1cfb99f6e6b6278dbb)\n- Fixed UnboundLocalError when viewing log files [84ebcf7](https://github.com/morpheus65535/bazarr/commit/84ebcf7a378bfae2a07fa3c6633b0e3b2c384637)\n- Added Sync button to individual Series and Movie pages [525d569](https://github.com/morpheus65535/bazarr/commit/525d569d09e707a0dcd317bdad06a571fc2e794f)\n- Fixed upgrade process again to prevent infinite loop of subtitles upgrade. #2749 [81909ca](https://github.com/morpheus65535/bazarr/commit/81909caf51de0801389e80c191769be964979b84)\n- Added a validation to confirm if media file exists during initial sync before trying to search for subtitles. #2866 [63a3622](https://github.com/morpheus65535/bazarr/commit/63a3622615e2b5ebb2b68832136da1f224cfb4b7)\n- Added Weekly option to Search and Upgrade Subtitles settings [7dbc3cb](https://github.com/morpheus65535/bazarr/commit/7dbc3cb17ca178c17f9bb9673df926a00f7d002e)\n- Fix for prior uppercase mod fix [31fe371](https://github.com/morpheus65535/bazarr/commit/31fe37178279b5bfdb49351b176e394f431cc1b4)\n- Improved uppercase detection / execution in Fix Uppercase mod [2a330f6](https://github.com/morpheus65535/bazarr/commit/2a330f63dc18562f5c4647be2680705c7a225efb)\n- Fixed another issue with Yavka provider. #2225 [cefd163](https://github.com/morpheus65535/bazarr/commit/cefd1638dd5809cda93473a5a34a26d7156a6ff3)\n- Multiple providers - Removed overwrite of provider language object [515282e](https://github.com/morpheus65535/bazarr/commit/515282eed862c706f4ed2ace14f6c51eee631028)\n- Added animetosho provider german, vietnamese, russian and indonesian language support [fa4aca8](https://github.com/morpheus65535/bazarr/commit/fa4aca8bb1b7cbe820ba7c1d803d1bc628989cd0)\n- Regielive fix - Removed overwrite of provider language object [7cb471f](https://github.com/morpheus65535/bazarr/commit/7cb471fc200d768bfdae895942485e9b50a9271c)\n- Fixed properly timezone usage to prevent high CPU usage. #1914 [c22599a](https://github.com/morpheus65535/bazarr/commit/c22599a8bfbfede83dd4a748cc68301af21a0213)\n- Added animekalesi.com provider for Turkish Anime subtitles [3b4415e](https://github.com/morpheus65535/bazarr/commit/3b4415ec3f9349b8ff42cbe8631413fb4ef56222)\n- Fixed additional error catching when trying to bind to IPv6 when it's not supported. #2738 [fc9f94e](https://github.com/morpheus65535/bazarr/commit/fc9f94e9174604c6fa4fbb2700dfec2d07dc7211)\n- Added passing of video filename to detect-language for whisper provider [6c65267](https://github.com/morpheus65535/bazarr/commit/6c652673820ddd9aa29632cce88412b06be5bbab)\n- Fixed unwanted removal of subtitles format tags when using any mods. #2846 [1a93eb4](https://github.com/morpheus65535/bazarr/commit/1a93eb4049d859b0c913db9109a94cba7e51c8e9)\n- Fixed zimuku wrong archives subtitle language identified [#2856](https://github.com/morpheus65535/bazarr/pull/2856)\n- Fixed unhandled exception during backup restoration process. #2850 [2f16ae2](https://github.com/morpheus65535/bazarr/commit/2f16ae2e50fbfae583ffdfe688946cea594239c6)\n- Mods/Fix Uppercase: Fix broken uppercase detection in certain cases [#2852](https://github.com/morpheus65535/bazarr/pull/2852)\n- Added Turkcealtyazi.org provider for Turkish subtitles [8ff5d58](https://github.com/morpheus65535/bazarr/commit/8ff5d5838a2e8bfccacfc6e583556ef06dc8a24c)\n- Added more debug logging to opensubtitles.com provider to help then investigate issues. [6d5ebb0](https://github.com/morpheus65535/bazarr/commit/6d5ebb0faa6d965ebb16cc5d0287c0972e27e197)\n- Fixed UNC path parsing for Radarr sync [d1775ff](https://github.com/morpheus65535/bazarr/commit/d1775ff5c133d0205258b8a6f298495bc83ec862)\n- Fixed upgrade process for translated subtitles. #2817 [e2a3f36](https://github.com/morpheus65535/bazarr/commit/e2a3f363d1e81d1285c75403e46853445f0f9122)\n- Added navigation bar shell scroll support [59d8243](https://github.com/morpheus65535/bazarr/commit/59d8243d0b40b649f612136d333614e17d368127)\n- Added missing settings preventing proper download of subtitles. #2839 [b9a0d9c](https://github.com/morpheus65535/bazarr/commit/b9a0d9c922f4237dc4a6504d7cc42784afe32bc5)\n- Added support for remote DNS query over socks5h proxy. #2839 [bdbe946](https://github.com/morpheus65535/bazarr/commit/bdbe946be25f3f41fba156273eee1f5e24eb0aab)\n- Fixed yavka.net provider by passing all form parameters and improved caching to reduce number of requests [99d9691](https://github.com/morpheus65535/bazarr/commit/99d9691eb1a43d6905b006670dc67cb674a1b5b1)\n- Added scan Plex library option for new files after downloading subtitles [31400c8](https://github.com/morpheus65535/bazarr/commit/31400c89576ca0622e9816fc1a3a430cc76e4b62)\n- Added feature to change \"added\" info in Plex once subtitles are downloaded [fe7b224](https://github.com/morpheus65535/bazarr/commit/fe7b224916c169a58c3f91da8592dd09ae2ff6f7)\n- Updated vendored modules [2fc8f10](https://github.com/morpheus65535/bazarr/commit/2fc8f10a949d4e08c3038eada35d453fe3000b13)\n- Fixed podnapisi TypeError since we've merged #2790. [6fc6ca5](https://github.com/morpheus65535/bazarr/commit/6fc6ca5ec20138d52e1c2637b9f291af7a2c0b7c)\n- Fixed modal with tables application crash [4f77710](https://github.com/morpheus65535/bazarr/commit/4f77710f462ccd8765465b97ca7f5eac334c30cb)\n- Persist pages on url and hydrate on page load [#2826](https://github.com/morpheus65535/bazarr/pull/2826)\n- Added some failsafe to RegieLive provider to try to prevent getting redirected to captcha validation or being completely blocked for a while. #2165 [e17bad6](https://github.com/morpheus65535/bazarr/commit/e17bad6ec49421a315d463522ae40c5c9cd06dc9)\n- Fixed issue with some custom languages subtitles while trying to index them. #2815 [5888011](https://github.com/morpheus65535/bazarr/commit/58880117a9130368acc1eb8f636f0a564a1e123b)\n- Fixed issue with API not returning proper subtitles hi or forced subtitles in some edge cases [e780edd](https://github.com/morpheus65535/bazarr/commit/e780edd0b71924859d187f949d5669a791efe7c1)\n- Removed opensubtitles.org deprecation announcement for VIP users. [9ac6c69](https://github.com/morpheus65535/bazarr/commit/9ac6c69a4f4ee85f36a805c6bc65cd6d6de457dd)\n- Added mass delete subtitle files [#2816](https://github.com/morpheus65535/bazarr/pull/2816)\n- Added TooManyRequests throttling to podnapisi provider. [6a791b2](https://github.com/morpheus65535/bazarr/commit/6a791b2be065521c7437be9313d4232f2b8ec7a6)\n- Improved assrt release name matching by ignoring meaningless values. #2761 [2a038cd](https://github.com/morpheus65535/bazarr/commit/2a038cdc214357f6aae249f7119799fed6f062d7)", "date": "2025-05-11T16:40:55Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.2/bazarr.zip"}, {"name": "v1.5.1", "body": "From newest to oldest:\n- Improved languages profile health check to include default assigned profiles [0413dba](https://github.com/morpheus65535/bazarr/commit/0413dbaa4cf632df0f560f5df2cd67851c3eee02)\n- Fixed sync issue with <PERSON><PERSON> that would remove all movies from database on every sync. [60febe3](https://github.com/morpheus65535/bazarr/commit/60febe3d12b75d534b64d56839ee79ee063958fd)\n- Added check to opensubtitles.com for SDH subtitles being classified as forced [4809b40](https://github.com/morpheus65535/bazarr/commit/4809b403ca3b98f0f38f4e7ef62565b5a40e9aed)\n- Fixed Bazarr not starting when configured IP isn't available for binding. [b71daad](https://github.com/morpheus65535/bazarr/commit/b71daad7fb6a8a0a9d47a29471c24989eabef88c)\n- Improved Sonarr and Radarr syncing to prevent database integrity exception being raised by relying on proper primary keys instead of other values. [8346ea9](https://github.com/morpheus65535/bazarr/commit/8346ea9dc857d6f9bcd2ed3a452e0cbd1c38cfd9)\n- Added opensubtitles specific throttling for server under maintenance (http 506) [2247c55](https://github.com/morpheus65535/bazarr/commit/2247c55bfa9fd38bf3fbe80330a5c37f4988ee6a)", "date": "2025-01-01T16:15:52Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.1/bazarr.zip"}, {"name": "v1.5.0", "body": "From newest to oldest:\n- Reduced throttle from 24 hours to 6 before retrying Opensubtitles.com API on DownloadLimitExceeded [d5a290c](https://github.com/morpheus65535/bazarr/commit/d5a290c7a2bf4c0d7d923bb967d1825de2bbaba3)\n- Fixed download error with Napiprojekt provider [3209355](https://github.com/morpheus65535/bazarr/commit/320935548c28e85bbd9b0b0a6a073f1439deba31)\n- Fixed titrari provider to prevent abuse and throttle properly. #2709 [9d62d84](https://github.com/morpheus65535/bazarr/commit/9d62d84ef590c65e7e9392fe82f1f2022b90bede)\n- Added languages profile creation and assignment to health check. [43d9d43](https://github.com/morpheus65535/bazarr/commit/43d9d43224c84609a4fbd050c8d82e9500743a68)\n- Added series status and last air date [c8e2894](https://github.com/morpheus65535/bazarr/commit/c8e2894b2ba033a11eb52ccd4f50186da74a90d8)\n- Added missing subtitles count in series episodes view [f81780f](https://github.com/morpheus65535/bazarr/commit/f81780fac564080301b06be76dbd33267b8f0ac8)\n- Added official support for Python 3.12 [43a5841](https://github.com/morpheus65535/bazarr/commit/43a5841d04f07455f1f72d22817aa9a342772b91)\n- Fixed improperly guessed video episode number while refining. #2784 [cf559d1](https://github.com/morpheus65535/bazarr/commit/cf559d1028803dad83921f2b61162199bb1fb27f)\n- Fixed podnapisi not returning results [4df822d](https://github.com/morpheus65535/bazarr/commit/4df822d363b448b98000b6c4c6de054d8faa45e4)\n- Added debug logging while refining video object from scene name. #2784 [63c36c8](https://github.com/morpheus65535/bazarr/commit/63c36c8c184a5d7a34892f47e0f16d7b7ce6ea15)\n- Improved Settings wording and organization. [d2dc869](https://github.com/morpheus65535/bazarr/commit/d2dc869c1c90d479a3a39aee264f9d695af2e53c)\n- Fixed EmbeddedSubtitles provider results caching [a7df6a9](https://github.com/morpheus65535/bazarr/commit/a7df6a9c6213cbb16ffc212f22aa3ae4c82599dd)\n- Fixed missing mobile tooltips [137d619](https://github.com/morpheus65535/bazarr/commit/137d61930d6d570515b5d3a4149c19f6206b6aea)\n- Added frontend notification during automatic syncing of subtitles. #2724 [f4ca0f9](https://github.com/morpheus65535/bazarr/commit/f4ca0f9e5586b9b84f137c102e4b1c577b883da8)\n- Added opensubtitlescom provider validation [#2770](https://github.com/morpheus65535/bazarr/pull/2770)\n- Fixed item overview path label overflow [#2780](https://github.com/morpheus65535/bazarr/pull/2780)\n- Fixed match popover position [#2777](https://github.com/morpheus65535/bazarr/pull/2777)\n- Fixed issue with some DB queries and PostgreSQL. [d7f06bb](https://github.com/morpheus65535/bazarr/commit/d7f06bb707e74bd24b89d94dd9486a4c60601738)\n- Improved wording for Embedded Subtitles UI components [4d022c2](https://github.com/morpheus65535/bazarr/commit/4d022c24b60524f4235c49de324a52e97192b881)\n- Added matches dialog alert and column titles [#2771](https://github.com/morpheus65535/bazarr/pull/2771)\n- Fixed a bug introduced in Napiprojekt provider [669ed06](https://github.com/morpheus65535/bazarr/commit/669ed069f52e4f5bb95fa85d431ba2cdb6b57429)\n- Added Napiprojekt provider releases info and a new options to filter subtitles based on uploader [42d569f](https://github.com/morpheus65535/bazarr/commit/42d569faa357d49f4779cf5e9effe9f818eb9cb7)\n- Added validation for incomplete indexing of subtitles or calculation of missing subtitles before searching. [5b3312e](https://github.com/morpheus65535/bazarr/commit/5b3312ea4d88b3c37087da3938e43a021033d07e)\n- Fixed issue introduced in 1.4.6-beta.20 when subtitles upgrade is disabled. #2768 [6fbc912](https://github.com/morpheus65535/bazarr/commit/6fbc91289fe4031a73fb34d34706063a9d45d93b)\n- Refactored upgrade routine to bring logic out of db requests but into Python code. #2749 [24096cb](https://github.com/morpheus65535/bazarr/commit/24096cb10f6aaf3b377bfb24ef255efcdccf89c4)\n- Added switch to control passing of video filename in whisper provider modal [e298d15](https://github.com/morpheus65535/bazarr/commit/e298d157247951beea8578dcd632d80cd3a5d75c)\n- Fixed typos in assrt provider. #1953 [48cdc8b](https://github.com/morpheus65535/bazarr/commit/48cdc8bfc83487b2fa990a5669a0da07a71def54)\n- Fixed issue when whisper returns more than one subtitle for language profiles with more than one language #2758 [d67477a](https://github.com/morpheus65535/bazarr/commit/d67477aded7de5948d2156e2a2e4c83897c08f81)\n- Adjusted search result styles [0d63448](https://github.com/morpheus65535/bazarr/commit/0d6344859b56a533c22df3bfc36a00fbe66c2873)\n- Reverted last commit [db450bd](https://github.com/morpheus65535/bazarr/commit/db450bdcc35536ed1b520ff28430284134a60dfc)\n- Added logging of upgraded subtitles original ID to database [a70b26c](https://github.com/morpheus65535/bazarr/commit/a70b26cdbe520e75966af91f0be96d779bdfee0d)\n- Fixed an issue that prevented Bazarr from starting when PIv6 has been disabled using grub. #2738 [4eb09c5](https://github.com/morpheus65535/bazarr/commit/4eb09c546d4ebb612340de80301fdfc7549843b1)\n- Fixed logging of proxied client requests while authenticating. #2721 [ac1a3c5](https://github.com/morpheus65535/bazarr/commit/ac1a3c5eb07650eb3942163ca3d8f6485a2027b5)\n- Fixed anidb refiner special episodes without offset [#2736](https://github.com/morpheus65535/bazarr/pull/2736)\n- Handle bad JSON results from subsynchro provider #2735 [239ab78](https://github.com/morpheus65535/bazarr/commit/239ab780eb5c3006ce28a45eb5bf2c7eb65d56ad)\n- Improved guessing of video file properties by using file name and refining it with scene name if available. #2704 [c615f99](https://github.com/morpheus65535/bazarr/commit/c615f99f0635ab57e0de1015e4e4575821c074aa)\n- Fixed subdl to not ignore movies subtitles by erroneously check for season packs. #2725 [a186e64](https://github.com/morpheus65535/bazarr/commit/a186e64893fd71d986ef922d9d90cf0b9435ae41)\n- Fixed issue with translation failing when nothing is returned by Google Translate. #2519 [b34a0c1](https://github.com/morpheus65535/bazarr/commit/b34a0c19a21a2caac3fa8cf29cdcfa96717b54fc)\n- Added search poster [#2722](https://github.com/morpheus65535/bazarr/pull/2722)\n- Added support for Spanish (Latino) in opensubtitles.com. #2720 [a1658bb](https://github.com/morpheus65535/bazarr/commit/a1658bbf3271f66295ca8106eabfa2ab842cdb17)\n- Added Anilist error log when the record exists on mapping but doesn't enrich with Anilist id [a9243c6](https://github.com/morpheus65535/bazarr/commit/a9243c6c03fd7d24eb522231cf899d25aa850b66)\n- Added created and updated timestamp to DB for series, episodes and movies [678443f](https://github.com/morpheus65535/bazarr/commit/678443f212cb82b795bd0d7db14c845f641bd804)\n- Fixed RegieLive JSONDecodeError #2727 [092012a](https://github.com/morpheus65535/bazarr/commit/092012a48715fd65184cef5e623bfb60f6d01543)\n- Fixed titulky NameError issue [1874f29](https://github.com/morpheus65535/bazarr/commit/1874f29789aa574c867ff279892db12ae154e6cf)\n- Fixed another issue with subtitles upgrade process. [56d8f2c](https://github.com/morpheus65535/bazarr/commit/56d8f2c9a36536f0d5c8e29b331dc3d01729042f)\n- Fixed issue with subtitles upgrade process on Windows [de9ce4d](https://github.com/morpheus65535/bazarr/commit/de9ce4d13dc0fc2fa25597d9d3eb13bf025dc22a)\n- Fixed subtitles naming when saving subtitles to prevent parsing for HI content if the provider (or the user if it's an upload) specifies that it should be considered as HI. #2719 [5139fca](https://github.com/morpheus65535/bazarr/commit/5139fca5b8f60dd05be76c59296c3388d62d8199)\n- Fixed issue introduced in upgrade process in beta.3 [16499fc](https://github.com/morpheus65535/bazarr/commit/16499fc674b2a6aef7eab0bf07ff95e786c9afbd)\n- Improved provider labeling [9eb9bb5](https://github.com/morpheus65535/bazarr/commit/9eb9bb55c95430400668e4372179eee340b4db24)\n- Fixed subtitrari noi provider issue while downloading some subtitles. #2708 [92107a2](https://github.com/morpheus65535/bazarr/commit/92107a24257b996e28a045ff0e29f37812093a5c)\n- Fixed error while inserting episodes during sync process. #2700 [4b9417c](https://github.com/morpheus65535/bazarr/commit/4b9417c2e280ee42853c56ad14580be4dab48cfe)\n- Fixed subtitles conversion when use original format is enabled and prevented hearing-impaired detection for non srt format subtitles. #2693 [43563cd](https://github.com/morpheus65535/bazarr/commit/43563cdcbd9524803856228352a3ef0d1131717c)\n- Fixed bad non-HI detection with embedded provider when forced subtitles available [f50bcf3](https://github.com/morpheus65535/bazarr/commit/f50bcf34ac3c246d21a69dc3cc2f2eba63314c21)\n- Added the option to skip mismatched fps to Titulky [f296ba5](https://github.com/morpheus65535/bazarr/commit/f296ba5336a58c1108fa6a255a89077bcb23d8b4)\n- Added season pack download feature to titlovi [a4873fc](https://github.com/morpheus65535/bazarr/commit/a4873fc0f5207f4894e59a498a7a50e5bcef7803)\n- Implemented subdivx version resolution for buscar parameter [4f2bcc1](https://github.com/morpheus65535/bazarr/commit/4f2bcc17d9de6e2d53cd43a5c24cc91f2ba90ff1)", "date": "2024-12-24T15:18:44Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.5.0/bazarr.zip"}, {"name": "v1.4.5", "body": "From newest to oldest:\n- When multiple audio streams exist, pick the correct one for whisper to process [#2688](https://github.com/morpheus65535/bazarr/pull/2688)\n- Fixed subtitles translation to Portuguese. #2689 [7000d2a](https://github.com/morpheus65535/bazarr/commit/7000d2ac2c7a52fd0c429c17f5e826e9e3633bfc)\n- Fixed subdivx provider after recent changes to their website. #2686 [a1fac16](https://github.com/morpheus65535/bazarr/commit/a1fac160fbf3f71013ee25566029aa6377097940)\n- Fixed chinese traditional custom language hi extensions [bd3d1e5](https://github.com/morpheus65535/bazarr/commit/bd3d1e56246812d348039e373f4fb96dd8e3a78f)\n- Fixed improper paths history logging for different tools [0200bb9](https://github.com/morpheus65535/bazarr/commit/0200bb96d98127ee32b6b66f8d6b9e21d4571a4d)\n- Added database type, version and migration to System--&gt;Status page. [6297b4b](https://github.com/morpheus65535/bazarr/commit/6297b4b83cbae86cfefb31eb8455fb2a1e83bedf)\n- Fixed database migration issue with postgresql. #2664 [25098e5](https://github.com/morpheus65535/bazarr/commit/25098e5e07533f9eea9602d6a37380aa68a86bbb)\n- Fixed duplicate IDs in languages profile items [c11bdf3](https://github.com/morpheus65535/bazarr/commit/c11bdf34fadd49ea39cc73e471cc9a3075e492f4)\n- Fixed upload subtitle language [#2675](https://github.com/morpheus65535/bazarr/pull/2675)\n- Fixed duplicated search result name for series and movies [#2682](https://github.com/morpheus65535/bazarr/pull/2682)\n- Clear log file contents on Empty log [5e08898](https://github.com/morpheus65535/bazarr/commit/5e08898de82d62ecaf782aa1bb2032ff6304841d)\n- Improved embeddedsubtitles provider by turning on Audio Sync during audio extraction process [c69be93](https://github.com/morpheus65535/bazarr/commit/c69be93cd6bf9cf4ce6339052e938a3c5420e31f)\n- Fixed SyntaxWarning in subzero language enforced in Python 3.12. #2656 [14a361d](https://github.com/morpheus65535/bazarr/commit/14a361dd97bb89204c748f60ef6052544d992dac)\n- Removed unnecessary vendored argparse module since it's included in Python since 3.2 [384a754](https://github.com/morpheus65535/bazarr/commit/384a754f9ef93ec51ce59fa64d0423cd66d4560f)\n- Fixed logging filter issue. [90b4480](https://github.com/morpheus65535/bazarr/commit/90b44802dc3be1429b5d41eb4ad4603c80f1ce80)\n- Fixed logging filter bug introduced in 1.4.4 [cc7a800](https://github.com/morpheus65535/bazarr/commit/cc7a8000e74c09ac2a747cf303ff86a48f37ec1e)", "date": "2024-10-02T01:15:01Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.4.5/bazarr.zip"}, {"name": "v1.4.4", "body": "From newest to oldest:\n- Added support for Legendas.net provider [b8aa2a8](https://github.com/morpheus65535/bazarr/commit/b8aa2a8b1af42dff9539c6e190bd2e6a7a7fd0ca)\n- Modified some language names to match those in Sonarr and Radarr. #2650 [6d062f3](https://github.com/morpheus65535/bazarr/commit/6d062f35009db472e58e58fb9a05f463fbbe49d7)\n- Fixed async audio languages not mapped fallback [#2652](https://github.com/morpheus65535/bazarr/pull/2652)\n- Fixed series indexing when malformed alternate title is returned by Sonarr [6ddfdfe](https://github.com/morpheus65535/bazarr/commit/6ddfdfee6a64845fc0eab21b18ac56ad70d3e1f9)\n- Fixed podnapisi AttributeError [f6c5ee6](https://github.com/morpheus65535/bazarr/commit/f6c5ee6bfcbe28c77380b417562ba4deba2c658a)\n- Added support for binding IPv4 AND IPv6 instead of just either one or the other [4e365c6](https://github.com/morpheus65535/bazarr/commit/4e365c6aa6f7e0786d8ceefdaa91099da59431f1)\n- Made all subf2m test pass [#2645](https://github.com/morpheus65535/bazarr/pull/2645)\n- Fixed anidb refiner empty mapping episode reference not skipped [deae4e5](https://github.com/morpheus65535/bazarr/commit/deae4e52f0eabda1a6014bafcacf3ec121b8b430)\n- Fixed zimuku provider skipping non lowercase subtitles [#2644](https://github.com/morpheus65535/bazarr/pull/2644)\n- Fixed cutoff language not being properly handled. #2635 [5f7e1f6](https://github.com/morpheus65535/bazarr/commit/5f7e1f68c706a2f71dca0ed8b6763416d51342bb)\n- Fixed timeout while querying subdivx provider. #2633 [cfa9004](https://github.com/morpheus65535/bazarr/commit/cfa900404076ac440a3a5745279b0db85f5e85b4)\n- Added ability to remove language profiles based on tag values [e5f1528](https://github.com/morpheus65535/bazarr/commit/e5f1528bbad2d0df8d0ed32b16cfa6856db79f2c)\n- Fixed Podnapisi not returning any subtitles [74f2c66](https://github.com/morpheus65535/bazarr/commit/74f2c6646404b76821146e9b58bed12c53fc1186)\n- Added sonarr and radarr settings tags validation [#2634](https://github.com/morpheus65535/bazarr/pull/2634)\n- Fixed subf2m search process by stripping html title result before using regex [855da6b](https://github.com/morpheus65535/bazarr/commit/855da6b1bb1cbb41d687e4593e2e9d092ed6e273)\n- Fixed chinese audio language [#2632](https://github.com/morpheus65535/bazarr/pull/2632)\n- Fixed badge overflow [9ec6027](https://github.com/morpheus65535/bazarr/commit/9ec60279896420f80916687d29530025f16ba0a8)\n- Fixed hearing-impaired detection using regex when language is Arabic and parenthesis are included in text. #2612 [eb084ab](https://github.com/morpheus65535/bazarr/commit/eb084abc21b3d37390d6ec70d73e62297032f7c3)\n- Refactored the translation routine to prevent Google Translate from messing with subtitles sequence by sending line by line (slower but better). #2558 [609349b](https://github.com/morpheus65535/bazarr/commit/609349b4002290e771935c7e6d02263c3fdd7ce4)\n- Fixed provider AvistaZ & CinemaZ optional subtitle Uploader [#2628](https://github.com/morpheus65535/bazarr/pull/2628)\n- Added settings provider maximum description lines [#2611](https://github.com/morpheus65535/bazarr/pull/2611)\n- Removed raising of OSError during subsync [#2584](https://github.com/morpheus65535/bazarr/pull/2584)\n- Fixed table items losing order when action is executed [1ec415f](https://github.com/morpheus65535/bazarr/commit/1ec415f0c5d0d9d72555778d1c5f564271f0ea3d)\n- Fixed AvistaZ and CinemaZ scraping when the Reseed notification is displayed [0fc334f](https://github.com/morpheus65535/bazarr/commit/0fc334f731ddda1d70fb4b0ad6fc7c0329c76048)\n- Added jimaku provider [#2505](https://github.com/morpheus65535/bazarr/pull/2505)\n- Fix for provider subtitle language being unintentionally modified [5582cc0](https://github.com/morpheus65535/bazarr/commit/5582cc076dd58a63638d96cb0dbc5119554fe127)\n- Fixed postgresql database migrations not applied [#2601](https://github.com/morpheus65535/bazarr/pull/2601)\n- Fixed get movie monitored status [#2591](https://github.com/morpheus65535/bazarr/pull/2591)\n- SuperSubtitles: handle KeyError for movie searches [4a34186](https://github.com/morpheus65535/bazarr/commit/4a341869858103a60dccf712e70e6ad97f311a29)\n- Subdl Provider: avoid raising exception on no results [8fe8eaf](https://github.com/morpheus65535/bazarr/commit/8fe8eaf7dad6b1eccde9d0622e946e15acc43688)\n- Fixed anidb refinement for sonarr specials [#2590](https://github.com/morpheus65535/bazarr/pull/2590)\n- Added new feature: Tag-Based Automatic Language Profile Selection [b304f6f](https://github.com/morpheus65535/bazarr/commit/b304f6f1efecdfa5b258138029b54460267e8032)\n- Fixed anidb refiner episode not found [#2581](https://github.com/morpheus65535/bazarr/pull/2581)\n- HDBits provider: handle KeyError (common exception) [b419593](https://github.com/morpheus65535/bazarr/commit/b4195934c1fd5ff97acf07d7fbd82f52e272c520)\n- Subdl Provider: add tests and use standard utils [91a3531](https://github.com/morpheus65535/bazarr/commit/91a35317cc1f627030b7bae4d9776acef4745afd)\n- Fixed cached logged out index page [127a7ae](https://github.com/morpheus65535/bazarr/commit/127a7aebadde462babba33d2115dc058a854954c)\n- Added series empty subtitle episodes progress bar labels [#2575](https://github.com/morpheus65535/bazarr/pull/2575)\n- Fixed issue with soustitreseu when guessit is unable to guess episode or season from release name. #2569 [ebf3471](https://github.com/morpheus65535/bazarr/commit/ebf3471eec3895ba06c5be3dfe1fb7efb7622100)\n- Fixed animetosho empty language fallback [#2571](https://github.com/morpheus65535/bazarr/pull/2571)\n- Fixed popover text wrap browser compatibility [#2573](https://github.com/morpheus65535/bazarr/pull/2573)\n- Improved subdl provider to filter out non SRT or ASS subtitles [e3a3ef1](https://github.com/morpheus65535/bazarr/commit/e3a3ef1e931ec6124f59941e14d6544be3a9e268)\n- Update fese (extractor) (cover #2538) [aafaf1c](https://github.com/morpheus65535/bazarr/commit/aafaf1cbf1ac29249960f2d5f2c7ba0608a5c284)\n- Fixed pt-BR issue with subdl provider. [a15b8d5](https://github.com/morpheus65535/bazarr/commit/a15b8d560252a953999976343b7665c533570365)\n- Fixed pwa assets files not served [#2568](https://github.com/morpheus65535/bazarr/pull/2568)\n- Fixed PWA image path on development build [5886c20](https://github.com/morpheus65535/bazarr/commit/5886c20c9c7929bf46836a99c2d9d4eb834638bd)\n- Added subdl provider initial implementation [40985fd](https://github.com/morpheus65535/bazarr/commit/40985fdee3bdfd722d160f04621a6294732a49d0)\n- Fixed overview poster wrap [#2567](https://github.com/morpheus65535/bazarr/pull/2567)\n- Fixed PWA not registered [#2566](https://github.com/morpheus65535/bazarr/pull/2566)\n- Updated tanstack table to v8.x [#2564](https://github.com/morpheus65535/bazarr/pull/2564)\n- Upgraded knowit to latest version to fix some long-lasting issue. [22ac838](https://github.com/morpheus65535/bazarr/commit/22ac838e9b60b0b7c0509d95f44843e5ce4abf48)\n- Fixed the subtitles upgrade process loop for normal subtitles upgraded to HI subtitles. [a4527a7](https://github.com/morpheus65535/bazarr/commit/a4527a7942fca4c0fe28ec5a2cdad56ee569800c)\n- Added Anidb integration soft rate limits [#2556](https://github.com/morpheus65535/bazarr/pull/2556)\n- Fixed file traversal via path filename vulnerability in swaggerui static route. #2559 [7b7e984](https://github.com/morpheus65535/bazarr/commit/7b7e984bff26f4d91bfec3dfdacedcd94c35d0cf)\n- Fix original format requests (#2514) [ad88ec3](https://github.com/morpheus65535/bazarr/commit/ad88ec37677b82a911acf35c1280b49b679beaa7)\n- Fixed uptime calculation overflowing hours [34089b0](https://github.com/morpheus65535/bazarr/commit/34089b0fd7915f61d7b0bdfd41a65c3d938bb380)\n- Fixed usage of pysubs2 removed exception in subliminal_patch [668ec38](https://github.com/morpheus65535/bazarr/commit/668ec386fc6eb2da53a68b3aaf8744ae364aaa97)\n- Fixed frontend sync and translate missing hi and forced information [26ce9d7](https://github.com/morpheus65535/bazarr/commit/26ce9d73e6b6c4cc1e219deba722c0eaab4b3187)\n- Fixed issue with subtitles translation failing. #2519 [9049ab2](https://github.com/morpheus65535/bazarr/commit/9049ab2238c46355e6896ed07ebf4e66442a91af)\n- Fixed HI or Forced status not being properly saved to history when syncing or translating a subtitles. [dcbd813](https://github.com/morpheus65535/bazarr/commit/dcbd8130dba8f0568173f61a9614e30e24e9c54e)\n- Fixed opensubtitles.com provider not being throttled properly when server returned http 500. [76175a8](https://github.com/morpheus65535/bazarr/commit/76175a889bb392d5c49174929f5a707d558d38f3)\n- Subtitle class: get format dinamically (fix #2514)\n- Fixed legendasdivx provider while searching for movies subtitles [9a52b3c](https://github.com/morpheus65535/bazarr/commit/9a52b3c539b3f32841f45177a949b001c51abf39)\n- Fixed usage of path mappings for video_analyzer operations [55d96b3](https://github.com/morpheus65535/bazarr/commit/55d96b3231ba78e2a5c0ae8a7df103b53f42b6c5)\n- Fixed unhandled exception in opensubtitles.com provider login method. #2545 [fb83ec4](https://github.com/morpheus65535/bazarr/commit/fb83ec4081484b046664c6fc3a21a47d6542519f)\n- Improved video metadata parser function and documented params and return values. [35b65dc](https://github.com/morpheus65535/bazarr/commit/35b65dcd4efba0e48b113b8a4f63bca031548b9d)\n- Fixed corrupted hi value in languages profile after reverting back Non-HI only feature. [1f843c7](https://github.com/morpheus65535/bazarr/commit/1f843c7d15ca2d1da50f0d4614491e3187809ace)\n- Reverted normal only (non-hi) option to languages profile. Will be properly reintroduced later. [5ad3926](https://github.com/morpheus65535/bazarr/commit/5ad392630649c9dd53c33794d51e51f8b9c8fa09)\n- Fixed theme colors and variants [d719d4c](https://github.com/morpheus65535/bazarr/commit/d719d4c6df0c8d8dbc71e29aabf59f5d0987ac18)\n- Fixed mass edit language clear not available [d7445bf](https://github.com/morpheus65535/bazarr/commit/d7445bf39c2a6b4365064087c75c39972b22469d)\n- Fixed background color of media overview badge on light theme [9787934](https://github.com/morpheus65535/bazarr/commit/9787934820f135debfde43f9898d790a3cd817d9)\n- Added submission on select to search box [c4f5511](https://github.com/morpheus65535/bazarr/commit/c4f5511915ad5c60c380d9ee8b2c0531bf2b6969)\n- Fixed settings profile style button light theme [ff8fd8c](https://github.com/morpheus65535/bazarr/commit/ff8fd8c9a43efea33a77d2e13b90a674672166ab)\n- Improved mass edit profile in chunks instead of at once [5442849](https://github.com/morpheus65535/bazarr/commit/54428495b9d7b68ea4caf82892dcc4c015cd46b2)\n- Upgraded react-query to v5 [4d3c1f4](https://github.com/morpheus65535/bazarr/commit/4d3c1f4b9d517ef3f64e489d011b939bc816d228)\n- Added normal only (non-hi) option to languages profile [eee8659](https://github.com/morpheus65535/bazarr/commit/eee8659ce16841df634a3960604f9b50d4c5e8c2)\n- Fixed episode history modal auto scrolling to top [d75c198](https://github.com/morpheus65535/bazarr/commit/d75c198c6c81ce85b0b7e8d6cbc664f70778ce63)\n- Fixed adjust time button overflow [854c43c](https://github.com/morpheus65535/bazarr/commit/854c43c53a737fb75e5d8f1ce35130690b8d69d2)\n- Fixed minor style and colors broken on Mantine v7 update [0a0762b](https://github.com/morpheus65535/bazarr/commit/0a0762b1cf338affd0565184ce1e28fb70470eda)\n- Updated pysubs2 to 1.7.2 [2b19f39](https://github.com/morpheus65535/bazarr/commit/2b19f390e72ee6368b6b250a829ec856ae073689)\n- Fixed provider legendasdivx unable to find series subtitles. #2509 [a8c1794](https://github.com/morpheus65535/bazarr/commit/a8c17940610808eba46b07048e59658ef5d3fe41)\n- Upgraded mantine to v7.x [be8f2d6](https://github.com/morpheus65535/bazarr/commit/be8f2d6d183e5206f932be1113e0da7db2a2277a)\n- Fixed external subtitles indexing on some platforms where filesystem encoding may be using a different UTF8 normalization form. [bb8233b](https://github.com/morpheus65535/bazarr/commit/bb8233b599fa0bd8133b092897a4fbcfd736c8cd)", "date": "2024-09-15T19:25:56Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.4.4/bazarr.zip"}, {"name": "v1.4.3", "body": "From newest to oldest:\n- Fixed throttled_providers.dat reset [77302fa](https://github.com/morpheus65535/bazarr/commit/77302fad218a3c14c91c3d28074f30e02ffe9b77)\n- Fixed bazarr restart traceback exception [b7e6de7](https://github.com/morpheus65535/bazarr/commit/b7e6de71ffe977a5b2fc71d3b61545226af83395)\n- Fix for case insensitive filesystem upates [8842004](https://github.com/morpheus65535/bazarr/commit/884200441bec801eba56a4ac08328f8227ad3bed)\n- Fixed subdivx series search process. #2499 [0e183c4](https://github.com/morpheus65535/bazarr/commit/0e183c428b1509e4cde77c53c4a47a6393c7a54e)\n- Reverted to apprise 1.7.6 to fix an issue with the upgrade process first. 1.8.0 will get back in nightly shortly. #2497 [5ca733e](https://github.com/morpheus65535/bazarr/commit/5ca733eac0ec43ebd3ca68e867bfd6ef0fb30cc2)\n- Fixed upgrade process that was broken since Apprise 1.8.0 update. #2497 [3e929d8](https://github.com/morpheus65535/bazarr/commit/3e929d8ef90fcb77bba0abeb4662d4d5e2882e6a)\n- Fixed uppercase issue in Apprise module name. [d70a92e](https://github.com/morpheus65535/bazarr/commit/d70a92e9476e61b12a79c5642629ed81c9ad703f)\n- Fixed issue while saving some odd case ASS embedded subtitles. [b3a5d43](https://github.com/morpheus65535/bazarr/commit/b3a5d43a10befd2451711a1abbafe4f5c65f3c76)\n- Emergency fix following Apprise 1.8.0 upgrade [fd0a8c3](https://github.com/morpheus65535/bazarr/commit/fd0a8c3d3bd1beadb1fed1a58013e386d3f29653)\n- Updated apprise to 1.8.0 [86d3403](https://github.com/morpheus65535/bazarr/commit/86d34039a35387e33663f14b30a65cc1165b4fc7)\n- Fixed issue with subssabbz provider comparing None with int. [006ee0f](https://github.com/morpheus65535/bazarr/commit/006ee0f63ac39dc1e73c761a161aacfc6d62b380)\n- Fixed issue with subsunacs provider comparing None with int. [47011f4](https://github.com/morpheus65535/bazarr/commit/47011f429a57a8b214681ce6527b4f49eae0cd90)\n- Removed closed subscene provider [bb4b01f](https://github.com/morpheus65535/bazarr/commit/bb4b01f3fb1f23ce7a5bf4fc5e2fbf89d302da16)\n- Removed dependency over moment library [5b5bead](https://github.com/morpheus65535/bazarr/commit/5b5beadf4d49954eb4ac95659ab470915002647d)\n- Removed dependency over moment [6e34225](https://github.com/morpheus65535/bazarr/commit/6e3422524c852c6c4e443c3116c3011639c3f96b)\n- Fixed db migrations dropping tables content because of ForeignKey constraints. #2489 [4815313](https://github.com/morpheus65535/bazarr/commit/4815313ac6f36154e6e59b0ee3ca87c04a36bb7c)\n- Added a database migration to get past the issues with incomplete table_languages_profiles. ##2485 [5630c44](https://github.com/morpheus65535/bazarr/commit/5630c441b04478c38483d7f6c228a36b7cf91408)\n- Added animetosho release info [970b0f9](https://github.com/morpheus65535/bazarr/commit/970b0f9d4769d47e325fb01441abbc14996d7262)\n- Fixed HI subtitles identification when downloading and improved some constants. #2386 [2c4ed03](https://github.com/morpheus65535/bazarr/commit/2c4ed03817c724463701bf313f7fcb7b48039f81)\n- Fixed embedded ASS subtitles writing encoding error [bea2f0b](https://github.com/morpheus65535/bazarr/commit/bea2f0b781ba4b1f5bd7dea2644854a141b3884d)\n- Added timeout to update check API call [ad151ff](https://github.com/morpheus65535/bazarr/commit/ad151ff1393656f71d9c9558d8d86c1d23a1429c)\n- Fixed Animetosho provider error for tv shows [2782551](https://github.com/morpheus65535/bazarr/commit/2782551c9bd5418e6fe0484a4002b4a350869917)\n- Improved whisper provider to not throttle when unsupported audio language is encountered. #2474 [5749971](https://github.com/morpheus65535/bazarr/commit/5749971d67b7fa7932a8c707f50732a22615a37f)\n- Improved cutoff options label. #2466 [86b889d](https://github.com/morpheus65535/bazarr/commit/86b889d3b6a2c36a97f2dbf83a984311b53048ca)\n- Fixed animetosho provider empty subtitle name. #2468 [5e04338](https://github.com/morpheus65535/bazarr/commit/5e0433834e16dfbc1c7184fd2116b2d7a79db631)\n- Fixed SyntaxWarning with Python 3.12. #2462 [fd190aa](https://github.com/morpheus65535/bazarr/commit/fd190aad143a01a83e13dcf03b82bb34ddb8d2fc)\n- Embedded Subtitles provider: handle FileNotFoundError [369b2c7](https://github.com/morpheus65535/bazarr/commit/369b2c73439d15c5e9c43526b239e36329c82329)\n- Fixed Anidb refinement for not anime episodes. #2463 [a2fee0e](https://github.com/morpheus65535/bazarr/commit/a2fee0e1e416bb45574cac9841f96c2e2cfe0303)\n- Added minimal Python 3.12 compatibility. Not yet official support. [6dbe143](https://github.com/morpheus65535/bazarr/commit/6dbe1433644b1bc35a631e2f3758cfe8ddcc3b0b)\n- Additional fix for restart process. #2456 [abc4500](https://github.com/morpheus65535/bazarr/commit/abc4500443830c5b175a51790b0def38b1a719f7)\n- Added additional languages to animetosho provider [7322486](https://github.com/morpheus65535/bazarr/commit/73224866cbeb2e2f30277118b5af0f0d1d9daf8a)\n- Fixed upgrade process to properly upgrade bazarr.py when it's updated. #2456 [a39d874](https://github.com/morpheus65535/bazarr/commit/a39d874d3b44d8c3a4f4f030fd4c2c9ac4c20cf5)\n- Fixed mass editor returning a 413 error with a large series/movies set. [e4bc792](https://github.com/morpheus65535/bazarr/commit/e4bc792ee0fba64a3a20d817bbf2c3e2ba66b737)\n- Fixed Swagger UI broken since last libraries update (1.4.3-beta) [a8c3528](https://github.com/morpheus65535/bazarr/commit/a8c352854fc70717de54823f4ce185990be5b8f7)\n- Fixed subtitle toolbox overlap [af89384](https://github.com/morpheus65535/bazarr/commit/af893847c618a5c531038a825aa2687c972b2117)\n- Updated apprise to version 1.7.6 [7578b8e](https://github.com/morpheus65535/bazarr/commit/7578b8ef146431a74f96129b9bcde2388bb90bbe)\n- Fixed manual upload table long names without spacing. #2448 [b2d807d](https://github.com/morpheus65535/bazarr/commit/b2d807d9d9fb98c7cdf79e5452201589e1bb9198)\n- Added animetosho provider [77ebd03](https://github.com/morpheus65535/bazarr/commit/77ebd036f210ec30529ea5419d7a91ac3c0ece13)\n- Improved best subtitles logging when score is below minimum score. [3c30492](https://github.com/morpheus65535/bazarr/commit/3c30492e71d716dbced8bbdbc7cd004b064a65b4)\n- Upgraded some frontend dependencies [6fc4b41](https://github.com/morpheus65535/bazarr/commit/6fc4b4152691e29e49031e29e2adde97f222c571)\n- Fixed restart loop when TCP port already in use. [3e9cfea](https://github.com/morpheus65535/bazarr/commit/3e9cfea3c5d7bc856d2956ab7d095c90eeca98a8)\n- Fixed improper redirection from login page when base_url isn't empty. [ad16acb](https://github.com/morpheus65535/bazarr/commit/ad16acb88f0d9e4eb0b4d7ba88add7380fe5cd42)\n- Fixed login page getting called even if authentication is disabled. #2441 [4d11b95](https://github.com/morpheus65535/bazarr/commit/4d11b9580cc1d175a93204a3067d2561f3f91f66)\n- Fixed betaseries provider when series doesn't exist. #2431 [b4071f0](https://github.com/morpheus65535/bazarr/commit/b4071f0af6d73976680bbbcb2655259ca000200e)\n- Improved multiple exceptions catching and fixed some other providers issues [ec85f6e](https://github.com/morpheus65535/bazarr/commit/ec85f6e172cc004138d875f1980dd8d06dffa7a2)\n- Fixed overflowing of episode titles in wanted view. #2419 [a9f438b](https://github.com/morpheus65535/bazarr/commit/a9f438b5483bc9cf427d718f12a8acad188c562f)\n- Fixed and improved UI while correcting text [814b1af](https://github.com/morpheus65535/bazarr/commit/814b1af79f03a31946ccecc95b5b399a85fd712a)\n- Updated other React dependencies. [2b92752](https://github.com/morpheus65535/bazarr/commit/2b9275244bd913260c8755342f407a7b0ab95c3e)\n- no  log: Bump actions/cache from 3 to 4 [#2428](https://github.com/morpheus65535/bazarr/pull/2428)\n- Updated apprise to latest version to prevent deadlocks issue in 1.7.3. [aedf2d4](https://github.com/morpheus65535/bazarr/commit/aedf2d4d894b1ccbafbf6e418e9753865170edd5)\n- Rolled back cloudscraper to fix captcha v1 solving issue. [213a044](https://github.com/morpheus65535/bazarr/commit/213a04405d6d02e3349387d1cfe8dcd25ed7e64e)\n- Updated fese module to latest version to fix embedded subtitles provider. #2423 [1428edf](https://github.com/morpheus65535/bazarr/commit/1428edfb8bde0abfd3882dfe2e27e3ca872795e8)\n- Added Test Connection button for whisper provider [afa529c](https://github.com/morpheus65535/bazarr/commit/afa529c4b345fdd0331123f106fa76fa726d3032)\n- Fixed unbound local variable if 0 movies in database. #2417 [345b6b3](https://github.com/morpheus65535/bazarr/commit/345b6b37185a4b04534ac53eb5f7294f1937ea3a)\n- Added Weekly Sync option for Radarr and Sonarr [33f82fe](https://github.com/morpheus65535/bazarr/commit/33f82fe44568888b086dbda128f3b230a189a1e7)\n- Updated multiple Python modules (now in libs and custom_libs directories) and React libraries [03afeb3](https://github.com/morpheus65535/bazarr/commit/03afeb347075381bcb7fd6036295c9fa4a90d2dc)\n- Refactored Shutdown, Restart and exit status codes [9ae6842](https://github.com/morpheus65535/bazarr/commit/9ae684240b5bdd40a870d8122f0e380f8d03a187)\n- Allow numeric passwords for all providers. #2416 [c455345](https://github.com/morpheus65535/bazarr/commit/c4553452a5d6548acb842fd9f1912cdea2f37773)\n- Fixed subtitles synchronization process when settings values have changed since Bazarr started [5d87b10](https://github.com/morpheus65535/bazarr/commit/5d87b1047536efb42ee45d53a1ea85a0d536157d)\n- Prettier version? [16a3158](https://github.com/morpheus65535/bazarr/commit/16a3158f5ed4dd3100177a558a81d08ca663c640)\n- Text updates, especially for thresholds [3827ea6](https://github.com/morpheus65535/bazarr/commit/3827ea6ffeae6ad99b0e46dd63e636a9ad4a687d)\n- Added Progressive Web App support [731e44c](https://github.com/morpheus65535/bazarr/commit/731e44cb9a3c6198acc7056210ffe01364e4b036)\n- Include server URL in Yify subtitle page link [b24ee30](https://github.com/morpheus65535/bazarr/commit/b24ee309ed0e781bf6e11435de7b5cf15559e96d)\n- Disabled autoscroll to top for underlying window after manual search. #2285 [f95db43](https://github.com/morpheus65535/bazarr/commit/f95db43a2fc7d485ed5c2216e63884df9ad8ec14)\n- Fixed subtitles sync function to preserve subtitles file extension. #2399 [f71b893](https://github.com/morpheus65535/bazarr/commit/f71b8931e34729e0cca17195b574333c7f32620a)\n- Subdivx Provider: major updates [d9629df](https://github.com/morpheus65535/bazarr/commit/d9629df3afccaaf8ff14550769cc175313fce3c8)\n- Modified upgrade task so it always shows in UI even if it's set to manually in scheduler [6c48806](https://github.com/morpheus65535/bazarr/commit/6c488063e73b401c60c71a5a4b57a045de2bdc36)\n- Reduced debounce value for event notifications [e3354aa](https://github.com/morpheus65535/bazarr/commit/e3354aa7f3419448328558e1844a461318734ff2)\n- Added originalFormat db migration from bool to int only for Postgresql [2eec913](https://github.com/morpheus65535/bazarr/commit/2eec9131f85eba7976539ca7a48f43afdf3512ae)\n- Added log level/filter settings to Logs page [d5466ff](https://github.com/morpheus65535/bazarr/commit/d5466fff2304e863e62fe2dfc62d9e88bdeb4068)\n- Updated fallback language for embeddedsubtitles provider to be able to specify the one to use [ad8f116](https://github.com/morpheus65535/bazarr/commit/ad8f116c78688ab038e834a8ad3a3b4c7bfee0c6)", "date": "2024-06-02T14:20:47Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.4.3/bazarr.zip"}, {"name": "v1.4.2", "body": "From newest to oldest:\n- Fixed subdivx issue with foreign title encoding issue. #2395 [43d313a](https://github.com/morpheus65535/bazarr/commit/43d313a31b058fbdebd4085000268e6dc3a3c03b)\n- Fixed subdivx provider to use alternative titles. #2378 [b96965e](https://github.com/morpheus65535/bazarr/commit/b96965e74030f3c9abb93f4e087cce8ec2fac8fd)\n- Updated titrari provider [2e124d2](https://github.com/morpheus65535/bazarr/commit/2e124d266635cf2ef7ff77c731b20ace328f09c2)\n- Fixed saving of null anti-captcha provider that caused Validator to reset default value. [faa4dfb](https://github.com/morpheus65535/bazarr/commit/faa4dfb77c6e19c2d6e470b3f58fed76a3878a54)\n- Fixed indentation issue in pool update. #2390 [4ca2111](https://github.com/morpheus65535/bazarr/commit/4ca211191d6fe546abc084bbad4b8b5d327e733b)\n- Added support for opensubtitles.com dedicated VIP subdomain that get automatically used according to subscription status. [95474b2](https://github.com/morpheus65535/bazarr/commit/95474b29db452a7dad055af6d89a671ff50ae2d4)\n- Fixed provider pool update on automatic search. #2379 [88c267f](https://github.com/morpheus65535/bazarr/commit/88c267f8480bedd6319db2620a74c13f5fe2595b)\n- Modify portuguese custom language [9058335](https://github.com/morpheus65535/bazarr/commit/90583354bf06532e7ae9881cfdfb52cba75424bf)\n- Revert \"Fixed podnapisi results parsing\" [90e4cf1](https://github.com/morpheus65535/bazarr/commit/90e4cf1c6fa17ea179fa808025b7e23b1e106201)\n- Fixed podnapisi results parsing [27d7f96](https://github.com/morpheus65535/bazarr/commit/27d7f96599b0bf825052a146a0cb5def833f7ce9)\n- Fixed Bazarr not starting when Sonarr or Radarr isn't running. [12f1aa6](https://github.com/morpheus65535/bazarr/commit/12f1aa6acf4faf0b5621743423ce5ff3f1e3edf6)\n- Fixed opensubtitles.com filtering AI translated subtitles even when they are requested by user. #2381 [5402c2a](https://github.com/morpheus65535/bazarr/commit/5402c2aaaf111685ec5b3e2ad7023beccd096f16)", "date": "2024-02-20T00:28:55Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.4.2/bazarr.zip"}, {"name": "v1.4.1", "body": "From newest to oldest:\n- Fixed forced subtitles download loop [4029c9f](https://github.com/morpheus65535/bazarr/commit/4029c9f712b7718eca89c5dd2e4d29c3f5b2762e)\n- Fixed debug logging of opensubtitlescom provider [fb660a0](https://github.com/morpheus65535/bazarr/commit/fb660a0e6ea4d45c9cab0a49008d4e65d31b355c)\n- Fixed movies indexing issue [e6b9b32](https://github.com/morpheus65535/bazarr/commit/e6b9b327f2723f3d2cc24c9ef0acb256e970f2da)\n- Added separate values to whisperai provider for connection and read timeouts [938f6df](https://github.com/morpheus65535/bazarr/commit/938f6df386c0f50568f7a6c04ed4ae58e440af04)\n- Embedded Subtitles Provider: handle KeyError for cached paths [cdf7296](https://github.com/morpheus65535/bazarr/commit/cdf7296dd41fe6fb46d7abe49c684e03680a278c)\n- Add Argenteam Dump Provider [deef13d](https://github.com/morpheus65535/bazarr/commit/deef13d11b9310384d71cb0a4af230a524541359)\n- Fixed unhandled FileNotFoundError during restore process [d758c53](https://github.com/morpheus65535/bazarr/commit/d758c53b412ab8bcd5927beba13ff5a56b266c17)\n- Added Sync Optimization for Movies, Series and Episodes to ignore unmonitored items [990448b](https://github.com/morpheus65535/bazarr/commit/990448b06e6df9f2728d98a9a14df5f2b378b00a)\n- Fixed improper API call on removal of all blacklist items [3922d52](https://github.com/morpheus65535/bazarr/commit/3922d52d5cfa6fd3da2aca2f9f2e5bda61091dd4)\n- Fixed single blacklist item deletion issue that would remove all the blacklisted subtitles from the same provider at once. [c45a2ac](https://github.com/morpheus65535/bazarr/commit/c45a2ac43cad30ab6aa45f7cfd90afba16cdbdd3)\n- Fixed wizdom provider to handle Zip files with more than one (up to two entries). #2351 [345408d](https://github.com/morpheus65535/bazarr/commit/345408d692b6cd6917a0a0db786c1b0af3ed809c)\n- Fixed improper passing of hi and forced flags when searching a subtitles manually. #2350 [e17865a](https://github.com/morpheus65535/bazarr/commit/e17865ad535da06caddb25cfff30961cecae20f1)\n- Fixed titlovi provider not being properly throttled and resulting in account locking. #2062 [828ac34](https://github.com/morpheus65535/bazarr/commit/828ac34074c35778ac47ce7df78c362def60de77)\n- Fixed issue introduced while merging the new sync feature. #2356 [e4ebc64](https://github.com/morpheus65535/bazarr/commit/e4ebc64ca95b4a18b7a1909a5cef517c0715d74d)\n- Removed aRGENTeaM provider that have been recently closed. #2352 [783e6b3](https://github.com/morpheus65535/bazarr/commit/783e6b38ea5be62ebe9c001a9736a19486b269e6)\n- Improved subtitles synchronisation settings and added a manual sync modal [0e648b5](https://github.com/morpheus65535/bazarr/commit/0e648b5588c7d8675238b1ceb2e04a29e23d8fb1)\n- Fixed improper closing of resources on exit [0807bd9](https://github.com/morpheus65535/bazarr/commit/0807bd99b956ee3abf18acc3bec43a87fc8b1530)\n- Added additional variables for custom post processing [0ac9ece](https://github.com/morpheus65535/bazarr/commit/0ac9ecea6e7bae6828441ac3c8eece58f68c88fa)\n- Fixed some Whisper issues and added logging level selector for this specific provider [c0bbd4f](https://github.com/morpheus65535/bazarr/commit/c0bbd4f150c6a0a48cba5c3d028299e0eec1d316)\n- Updated logging to use ISO 8601 date format [549bebc](https://github.com/morpheus65535/bazarr/commit/549bebcc43102efaace1920bef92cc59032e2e7d)\n- Fixed betaseries parsing issue [902d1e6](https://github.com/morpheus65535/bazarr/commit/902d1e62b8251c36a622f96bc40d6ef9f052997e)\n- Fixed podnapisi search results parsing error [b11f810](https://github.com/morpheus65535/bazarr/commit/b11f8100ac81f07727b19e87da26defdbbeb55a0)\n- Added settings to exclude subtitles from specific provider to be synced [#2340](https://github.com/morpheus65535/bazarr/pull/2340)\n- Improved error handling for betaseries provider. [cb71b11](https://github.com/morpheus65535/bazarr/commit/cb71b11fd2332a5175e9cb4d87cd26faf98d1f42)\n- Increased supersubtitles connection timeout and properly deal with invalid JSON returned from their API. [9379d1b](https://github.com/morpheus65535/bazarr/commit/9379d1b7f843e46b16483a0d54a17b4453b21590)\n- Increased podnapisi connection timeout to prevent provider throttling when their servers are slower than usual. [6b304e0](https://github.com/morpheus65535/bazarr/commit/6b304e0ce778a4abfd1ede216cf35875fc76d3d6)\n- Added opensubtitlescom settings to include AI translated subtitles in search results. [5739b9a](https://github.com/morpheus65535/bazarr/commit/5739b9ad08b6c0c7eef14aad21863c8a08a88852)\n- Added filter to prevent useless Session is disconnected warning in logs [9e75acd](https://github.com/morpheus65535/bazarr/commit/9e75acd5493791879a3037a77c89ccddcb3844f4)\n- Added db migration version to create the missing monitored column in TableShows table of some old instances. [7c40bfe](https://github.com/morpheus65535/bazarr/commit/7c40bfec1e390552c6f54c93e7cc5336307ed237)\n- Fixed output encoding for subtitles synchronization. #2323 [72bd52c](https://github.com/morpheus65535/bazarr/commit/72bd52ce79405cc8adca427d2f5887a50273839e)\n- Fixed numeric password issue for authentication. #2326 [f2cd79e](https://github.com/morpheus65535/bazarr/commit/f2cd79ec20692f52b7bdfd7698a52b4aa3c154b4)\n- Fixed function to prevent usage of Python 3.7 [5e68fac](https://github.com/morpheus65535/bazarr/commit/5e68fac4d175f2999b5b05bf7bdd02b76cdf942d)\n- Fixed exception being raised when setting all numeric password to access the web UI [256ceeb](https://github.com/morpheus65535/bazarr/commit/256ceeb598e918cd26867ab583c8748193b31b88)\n- Updated zimuku URL [058a005](https://github.com/morpheus65535/bazarr/commit/058a00594e676cf8f87f910fec9c300c8ef856d0)\n- Silented engine.io exception when client session as expired on server side [a3b32c3](https://github.com/morpheus65535/bazarr/commit/a3b32c3ebfdb8d6c37127bb1af27fcca2f0a46c1)\n- Added support for 7z by upgrading rarfile dependency [4b7cdbc](https://github.com/morpheus65535/bazarr/commit/4b7cdbc5f38c68549e4c2e454f946078db6fb338)\n- Fixed usage of number only username or password for providers, proxy or postgres. #2309 [c97a98d](https://github.com/morpheus65535/bazarr/commit/c97a98d4f2141a34fd798612457f678f10ce0461)\n- Fixed upgrade subtitles tas not removed from scheduler when disabled in settings. #2308 [fd5b087](https://github.com/morpheus65535/bazarr/commit/fd5b087f929b6e00164ad80f7e131c8a4b279653)\n- Updated to apprise v1.6.0 [55c5384](https://github.com/morpheus65535/bazarr/commit/55c5384f9cd3b168167343a8177a07732cf1af94)", "date": "2024-02-04T01:30:02Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.4.1/bazarr.zip"}, {"name": "v1.4.0", "body": "From newest to oldest:\r\n- Dropped support for Python 3.7 because our dependencies doesn't support it anymore.\r\n- Fixed Whisper provider to make it throttle longer after ConnectionError [#2258](https://github.com/morpheus65535/bazarr/pull/2258)\r\n- Fixed an issue that could clear episodes history in case Sonarr API return something else than valid series or episodes. [97bdf00](https://github.com/morpheus65535/bazarr/commit/97bdf0066e76b31c188699d7a94caeaa9b795199)\r\n- Improved providers exceptions throttling to reduce useless calls to providers. [bc0b101](https://github.com/morpheus65535/bazarr/commit/bc0b101fd7f93d424949f88e865085f0822bb5ce)\r\n- Fixed infinite download retry loop in whisperai provider [b25d416](https://github.com/morpheus65535/bazarr/commit/b25d4164c296483958ecc951ff17b901a146a245)\r\n- Fixed `(sqlite3.OperationalError) too many SQL variables issue` while inserting too many episodes at the same time. [a10e5db](https://github.com/morpheus65535/bazarr/commit/a10e5dbf3778d9aa177f978fc85184d95b1446a0)\r\n- Fixed issue with series/movies import using a deleted default languages profile. [f2bb48a](https://github.com/morpheus65535/bazarr/commit/f2bb48ac9a47e738e09fa5386cbf5189f3b51210)\r\n- Modified \"no subtitles found\" notification to be a message instead of an error. [ad65573](https://github.com/morpheus65535/bazarr/commit/ad6557357d3d3a46d5692e707b3df90c4a20d777)\r\n- Fixed improper subtitles being downloaded when HI is required by languages profile. #2293 [0f19d79](https://github.com/morpheus65535/bazarr/commit/0f19d79fa2b4a16806487d92a0bf7628f31d947c)\r\n- Fixed languages profile creation issue. [cb3a274](https://github.com/morpheus65535/bazarr/commit/cb3a274894ca43aea9cee919bcc264197a729c23)\r\n- Prevent overwriting season and episode number guessed from file name with db values unless they haven't been properly guessed. #2284 [b87aef8](https://github.com/morpheus65535/bazarr/commit/b87aef8763d94f384a8142394ee6534b08a5e4b4)\r\n- Hide clipboard button when is not possible to copy to clipboard [452f8c1](https://github.com/morpheus65535/bazarr/commit/452f8c12c9cbacd98eabc1f04750a0282903847f)\r\n- Subdivx provider: improve language matching [6d79e6f](https://github.com/morpheus65535/bazarr/commit/6d79e6f34588283683d67ea9da0bc555274db604)\r\n- Improve mods [b36b378](https://github.com/morpheus65535/bazarr/commit/b36b3782d7eb3e348b1baf2c763fc4f81f5982da)\r\n- Fixed an error in previous commit that would prevent episodes syncing. [6925a97](https://github.com/morpheus65535/bazarr/commit/6925a97f2d45cc1edbd0db5e001ba26162ad04ae)\r\n- Fixed compression type for backup. #2278 [54f9570](https://github.com/morpheus65535/bazarr/commit/54f9570a2298ab923a61e2be2c4fbff49c8c9dfa)\r\n- Fixed other issues with subzero_mods parsing. #2276 [5234565](https://github.com/morpheus65535/bazarr/commit/5234565f847123b03d6a73df2f0ebaad2f598315)\r\n- Fixed issues with SQlite older than 3.35. [fe987ba](https://github.com/morpheus65535/bazarr/commit/fe987babc520020996669cf5ead5e180dda01c96)\r\n- Avoid FileNotFoundError indexing subtitles [#2273](https://github.com/morpheus65535/bazarr/pull/2273)\r\n- Improved compression ratio of backup. [e3cda5c](https://github.com/morpheus65535/bazarr/commit/e3cda5c11e2449b7be1a5b1b81b03db4c49e5075)\r\n- Improved stability by usinf Python f-string as much as possible to prevent TypeError and improve code readability. [2ad7ddf](https://github.com/morpheus65535/bazarr/commit/2ad7ddf5a63e42de028e1fd09fccde1b1b086769)\r\n- Fixed issue while parsing enabled mods during subtitles download [225d79e](https://github.com/morpheus65535/bazarr/commit/225d79e5697892cec34d8112a09cd4e23dd55b77)\r\n- Fixed config.ini conversion by disabling interpolation with ConfigParser that failed with addic7ed cookie. [85c65ec](https://github.com/morpheus65535/bazarr/commit/85c65ecb5a33497d1a152bbf46412afa2838e211)\r\n- Fixed datatype issue when creating a new languages profile with PostgreSQL. [2a8a403](https://github.com/morpheus65535/bazarr/commit/2a8a4030b9e032392f172e73679ba47bdb060b77)\r\n- Changing from config.ini to config.yaml [c89da3e](https://github.com/morpheus65535/bazarr/commit/c89da3e6192a519ccefa6bb7d9f9c9eaa280d373)", "date": "2023-11-28T12:26:18Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.4.0/bazarr.zip"}, {"name": "v1.3.1", "body": "From newest to oldest:\n- HDBits provider: add search filters [d657941](https://github.com/morpheus65535/bazarr/commit/d6579417ba3b3555bff3af446bef8a56e2d7a7c6)\n- EmbeddedSubtitles provider: add blacklist support [bee6919](https://github.com/morpheus65535/bazarr/commit/bee6919979409ccbc6e8174b117d091ec8d2e0c2)\n- HDBits provider: handle JSON errors [0031abc](https://github.com/morpheus65535/bazarr/commit/0031abcea4b7a054e55d96005ec9d0eb76490691)\n- Exposed subtitle file size through API [4e7dedc](https://github.com/morpheus65535/bazarr/commit/4e7dedc43c1ceb936b093338363a8c6ff46505c1)\n- Fixed hosszupuska website parsing issue. #2246 [33af93a](https://github.com/morpheus65535/bazarr/commit/33af93a341b326ca314de31c2d312513b209b307)\n- Improved throttling duration for some common configuration or connection issues. [09295dc](https://github.com/morpheus65535/bazarr/commit/09295dcd10ff3727de2546f9af27fdca66dc7baa)\n- Fixed history logging of downloaded and uploaded subtitles. #2261 [2972c38](https://github.com/morpheus65535/bazarr/commit/2972c3881c525cad183d66e2540459c0f19cad77)\n- Improved synchronization speed for Sonarr and Radarr. #2260 [0f216ab](https://github.com/morpheus65535/bazarr/commit/0f216ab69f5d2b386e101f27e679513942d05e41)\n- Fixed history logging issue for episodes and movies subtitles. [1f187d8](https://github.com/morpheus65535/bazarr/commit/1f187d89ca80e3734bf0831e2f7940bb01235695)\n- EmbeddedSubtitles provider: update subtitles checker [654934b](https://github.com/morpheus65535/bazarr/commit/654934b5e1d9a4637e53c6882b2dfc3213600f54)\n- HDBits provider: improve episode matching [3942264](https://github.com/morpheus65535/bazarr/commit/3942264d0057860e53be8d18e8dfb093352f0dbe)\n- Fixed an additional issue with 85d300f94ef60ef60416786a80db2d89917b8266. #2243 [807621a](https://github.com/morpheus65535/bazarr/commit/807621a612a0734e91b8271278099f7d950d01de)\n- Add HDBits.org provider [63335f4](https://github.com/morpheus65535/bazarr/commit/63335f40fcefd773405c11db8550988662ac88ae)\n- Fixed database migration process causing UNIQUE constraint in some edge cases where duplicate ID root folders where stored. #2253 [166d0cc](https://github.com/morpheus65535/bazarr/commit/166d0ccc95112173acf6f6967b2d13cbb5d662c9)\n- Fixed latest migration that cleared history for sqlite users. [0ddadb2](https://github.com/morpheus65535/bazarr/commit/0ddadb273e8671e183a2d37cd5fc3054b9adbccc)\n- Added error logging when trying to determine malformed audio track languages. #2250 [81159a0](https://github.com/morpheus65535/bazarr/commit/81159a09789aa0251c5a6935ff06ae9e886e7a05)\n- Added logging for opensubtitles.com 400 error. #2232 [e7703ca](https://github.com/morpheus65535/bazarr/commit/e7703ca5a08f6a25590bd9d678f844857c41a108)\n- Fixed Postgres issues while creating new database with v1.3.0. #2249 [e2d0647](https://github.com/morpheus65535/bazarr/commit/e2d0647d550972aa28b6917c3d2faf6f000efe71)\n- Subdivx provider: improve movies matches\n- Update providers exception regex for Windows [e06aad7](https://github.com/morpheus65535/bazarr/commit/e06aad7fc6658d7a04479e66b4efb3ce2626b3da)\n- Fixed some code to prevent arbitrary file read and blind SSRF. [17add7f](https://github.com/morpheus65535/bazarr/commit/17add7fbb3ae1919a40d505470d499d46df9ae6b)\n- Removed sucha provider that doesn't exist anymore. #2242 [aa0af3f](https://github.com/morpheus65535/bazarr/commit/aa0af3f601bad55294ec241009293c60bbb3dce3)", "date": "2023-10-14T12:45:19Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.3.1/bazarr.zip"}, {"name": "v1.3.0", "body": "From newest to oldest:\n- EmbeddedSubtitles provider: improve cache management (Fix #2241)\n- Update providers exception info format [ceb947d](https://github.com/morpheus65535/bazarr/commit/ceb947dac1a582205777c27645ee8382f9cf8313)\n- Fixed IndexError in provider throttling function [b736f26](https://github.com/morpheus65535/bazarr/commit/b736f267b5dc5f6eaffa19021858ccc77278cdaa)\n- Add more info to exceptions [9cf2124](https://github.com/morpheus65535/bazarr/commit/9cf21242ca30ad2d7642de430d3a17d464ae04e1)\n- Added analytics tracking of throttled providers [3694254](https://github.com/morpheus65535/bazarr/commit/3694254c79d29498bda53335fbf45c841ba3400d)\n- Prevent subtitles not having a proper matches attribute from raising a TypeError exception. #2243 [85d300f](https://github.com/morpheus65535/bazarr/commit/85d300f94ef60ef60416786a80db2d89917b8266)\n- Added more feedback to UI on automatic and interactive search issues. [fbe6b76](https://github.com/morpheus65535/bazarr/commit/fbe6b765aec54ad4e728ddf36fc36fa62790f719)\n- Modified default value for adaptive searching (now enabled) and improved description in settings [296d497](https://github.com/morpheus65535/bazarr/commit/296d4976730df91948a184ff1fd7afcd6f685f92)\n- Fixed minimal value for time offset input field [1489926](https://github.com/morpheus65535/bazarr/commit/1489926b6f52e8881e45773486fe2530d5dfeca9)\n- Fixed improper http status code returned that prevented proper cache management on browser side. [77283e4](https://github.com/morpheus65535/bazarr/commit/77283e406371c8049f6bc3c92a4fe2ac02b4db25)\n- Improved responses marshalling to better deal with error messages sent to frontend. [8895dd6](https://github.com/morpheus65535/bazarr/commit/8895dd68a8e155550a5362ffea96966aebce5221)\n- Improve manual search modal. Change cache behavior of manual search api to no cache [714c464](https://github.com/morpheus65535/bazarr/commit/714c46444aacc16e78b7dc5d7184a81d7b8f8aea)\n- Improved UI feedback on multiple search, download and upload issues. #2235 [995ae1b](https://github.com/morpheus65535/bazarr/commit/995ae1b5b8f475f9ce5f15b030ad74ca428b2e91)\n- Fixed opensubtitles.com token from being added to debug log in login response [162dbc0](https://github.com/morpheus65535/bazarr/commit/162dbc0eee6e38511f97aebcc4cfe8ce913d3623)\n- Improved debug logging of login attempts to opensubtitles.com provider. [1bdad91](https://github.com/morpheus65535/bazarr/commit/1bdad9166362daa24baa0971025f0e058d9284fe)\n- Fix some minor issues in the Notification modal [53951a5](https://github.com/morpheus65535/bazarr/commit/53951a5fad1143dd190d79faaca30e06e3c4ced8)\n- Fixed issue while saving notifications settings [42ae3af](https://github.com/morpheus65535/bazarr/commit/42ae3af4a251ee976f89178368baa1a85e2116a0)\n- Added timeout for call to get announcements from Github [6a9f914](https://github.com/morpheus65535/bazarr/commit/6a9f91450c1ac857cacb592f4e5df3ddee9b4915)\n- Improved supersubtitles to detect properly forced subtitles. #2226 [71f17a2](https://github.com/morpheus65535/bazarr/commit/71f17a202440a29fd106d0c314f96772537f9f2b)\n- Fixed bug introduced in v1.2.1 with analytics settings not being taken into account. [5f509cd](https://github.com/morpheus65535/bazarr/commit/5f509cd0a0ac970362953d92c2af376e2adfffc7)\n- Fixed yavka.net subtitles search since they've modified the website. #2225 [cce1be4](https://github.com/morpheus65535/bazarr/commit/cce1be4d0e020ae6eb46bf3802bccaf457e093e7)\n- Fixed Radarr SignalR deleted movie event being dropped for missing metadata in payload. #2219 [073f3a2](https://github.com/morpheus65535/bazarr/commit/073f3a2cfabf7f3300f1b220033af0cc41baaf19)\n- Fixed issue with Python deprecation warning. #2218 [f58d1a9](https://github.com/morpheus65535/bazarr/commit/f58d1a915d8e2227d9f076aeb3b3ea1cf8059cdc)\n- Removed sensitive information from opensubtitles.com debug logging. [7e211f6](https://github.com/morpheus65535/bazarr/commit/7e211f62500019877838e21ead2c5442b501d731)\n- Added upcoming Python 3.7 deprecation announcement. [d284e62](https://github.com/morpheus65535/bazarr/commit/d284e629b7163bab1a4f9fb8f698fe82006c88db)\n- Fixed history views when upgrade subtitles is disabled. #2210 [6000a88](https://github.com/morpheus65535/bazarr/commit/6000a8889d96364eddda87035e83e6c79c65a035)\n- Fixed translation issue for episodes subtitles. #2214 [fa42346](https://github.com/morpheus65535/bazarr/commit/fa423469688e553270670eb604931abe1a6bee70)\n- Fixed removal of rowid column while preventing the lost of history or blacklist data. [e0e5dae](https://github.com/morpheus65535/bazarr/commit/e0e5daea1de2ac8b6cbd0aa53ba284de70d5297c)\n- Fixed download notifications not being sent properly. [164dc95](https://github.com/morpheus65535/bazarr/commit/164dc95f237702a3972813c68f49a5c3ff34d2b8)\n- Fixed missing table during migration of a new database. [26f6fcb](https://github.com/morpheus65535/bazarr/commit/26f6fcb3adf3440d56c686ea87c8ee00a3724435)\n- Fixed table deletion issue with PostgreSQL. [479f11f](https://github.com/morpheus65535/bazarr/commit/479f11fae62834b341e3d78a2fc82bd6c54440af)\n- Fixed issue with unused rowid columns and removed custom score profiles tables as they aren't used anymore. [6bc46fe](https://github.com/morpheus65535/bazarr/commit/6bc46fe4e97a3127b3baab1373bb2111b57f7ec4)\n- Fixed issue with missing migrations directory in release asset. [6f03758](https://github.com/morpheus65535/bazarr/commit/6f03758becabd53d71a9492eb324c245c9956636)\n- Replaced peewee with sqlalchemy as ORM. This is a major change, please report related issues on Discord. [bccded2](https://github.com/morpheus65535/bazarr/commit/bccded275c3cb09dc001d66858f3200c78723935)\n- Improved logging of failed attempt to authenticate to UI. Successful basic authentication attempt cannot be logged right now. [486d2f9](https://github.com/morpheus65535/bazarr/commit/486d2f9481982fef0ff0a30c314f74e9268cc7fd)\n- Embedded subtitles: fix #2195\n- Improved external subtitles file encoding guessing to ignore those who can't be guessed properly. [a7562e0](https://github.com/morpheus65535/bazarr/commit/a7562e06b5849b32d266cb0ee9e297edbbd4ddb4)", "date": "2023-09-16T02:43:54Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.3.0/bazarr.zip"}, {"name": "v1.2.4", "body": "From newest to oldest:\n- Fixed more stuff related to opensubtitles.com [6e7858f](https://github.com/morpheus65535/bazarr/commit/6e7858fc7ce0905ba1f241885c386d70507f6b56)\n- Improved subtitles providers settings to reset Authentication or Configuration error throttling while saving. [faa9566](https://github.com/morpheus65535/bazarr/commit/faa9566431860873aebbac542990b07a6cba47e8)\n- Fixed opensubtitles.com issue with looping over failed login. [333c6b2](https://github.com/morpheus65535/bazarr/commit/333c6b23f54024ddf0055825cb5e0877e4c98b6f)", "date": "2023-07-22T13:48:30Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.2.4/bazarr.zip"}, {"name": "v1.2.3", "body": "From newest to oldest:\n- Moved file encoding to charset-normalizer instead of chardet that is causing too much issues. #2196 [dd9ce4d](https://github.com/morpheus65535/bazarr/commit/dd9ce4d6ea2068385301a371b469a4c029afab0a)\n- Improved opensubtitles.com provider to deal with return codes in some edge cases. #2179 [529b5a1](https://github.com/morpheus65535/bazarr/commit/529b5a13860606a39617e38a4272561de74cd00c)\n- Fixed exception raised when get_episodesFiles_from_sonarr_api() doesn't return proper content. #2188 [cfc8a91](https://github.com/morpheus65535/bazarr/commit/cfc8a914888acc1f45da80a27643378951179c47)\n- Fixed requirements.txt to use only wheels for Pillow since compilation doesn't work on 32bits Python embedded in Windows installer version. [da7879a](https://github.com/morpheus65535/bazarr/commit/da7879a02a9c3e532e057b59c2b73715e7667945)\n- Fixed upgradable subtitles logic being called even if user do not want to upgrade existing subtitles. [dfd8bed](https://github.com/morpheus65535/bazarr/commit/dfd8bedd6a1e4ebf43c35defff156fd9a135d9fb)\n- Added description of \"provider\" to post-processing option [35dfa24](https://github.com/morpheus65535/bazarr/commit/35dfa2483b2c0ad2e67091d59d9351ed284515cf)\n- Fixed monitored status tooltip for series/movie view [59d00f2](https://github.com/morpheus65535/bazarr/commit/59d00f28d00dd2b809f52c74dfba58d5fb8e6869)", "date": "2023-07-11T00:27:34Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.2.3/bazarr.zip"}, {"name": "v1.2.2", "body": "From newest to oldest:\n- Add debugging changes [b17a2a5](https://github.com/morpheus65535/bazarr/commit/b17a2a5f4a5712d00dfb2c8be3c2062072c2aa61)\n- Reverting b6777bc26ea2faafa4d6a8eee8aa791a8736780a [edfbb1a](https://github.com/morpheus65535/bazarr/commit/edfbb1a5ca0edb43eb9e88ae83740c10649a8a9b)\n- Added experimental Python 3.11 support [c92d574](https://github.com/morpheus65535/bazarr/commit/c92d574bf2c406974db3f1d513b37398ac9a0e70)\n- Debugging: update subtitle and language representations [b6777bc](https://github.com/morpheus65535/bazarr/commit/b6777bc26ea2faafa4d6a8eee8aa791a8736780a)\n- Updated pytz module to support latest timezone changes [cd01684](https://github.com/morpheus65535/bazarr/commit/cd016840f9d69f48429ff2d088a4fa00d705d41c)\n- Fix path mapping issues [ab3c171](https://github.com/morpheus65535/bazarr/commit/ab3c1714830e0f0d8b5f003a854402dc27cb1a7d)\n- Subf2m provider: improve episode matching [4921c55](https://github.com/morpheus65535/bazarr/commit/4921c55bf05189f68bdd6279b957eaffd00e7cf0)\n- Fix issues when saving the notification providers [374e4be](https://github.com/morpheus65535/bazarr/commit/374e4bec87c4581c4bb07e7fe528123ffa6998d4)\n- Fixed issue in releases page since we upgraded Mantine. [560a697](https://github.com/morpheus65535/bazarr/commit/560a697beb21e20d815ff1513eca8903097960fc)\n- Removed leftover from legendas.tv provider. [fc01267](https://github.com/morpheus65535/bazarr/commit/fc01267d5bd3f854c4dff8794cfd1502d20a41f2)\n- Fixed issue where subtitles would get upgraded even if not required anymore by shows languages profile. #2174 [d4262e5](https://github.com/morpheus65535/bazarr/commit/d4262e530adcb048e81e167a06bb4e50936c87d7)\n- Subf2m provider: improve queries [304ad16](https://github.com/morpheus65535/bazarr/commit/304ad160e0157ed98d3bc30d5512b23e9ce233e9)\n- Embedded Subtitles provider: Fix #2171\n- Update language equals mechanism [2b0e435](https://github.com/morpheus65535/bazarr/commit/2b0e435f6a6485e833233f9fab9203ef1b02d184)\n- Subf2m provider: add support for IMDB IDs [ee1506e](https://github.com/morpheus65535/bazarr/commit/ee1506ed54b5a610dca7ca47ff6a09f13773c84e)\n- Embedded Subtitles provider: handle ASS errors\n- Subf2m Provider: add user-agent header configuration [e3c4def](https://github.com/morpheus65535/bazarr/commit/e3c4def89acc185bdbb67f8dcb7a12531e209df7)\n- Fixed yavka.net provider. #2169 [a13d69d](https://github.com/morpheus65535/bazarr/commit/a13d69ddb2f41fcba4bd78952db368b9cce6885c)\n- Increase width of the manually search modal [963ce21](https://github.com/morpheus65535/bazarr/commit/963ce21d9b723d50baa72f235afcc0f2a68aecab)\n- Fixed some UI issues caused by React and Mantine upgrade. #2167 [bc40aba](https://github.com/morpheus65535/bazarr/commit/bc40abac50ccc4967c43a54e35cca1a02070e0e2)\n- Fixed server side exception throttling opensubtitles.com when it shouldn't. #2153 [3c57381](https://github.com/morpheus65535/bazarr/commit/3c57381bb4c6f67ab7fead06508da8223794ea70)\n- Fixed Chinese translation issue (zh and zt inverted). #2166 [b9b97e9](https://github.com/morpheus65535/bazarr/commit/b9b97e980e5bd542489cec0c6abe66adba0a6c38)\n- Updated apprise module to improve notification system. #2163 [07f601f](https://github.com/morpheus65535/bazarr/commit/07f601f407ef5b9e6fe0b0db842f3bec8c9916b0)\n- Update all UI dependencies and update React version to 18 [#2127](https://github.com/morpheus65535/bazarr/pull/2127)\n- Downgrade Axios to fix a settings saving issue. [6a9f875](https://github.com/morpheus65535/bazarr/commit/6a9f875cbd588a43864a7772c3ac5ed8596c5404)\n- Added support for Portuguese and Simplified Chinese to opensubtitles.com. #2159 [ead8a38](https://github.com/morpheus65535/bazarr/commit/ead8a3892793351fb504a1ec8fd33fa54301d8f6)\n- Added some exceptions that shouldn't be retried to retry function. #2153 [2346f3e](https://github.com/morpheus65535/bazarr/commit/2346f3ed580ac8c05d3971315f239694201364ed)\n- Language-equals: add compatibility for more providers [4725496](https://github.com/morpheus65535/bazarr/commit/4725496313a53e47cda01a092e6829a282413e0b)\n- Fixed external subtitles indexation with accented characters that resulted in download in loop. #1961 [d90d1cb](https://github.com/morpheus65535/bazarr/commit/d90d1cbfcc57ff07ad2cff136481260dbb1ef7e3)\n- Added feature to treat couples of languages as equal when searching for subtitles [547f8c4](https://github.com/morpheus65535/bazarr/commit/547f8c428df856d97bf9d258e723e39a7609b635)\n- Fixed type in opensubtitles.com provider that could cause AuthenticationError for new users. #2152 [baf7a73](https://github.com/morpheus65535/bazarr/commit/baf7a7300d7d3556642dfab48b37c2a64743afeb)\n- Specify ffmpeg path in case it's not in PATH envvar [#2147](https://github.com/morpheus65535/bazarr/pull/2147)\n- Add support for custom languages in audio tracks [585c70c](https://github.com/morpheus65535/bazarr/commit/585c70c39d8ae1e3b9b82fae8800417eba5f6444)\n- Use literals instead of scripts for custom languages [13f965d](https://github.com/morpheus65535/bazarr/commit/13f965d7255d99a23633ee1f04dc0adfb3b8dd4d)\n- Fixed AI and machine translated subtitles being incorrectly returned by Opensubtitles.com [bdf4ee8](https://github.com/morpheus65535/bazarr/commit/bdf4ee85af7bd9f194da82420f66649e964650a1)\n- Emergency fix for custom languages issue [015beaf](https://github.com/morpheus65535/bazarr/commit/015beaf769f7d10077ad590a6ed0363c4407f129)\n- Add mediainfo support for custom languages [c06dd62](https://github.com/morpheus65535/bazarr/commit/c06dd620b759d6dd37125caf19a22711fc426f37)\n- Implemented gzip compression of http requests made to Bazarr [2b5cd2b](https://github.com/morpheus65535/bazarr/commit/2b5cd2b72e19469b545e4d35e2564d5219a36e7c)\n- Subdivx Provider: improve series matching [5f9418b](https://github.com/morpheus65535/bazarr/commit/5f9418b1f3eae5b1acb5f8c4ecbd835726c6f63f)\n- Fixed yify provider MissingSchema exception on download. #2139 [a6ecbb4](https://github.com/morpheus65535/bazarr/commit/a6ecbb43154d6f57bcab4ea3be17467b487eea3f)\n- Fixed zimuku.org parsing error [0907269](https://github.com/morpheus65535/bazarr/commit/0907269377401b64a52589a54431e4ab75a2587a)\n- SuperSubtitles provider: fix hungarian subtitles downloads [e83f37d](https://github.com/morpheus65535/bazarr/commit/e83f37d42ec3a8141ca6b5ca487d0ab200e59cb5)\n- Fixed SyntaxError under Python 3.7 [09f0a27](https://github.com/morpheus65535/bazarr/commit/09f0a2783377e366a6a75d60ff57775abe244596)", "date": "2023-06-24T22:08:44Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.2.2/bazarr.zip"}, {"name": "v1.2.1", "body": "From newest to oldest:\n- Fixed requirements.txt installation [0f84ffb](https://github.com/morpheus65535/bazarr/commit/0f84ffb9cd464dbbeae5e00885494c97fac4dd4a)\n- Fixed zimuku that now require anti-captcha [e2ba532](https://github.com/morpheus65535/bazarr/commit/e2ba532cee04b6fbf1e536d18666dc4d017f23ee)\n- Fixed upgrade of non-existant subtitles files [ac6dddd](https://github.com/morpheus65535/bazarr/commit/ac6dddd6073708d484fe40928cc65db0a66803a5)\n- Fixed episodes blacklist update in webui [2c313a4](https://github.com/morpheus65535/bazarr/commit/2c313a40a558069c8cf4a3dc6efa80e4bec50f43)\n- Fixed yifysubtitles url [e263927](https://github.com/morpheus65535/bazarr/commit/e263927779365a641a9018016763ebf05c769fdd)\n- Update node package to latest version, fix eslint warning [93e6324](https://github.com/morpheus65535/bazarr/commit/93e63246bff145720fed82ae8b236a6669926b49)\n- Subdivx Provider: handle UnicodeEncodeError for some titles [da542a3](https://github.com/morpheus65535/bazarr/commit/da542a311a5f9a72f36cf25802b38e75e518d7db)\n- Allow defining postgres connection settings via env vars [695734a](https://github.com/morpheus65535/bazarr/commit/695734abe67f56f34a5e99aa98cd558e2b39545b)\n- Added OpenAI Whisper Provider [8b1db07](https://github.com/morpheus65535/bazarr/commit/8b1db07e9f984f66df669d7b5a31868c9ef48530)\n- SuperSubtitles provider: fix episode matching [1427a8a](https://github.com/morpheus65535/bazarr/commit/1427a8ab733685f0813e5c07b38dca6bf92453dd)\n- Fixed opensubtitles.com provider year matching process. [ef67cd4](https://github.com/morpheus65535/bazarr/commit/ef67cd4792b365521a2d9e38f5d998d6556f2c08)\n- Added announcements for deprecated Sonarr and Radarr versions end of support. [17e62f2](https://github.com/morpheus65535/bazarr/commit/17e62f2d29411e5c5c635229da8f15b97a46af78)\n- Disabling mediainfo path temporarily until issue with knowit is fixed. #2098 [3a10df7](https://github.com/morpheus65535/bazarr/commit/3a10df7724750e5221ba608b875ff16bd0a254d5)\n- Trying to fix Segmentation fault caused by mediainfo in docker container. #2098 [7455496](https://github.com/morpheus65535/bazarr/commit/7455496c4c42518df5f20646d50a93ca66c1a912)\n- Added an option to prevent sleeping hard drives from being wake-up by video file hashing. [7136383](https://github.com/morpheus65535/bazarr/commit/71363830985a34f5f45a32972477e0ac83dce519)\n- Fixed error when manual search return an invalid subtitles. #2102 [9fb793a](https://github.com/morpheus65535/bazarr/commit/9fb793a4882bbf66c09014df6c09fe4770f36c71)\n- Fixed HI subtitles wrongly ignored when normal or HI is selected in languages profile. [9bd2ab5](https://github.com/morpheus65535/bazarr/commit/9bd2ab514e273426a0918a2c64165f0bcf9f7be3)\n- Replaced deprecated Google Universal Analytics by GA4 [abc48b4](https://github.com/morpheus65535/bazarr/commit/abc48b4ed0fc9ba8daca16d0076c13b9d6be1475)\n- Titrari Provider: update search path [5250785](https://github.com/morpheus65535/bazarr/commit/52507854e8334f7d4ab64212660fda00817f5e2e)\n- Fixed fcache issue when moving cache file between disks. [37059e7](https://github.com/morpheus65535/bazarr/commit/37059e7fbdeb47324d277c31adfd7d1e37b1ef1f)\n- Fixed opensubtitles.com provider to prevent downloading of AI or machine translated subtitles. [7720d00](https://github.com/morpheus65535/bazarr/commit/7720d000de00545ffb0d0c53ec70212545ab886a)\n- Fixed permissions issue with cache file. #2084 [fb6ac47](https://github.com/morpheus65535/bazarr/commit/fb6ac47bea64c5715672e4f11be8ffc1e02e32bf)\n- Supersubtitles provider: fix #2092\n- Subf2m Provider: improve episode matching (#2081) [ad13f79](https://github.com/morpheus65535/bazarr/commit/ad13f79d193d479b0c674a05330b97befc8445c7)", "date": "2023-05-02T00:36:35Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.2.1/bazarr.zip"}, {"name": "v1.2.0", "body": "From newest to oldest:\r\n- Improved Gestdown provider to get better matches using tvdb id [248e49d](https://github.com/morpheus65535/bazarr/commit/248e49de76c4a94e9dc6db9166521b8527f476bc)\r\n- Fixed subtitles file naming when using hearing-impaired removal mods. [e4bf041](https://github.com/morpheus65535/bazarr/commit/e4bf041ecb4921c8829ab60b8de13fde982cd20e)\r\n- Improved languages profile edit modal to clarify subtitles types. [ef46ab9](https://github.com/morpheus65535/bazarr/commit/ef46ab9261797f2fb6f37e08ac4186d8c8bb0901)\r\n- Fix postgresql reconnection #2071 [2e8203f](https://github.com/morpheus65535/bazarr/commit/2e8203f0d4f9830a3608db28e6f61f8d9d87715e)\r\n- Fixed history logging of manually uploaded subtitles. #2072 [2acf245](https://github.com/morpheus65535/bazarr/commit/2acf2451b44e0dd407e299170ed37117b0eeff7b)\r\n- Fixed some issues after subtitles upgrade refactor. [0196139](https://github.com/morpheus65535/bazarr/commit/019613958e5736a5adfdfc2527ebc753805e6c9c)\r\n- Added option to deeply analyze audio tracks from media files or not to determine languages. Default is to use Sonarr/Radarr provided languages instead. Should prevent API limit ban for cloud user. [5dc4e78](https://github.com/morpheus65535/bazarr/commit/5dc4e782ae31ed36aaf4d27d9a46af3e494c7491)\r\n- Refactored subtitles upgrade [5b28309](https://github.com/morpheus65535/bazarr/commit/5b283098f9ec0804706225cc0b1266515e034be7)\r\n- Fixed zimuku 403 error caused by search url change [7206624](https://github.com/morpheus65535/bazarr/commit/72066245e0a5b6a27d93cc46245403e309ee4b35)\r\n- Fixed uppercase detection mod with multiline HI [68a0d78](https://github.com/morpheus65535/bazarr/commit/68a0d787256f73982da0bc34020d102a9d69ed0f)\r\n- Added Announcements section in System to be able to inform users of Bazarr's news. [58262bc](https://github.com/morpheus65535/bazarr/commit/58262bc299d7e0f8742379d7018e06bf86a5b9b7)\r\n- Fixed UI freeze on certain notification events [339883c](https://github.com/morpheus65535/bazarr/commit/339883cff64c0d95378599b99337068245968a1a)\r\n- Fixed Upgrade previously downloaded subtitles (#2059) [6578710](https://github.com/morpheus65535/bazarr/commit/6578710c8d8d384cfffcb42fdd008da0c5571250)\r\n- Fixed upgradable icon in history now show properly [5c01c3c](https://github.com/morpheus65535/bazarr/commit/5c01c3c2e216153e4dbc1b3ec157f1ccdbaa5b2d)\r\n- Added postgres specific requirements. #2063 [d721be3](https://github.com/morpheus65535/bazarr/commit/d721be3185fb7581a6b3ee68d01977a21865ef12)\r\n- Supersubtitles provider: update matcher and downloader [62b5bd8](https://github.com/morpheus65535/bazarr/commit/62b5bd84b909e8e784cded76899ec653bbc3c071)\r\n- Improve providers utils [f6c0146](https://github.com/morpheus65535/bazarr/commit/f6c01464020f70e2736f8a2d9ad185515e3d87db)\r\n- Fixed chmod not working after custom post-processing. [8ac3b0c](https://github.com/morpheus65535/bazarr/commit/8ac3b0c9b56def6153e3144bec7be0f0abf56f8a)\r\n- Add Unit Tests to UI [#2015](https://github.com/morpheus65535/bazarr/pull/2015)\r\n- Bring back clear action to the mass editor [839ce38](https://github.com/morpheus65535/bazarr/commit/839ce384c64caa9767125661b4a51364965a907b)\r\n- Improved audio track language detection by using our video parser instead of values provided by Sonarr/Radarr. We also added \"treat as\" concept for undefined languages audio and embedded subtitles tracks. #2050 [3310f6a](https://github.com/morpheus65535/bazarr/commit/3310f6aeb88fcc9a70f9e5d6f673873ff2f1af85)\r\n- Fixed podnapisi provider on host with OpenSSL 3 [7f05f93](https://github.com/morpheus65535/bazarr/commit/7f05f932ffb84ba8b9e5630b2adc34dbd77e2b4a)\r\n- Increased minimum subtitles search and upgrade frequencies to 6 hours instead of 3 to reduce the impact on providers' website. [d39f41d](https://github.com/morpheus65535/bazarr/commit/d39f41d7e275e7426049aac69387e39b2ad64edb)\r\n- Try to fix the unresponsive issue when notifications are showed [c7b4292](https://github.com/morpheus65535/bazarr/commit/c7b4292100b9875879a6304c7e8e463cd39fd3f0)\r\n- Add Subscene Cloudscraper Provider [Experimental] [3a0085a](https://github.com/morpheus65535/bazarr/commit/3a0085a155ad81784b6132ec1b8048b4d626bf95)\r\n- Subf2m Provider: add optional config for SSL verification [47aff78](https://github.com/morpheus65535/bazarr/commit/47aff78ab0acb8e1c4ebabed455e1a956392da04)\r\n- Fix for get movies/series response serializer [11fff72](https://github.com/morpheus65535/bazarr/commit/11fff727b2699648d29bbe6b438c67ac399bf301)\r\n- Improved impact on opensubtitles.com provider by simplifying queries to improve cache usage. [f057a77](https://github.com/morpheus65535/bazarr/commit/f057a778203e9729b6a02ff137388de361c0406b)\r\n- Fix for #2051 [738e10e](https://github.com/morpheus65535/bazarr/commit/738e10e075d8b6afce7836d3b7d5d38968693444)\r\n- Added PostgreSQL as optional database engine [d5911e7](https://github.com/morpheus65535/bazarr/commit/d5911e78b5f141fe3badd40dd185c148c5f8034a)\r\n- Fix display for notification task in progress [9f2ba67](https://github.com/morpheus65535/bazarr/commit/9f2ba673de0693cb8737bdee7929a22f31f8b987)\r\n- Added configurable request timeout to Sonarr and Radarr [f5d7b4d](https://github.com/morpheus65535/bazarr/commit/f5d7b4d3218a7e88f5267d3c58f16fe271afce0e)\r\n- Fixed improper profile ID value preventing listing of series and movies. #2043 [cf4571b](https://github.com/morpheus65535/bazarr/commit/cf4571ba8604fbdda84d1f775b9c6888e06018a2)\r\n- Fixed unwanted injection of variables on API endpoints. [b0f7437](https://github.com/morpheus65535/bazarr/commit/b0f743767bf907df56c4a5310ab0938bc73f8722)\r\n- Embedded Subtitles provider: update filters [be75d78](https://github.com/morpheus65535/bazarr/commit/be75d78b939ca8b798c50d9c95b5d8b98ada984d)\r\n- Fixed high CPU usage when improper timezone defined (will use UTC instead). #1914 [2babae1](https://github.com/morpheus65535/bazarr/commit/2babae1937917567afe1ac7693768a81c6b1354a)\r\n- Fixed analysis of corrupted video file that may wrongly throttle providers. #2034 [92be39b](https://github.com/morpheus65535/bazarr/commit/92be39bebac885ff695030756c8e52389569c951)\r\n- Fixed post-processing output logging not returning anything if stdout is an empty string while stderr return the actual error. [fadda0a](https://github.com/morpheus65535/bazarr/commit/fadda0ac4087b3c31ae8524b14249cdda24d7680)\r\n- Fix task progress notification [d2bd0c7](https://github.com/morpheus65535/bazarr/commit/d2bd0c7c7e559bf9742ed2ebb4944e3913970caa)\r\n- Fixed RegieLive provider to use the official API [237e85b](https://github.com/morpheus65535/bazarr/commit/237e85bd3454ce061f2632ba066c5f3ed2e5c31e)\r\n- Updated regielive provider to use built in search and scraper API [9105c53](https://github.com/morpheus65535/bazarr/commit/9105c53d6a9ebd309710ee47651b5a422da1886f)\r\n- Fixed yifysubtitles provider. #2029 [e57a99d](https://github.com/morpheus65535/bazarr/commit/e57a99d39eb6f6095568a11642f80b42c4512095)", "date": "2023-03-03T02:12:17Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.2.0/bazarr.zip"}, {"name": "v1.1.4", "body": "From newest to oldest:\n- Removed Enzyme for embedded subtitles indexing and fixed mediainfo integration issues. #2007 [5d36b80](https://github.com/morpheus65535/bazarr/commit/5d36b80c0753cff951d3198a197cf709f43efca2)\n- Fixed mediainfo integration issues. #2007 [0970f19](https://github.com/morpheus65535/bazarr/commit/0970f19d96443401b92099ceaa1e8b654258b077)\n- Added mediainfo as potential embedded subtitles parser. #2007 [c4b8345](https://github.com/morpheus65535/bazarr/commit/c4b8345e659e38992456e31a4c21bbf0a7d38545)\n- Subtitrarinoi provider: add several improvements [1233026](https://github.com/morpheus65535/bazarr/commit/1233026adc896055d73576e23e826cb6b8e3c5b2)\n- Updated regielive provider to use a search proxy [82c9e14](https://github.com/morpheus65535/bazarr/commit/82c9e1434108f5c030da08a09375d4ba3ed8d50b)\n- Fixed wizdom provider urls [577a0d6](https://github.com/morpheus65535/bazarr/commit/577a0d65428f950b0074307e21145258120f5769)\n- Embedded Subtitles provider: improve streams parsing [7640a6d](https://github.com/morpheus65535/bazarr/commit/7640a6d5a420db2c74648208be62513d1b788da0)\n- Fixed issues with new database creation since e6d089381285ab3e945971dad7899e17062062a6 [6693b98](https://github.com/morpheus65535/bazarr/commit/6693b988115450e7823d1735cb2eb431ab72db39)\n- Improve titulky session expiration handling [#2014](https://github.com/morpheus65535/bazarr/pull/2014)\n- Fixed Plex webhook trying to search for subtitles for pre-roll video. #2013 [2622a08](https://github.com/morpheus65535/bazarr/commit/2622a0896eec60ed0647175cdc42be5a971aeab0)\n- Removed Legendastv provider since the website as been shutdown. #2012 [383d906](https://github.com/morpheus65535/bazarr/commit/383d906749656491aedf333fda6fa8f50983c2bd)\n- Added routine to remove deprecated providers from enabled providers in config.ini [e6d0893](https://github.com/morpheus65535/bazarr/commit/e6d089381285ab3e945971dad7899e17062062a6)\n- Added logging to legendasdivx provider when lxml isn't installed properly. #2009 [993168e](https://github.com/morpheus65535/bazarr/commit/993168e901e47b5932bd0abfc784909ac14a3199)\n- Fix Notification settings not saving after removing Discord #2005 [06f0fe9](https://github.com/morpheus65535/bazarr/commit/06f0fe9972c2e6d06a54d2acee94e5f03521811e)\n- Updated opensubtitles.com to give more information to the download limit reached exception. [979301d](https://github.com/morpheus65535/bazarr/commit/979301dee01241028ca1c5e9e30f7cf6fb6cf451)\n- Fixed improper page_size value that prevent series and movies page from loading. #1995 [cc42e7b](https://github.com/morpheus65535/bazarr/commit/cc42e7bc5c8011a544bb5968b67b56347d0cb224)\n- Fixed some edge case where no media could be found in db with a specific path. #2003 [e6cab17](https://github.com/morpheus65535/bazarr/commit/e6cab17a2942360cac30784775db2f4fd16afaa6)", "date": "2022-12-31T16:36:31Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.1.4/bazarr.zip"}, {"name": "v1.1.3", "body": "From newest to oldest:\n- Fixed decimal value of framerate change tools from being rounded to unit. #1999 [920e799](https://github.com/morpheus65535/bazarr/commit/920e799468fd96d7a09c2ff2b0ef60c0be175553)\n- Removed call to deprecated language profile endpoint in Sonarr v4. #1998 [ea5bf9a](https://github.com/morpheus65535/bazarr/commit/ea5bf9ad072c73874fcf39fef45f6fc718a4e616)\n- Refactor settings submit hooks, try to fix issue #1924 [30f04fe](https://github.com/morpheus65535/bazarr/commit/30f04feae68e190a09ec00a35b84d42576086945)\n- Fixed issue while guessing episode number from subtitles filename in some edge case. #1994 [65c6a67](https://github.com/morpheus65535/bazarr/commit/65c6a67963b32d03fb41ddd6a34b4fa56668a503)\n- Embedded Provider: improve streams filtering [8c3a844](https://github.com/morpheus65535/bazarr/commit/8c3a844071e67db9d7d83f262527b01e804f91d2)\n- Fixed issue when explicitly requested HI subtitles from opensubtitles.com, subtitrarinoi or titrari. [29a4842](https://github.com/morpheus65535/bazarr/commit/29a4842e2d6f1623600f439d992a3194053c3b9f)\n- Fixed log an backup download from UI after flask upgrade. [8c944c4](https://github.com/morpheus65535/bazarr/commit/8c944c4cf2b8f2454d77983ab88c25f96d5eeec0)\n- Fix #1987\n- Move pageSize in UI settings to backend [9165663](https://github.com/morpheus65535/bazarr/commit/9165663cbab245ab06adb843fef35e88dd2a3bd0)\n- Fixed bad subtitles extension exception when uploading subtitles with uppercase extension. #1988 [551f57b](https://github.com/morpheus65535/bazarr/commit/551f57bc0ece32748c73b582ae3a54581f3e15db)\n- Fixed issue while downloading movie subtitles. [05cffa9](https://github.com/morpheus65535/bazarr/commit/05cffa94104c29b22ecbd58ee93f36a44c41c5b2)\n- Add 'other' attribute to Video class [923bcb4](https://github.com/morpheus65535/bazarr/commit/923bcb438298beef7cf4e620e6611a07c51ebcbc)\n- Fixed some remaining issues with assrt. #1953 [118eb09](https://github.com/morpheus65535/bazarr/commit/118eb09db03f54cd371983653530d2c2c26da83e)\n- Fixed infinite loop while trying to connect to Sonarr with SignalR permission issue. [0e8ac06](https://github.com/morpheus65535/bazarr/commit/0e8ac06e59d2181e9462eb975ed854279dad7f93)\n- Fixed issue with cutoff not enforced when searching for multiple languages at the same time. [2e2626c](https://github.com/morpheus65535/bazarr/commit/2e2626ce43dd2e364fa6b54498714c81aca20e3f)\n- Refactor Argenteam Provider [52760d8](https://github.com/morpheus65535/bazarr/commit/52760d8bc738e24b63b9b50db5f703d144e10139)\n- Fixed issue with Sonarr v3 SignalR for good. #1985 [4343b7c](https://github.com/morpheus65535/bazarr/commit/4343b7c40245df02edc0a72b1e693c470693d41e)\n- Revert \"Fixed Sonarr v3 SignalR feed not connecting when mono is used to run Sonarr. #1985\" [c563262](https://github.com/morpheus65535/bazarr/commit/c563262fa851348c934b93a4ea872f23e35bb099)\n- Fixed Sonarr v3 SignalR feed not connecting when mono is used to run Sonarr. #1985 [a26b86c](https://github.com/morpheus65535/bazarr/commit/a26b86cb953fb75859fe567e641f27253fe5a3a2)\n- Added live(SignalR) feeds status for Sonarr and Radarr. [b8b916d](https://github.com/morpheus65535/bazarr/commit/b8b916dc30156c66e4514477a5df59450dfa8b84)\n- Add support of string value for badge in navbar [726afcc](https://github.com/morpheus65535/bazarr/commit/726afcc7f81bd931e1c4b91157aceceb9df72300)\n- Subf2m Provider: improve series title matches [d6883c2](https://github.com/morpheus65535/bazarr/commit/d6883c2c7323641035537ba7b306808524e721db)\n- Update database refiner [1ba9404](https://github.com/morpheus65535/bazarr/commit/1ba94041299ecdd054e19c1ee4178dde115af076)\n- Updated vendored dependencies. [bbe2483](https://github.com/morpheus65535/bazarr/commit/bbe2483e21c2c1549ceeed16f021f9581b899f70)\n- Add support for configurable scores (movies and episodes) [708fbfc](https://github.com/morpheus65535/bazarr/commit/708fbfcd8ec0620647975be39a1f6acbbf08f767)\n- Fixed improper update of providers pool when forced subtitles are involved. #1977 [0b8274e](https://github.com/morpheus65535/bazarr/commit/0b8274ec3e12521f3bd99ccc00e90aca31713ca9)\n- Gestdown Provider: Use ShowId to find subtitles. Fixes issue with Slash in show name. [#1979](https://github.com/morpheus65535/bazarr/pull/1979)\n- Improved assrt provider error message logging. #1953 [78f769d](https://github.com/morpheus65535/bazarr/commit/78f769d743f6d5413d3a3c0c1c09570d8ee9cb2b)\n- Update node packages, fix issues [aee1849](https://github.com/morpheus65535/bazarr/commit/aee1849ce3653320aef139500e84fe6643cbbcbc)\n- Subf2m Provider: add support for complete series packs [122398f](https://github.com/morpheus65535/bazarr/commit/122398fdc869c1c9bb3aa092f6997db85da26925)\n- Improve file picker for season packs [876b42b](https://github.com/morpheus65535/bazarr/commit/876b42bb8f8db9e16364d597162e987481978e36)\n- Improved napiprojekt to search for subtitles using video metadata. [bf26dd2](https://github.com/morpheus65535/bazarr/commit/bf26dd231adc2e9c1887ebfb17a59b5d86d043ff)\n- Add new hook useSubmitHookWith to dynamically inject the submit hooks #1924 [1766cee](https://github.com/morpheus65535/bazarr/commit/1766ceeabc103cfe33983bf1b243ddb4f4649476)\n- Try to fix languages profiles editor by introducing a new submit hooks source in the settings page #1924 [c08ba5f](https://github.com/morpheus65535/bazarr/commit/c08ba5f7937fcd86533a91ddc1a0675d7fc623fb)\n- Fixed blacklist item removal issue. [49fc597](https://github.com/morpheus65535/bazarr/commit/49fc597efb208f4ed6ca270bcb1696358fc45818)\n- Subf2m provider improvements [#1973](https://github.com/morpheus65535/bazarr/pull/1973)\n- Subdivx Provider: add more search improvements [21359b3](https://github.com/morpheus65535/bazarr/commit/21359b32b551ac13ecedc07b83f8c90bf080ec53)\n- Subdivx Provider: improve series matches [2f8814d](https://github.com/morpheus65535/bazarr/commit/2f8814dcf801552ceb4be5c5a98c93a22799695c)\n- Subdivx Provider: improve episode queries [71fe2b7](https://github.com/morpheus65535/bazarr/commit/71fe2b7a1ddc832ac903bdfcdc8c4e80e84e32db)\n- Fixed history statistic view not taking timezone into account. [d7c8188](https://github.com/morpheus65535/bazarr/commit/d7c8188f946ac671fa95124df89dca01bc31ed62)\n- Fixed another issue with assrt. #1953 [0374790](https://github.com/morpheus65535/bazarr/commit/0374790ed2c1f52b2cf95b0eb6a32d940a72d1e9)", "date": "2022-12-05T02:32:58Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.1.3/bazarr.zip"}, {"name": "v1.1.2", "body": "From newest to oldest:\n- Fixed another issue with assrt. #1953 [7ea452c](https://github.com/morpheus65535/bazarr/commit/7ea452c467ef9cf721b591b6b4a1b8e44d67f3e9)\n- Update subzero mods [daeb28b](https://github.com/morpheus65535/bazarr/commit/daeb28baeff206bf72a5cf68e3a3a493f99d5c9e)\n- Fix chip input issue [1a612d1](https://github.com/morpheus65535/bazarr/commit/1a612d12b802313220f95a65e66cfe8f702bcd6f)\n- Fix display issue in the search bar [559ec50](https://github.com/morpheus65535/bazarr/commit/559ec50c92c1a357a3d1794202a268d953dc75e4)\n- Upgraded Apprise to 1.1.0 version. [04b0959](https://github.com/morpheus65535/bazarr/commit/04b095995c221e3ffd7db95763c11a137c70d57f)\n- Fixed maximum number of requests per minute for assrt provider. #1953 [167cb5d](https://github.com/morpheus65535/bazarr/commit/167cb5dd983025e9935abc00d59d291b8ef571f4)\n- Fix titulky's subtitle downloading logic [a82ab67](https://github.com/morpheus65535/bazarr/commit/a82ab6769d9f508151c1cc438a6ab637d65bd7e8)\n- Embedded Subtitles provider: avoid KeyError [003e033](https://github.com/morpheus65535/bazarr/commit/003e033c937c111cb57684df897cdc9e917c815c)\n- Added support for series monitored status. #1964 [af70cf1](https://github.com/morpheus65535/bazarr/commit/af70cf1fc9c908494d3d2e5877d9dbfa0cd7a223)\n- Fixed use original format when uploading subtitles. #1955 [65c0e6b](https://github.com/morpheus65535/bazarr/commit/65c0e6b82393943797e36d94b2d1ce109cd46629)\n- Fix provider pool updates [571ffbc](https://github.com/morpheus65535/bazarr/commit/571ffbccf8544224bc63c9e51688be6310fb05e3)\n- Gestdown: Check for show before checking for subtitle [#1962](https://github.com/morpheus65535/bazarr/pull/1962)\n- Fixed subtitulamos.tv improper matching. #1960 [8348b6c](https://github.com/morpheus65535/bazarr/commit/8348b6c0dcbd7bf9fd5c3011522f71b9a0f6dd5e)\n- Fixed assrt provider to prevent making too many queries to the API. #1953 [0980af1](https://github.com/morpheus65535/bazarr/commit/0980af10608081cba784854ee0ea4e88ad816f20)\n- Subdivx Provider: improve spanish detection [c791f39](https://github.com/morpheus65535/bazarr/commit/c791f39e53b28f6b2e1053098bc39923ae1f7dbf)\n- Fix popover width in the manual search modal [e8d3b6f](https://github.com/morpheus65535/bazarr/commit/e8d3b6f0dd417e504ee80f3729634080f67d8d11)\n- Use new endpoint for gestdown [#1949](https://github.com/morpheus65535/bazarr/pull/1949)\n- Fix display issue of the popover in the manual search modal [56fb27a](https://github.com/morpheus65535/bazarr/commit/56fb27aab47f1551914390f319ca74c81b728e84)\n- Refactor form validation and fix issues [bb8e4f3](https://github.com/morpheus65535/bazarr/commit/bb8e4f31ee89291081dea646c918f02d595cfb66)\n- Fixed episodes subtitles search when notified of import by Sonarr v3 SignalR feed. #1946 [c492de8](https://github.com/morpheus65535/bazarr/commit/c492de8b670e11c7c5a33e57dd10dca04093c18e)\n- Added update mechanism sooner in the startup process to recover from a failed update more easily once a fixed release is available. [d3defa2](https://github.com/morpheus65535/bazarr/commit/d3defa2e09e93ef4340f740f9acfa327914b5c42)\n- Fix validation issues of the language profile editor [4826cb8](https://github.com/morpheus65535/bazarr/commit/4826cb84875a3aadeaae1e01eac0baef3dc16fad)\n- Fixed import error after last commit. [a338de1](https://github.com/morpheus65535/bazarr/commit/a338de147e8a4d74ca266b1306997fcfc90b8941)\n- Added Swagger documentation for Bazarr API [131b4e5](https://github.com/morpheus65535/bazarr/commit/131b4e5cde4034f78923d7eaebd49b3550f8aa13)\n- Upgrade Mantine to Version 5 [#1930](https://github.com/morpheus65535/bazarr/pull/1930)\n- Upgrade outdated packages [cce440a](https://github.com/morpheus65535/bazarr/commit/cce440a8e2b67123f0109d20fbedd5320a9603de)\n- Embedded Subtitles provider: add support for alpha2 tags [803dfe3](https://github.com/morpheus65535/bazarr/commit/803dfe351201ae602d9f3e96cfb01d79604296fc)\n- Fixed opensubtitles.com authentication issue. #1935 [663e8d7](https://github.com/morpheus65535/bazarr/commit/663e8d73958cc26233a649ea1cea7c3a53c5bd2c)\n- Added warnings in Settings--&gt;Providers for broken providers. [61f888c](https://github.com/morpheus65535/bazarr/commit/61f888c3dfa732c2f511de60732d46bff80bf511)\n- Fixed incorrect IP or already used TCP port from preventing Bazarr initialization. [7a2d239](https://github.com/morpheus65535/bazarr/commit/7a2d2399fc21ae2f67185e698a9bc0bf1d8fe60d)\n- Fix to deal with empty series/movies list during sync. #1941 [fa8ddeb](https://github.com/morpheus65535/bazarr/commit/fa8ddeb2aa41942e69f31c0b62869ef9a314f860)\n- Fix #1938\n- Embedded Subtitles provider: improve ASS subtitles filtering [6e52114](https://github.com/morpheus65535/bazarr/commit/6e521143e1de1f720dd9cf908aa7c907696187f0)\n- Ignore mods for non-tested subtitle formats [0bc0523](https://github.com/morpheus65535/bazarr/commit/0bc05232ed69be8dd727ce70e24e68e9d7362367)\n- Fixed improper parsing of series metadata when receiving an event from Sonarr SignalR feed. [9361243](https://github.com/morpheus65535/bazarr/commit/93612434e59a99f858a33667fae8835bb3473c53)\n- Improved exceptions logging for events received through SignalR feeds. [a880386](https://github.com/morpheus65535/bazarr/commit/a880386184c7b92dc7284487135c611482ca649e)", "date": "2022-10-15T12:44:34Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.1.2/bazarr.zip"}, {"name": "v1.1.1", "body": "From newest to oldest:\n- Avoid error trying to scan some videos [09d1eb7](https://github.com/morpheus65535/bazarr/commit/09d1eb78c95d2a1bee85f8e17cd9877149e26afb)\n- Improved the fix uppercase mod to better deal with non-alphabetical characters [2608278](https://github.com/morpheus65535/bazarr/commit/26082780d579bd1ffdca0e8c4bef393966db982c)\n- Improve authentication and fix #1901\n- Fix #1917, bring back sceneName in manual search modal\n- Improved API to return better message/code on errors [c2c0cbd](https://github.com/morpheus65535/bazarr/commit/c2c0cbda0b85b541e96b95ae828525c75c41ec4d)\n- Add support of showing confirmation when leaving settings page with unsaved changes [8eef455](https://github.com/morpheus65535/bazarr/commit/8eef455cc59193be0266612c42a66e5b413b3c38)\n- Added support for CORS headers [0d39928](https://github.com/morpheus65535/bazarr/commit/0d39928a7b7b82d933d74c2593b04326b21774f9)\n- Added download link for backup archives. [a36b3ce](https://github.com/morpheus65535/bazarr/commit/a36b3ce2ba9f8e9810fcfa6b0720c5e4baf5f818)\n- Improved live synchronization with Sonarr and Radarr trough SignalR feed by implementing dedicated threaded events queues, by preventing execution of duplicate events received, by filtering events to be processed and by logging at debug level events received to improve debugging. [180a40e](https://github.com/morpheus65535/bazarr/commit/180a40e027a8a5e4e77d3ce4c17ffe37a9f3050f)\n- Fixed None type issue on the first subtitles indexation for episodes or movies. [c6efda0](https://github.com/morpheus65535/bazarr/commit/c6efda0f75e338c72c76a26d33920267cc15a293)\n- Update outdated node packages [bfd1af7](https://github.com/morpheus65535/bazarr/commit/bfd1af7c0cf0211abc49532bba22870856461d9d)\n- Fixed compatibility with the latest nightly version of Sonarr v4. [0fb9286](https://github.com/morpheus65535/bazarr/commit/0fb928675c2f020ecf34faecb12603a4e669eaae)\n- Avoid 'Insufficient data to process the guess' error (fix #1910)\n- Fix issue in external subtitles cache. #1916 [bc0a7c2](https://github.com/morpheus65535/bazarr/commit/bc0a7c2e0fa2f032a299151bdea76b97cfeae6ef)\n- Tried to improve full disk daily indexing tasks resources usage. #1916 [c74f9e9](https://github.com/morpheus65535/bazarr/commit/c74f9e9602ed3c426e4e01ffc4acef44a127168b)\n- Fixed integer conversion issue. #1918 [b0abe81](https://github.com/morpheus65535/bazarr/commit/b0abe81d1277bb14f877909541d56b033874f8e0)\n- Fixed double slashes stored in config.ini by sanitizing the config values. #1903 [4382a05](https://github.com/morpheus65535/bazarr/commit/4382a05da1ad0864b74acdf911c591f3c80d6918)\n- Fixed subtitles upgrade job trying to upgrade null scored subtitles (part 2). #1909 [ba1687a](https://github.com/morpheus65535/bazarr/commit/ba1687a478ea2c7a647d6308979eab2c114efdc0)\n- Fixed episodes or movies importation skipped when Sonarr/Radarr improperly report that media file size is null. [81e13e1](https://github.com/morpheus65535/bazarr/commit/81e13e1eac1d2bdbd9ee10ca79d59feb01d09159)\n- Fix #1901 temporarily\n- Fixed subtitles upgrade job trying to upgrade null scored subtitles. #1909 [414900c](https://github.com/morpheus65535/bazarr/commit/414900c24e082d0758b88f6117ddf09d954bdf44)\n- Fix display issues of the base URL [af8a14c](https://github.com/morpheus65535/bazarr/commit/af8a14c92bbd79f4a0801bd975b6e9fb08d5fcf0)\n- Embedded Subtitles provider: add support for unknown language tags [5624ae4](https://github.com/morpheus65535/bazarr/commit/5624ae431ea6e1d29a0c9fd909949e1021dc1226)\n- Fix #1904\n- Fix postprocessing escaping [#1907](https://github.com/morpheus65535/bazarr/pull/1907)\n- Allow searching when uploading subtitles #1898 [775f67b](https://github.com/morpheus65535/bazarr/commit/775f67b45e83ed62d9bd4f2cc068cde305c2c5a3)\n- Fixed quotation marks escaping in post-processing variables [#1882](https://github.com/morpheus65535/bazarr/pull/1882)\n- Added option to always use audio track for subtitle syncing [c622e1f](https://github.com/morpheus65535/bazarr/commit/c622e1f710920092773fe35e3ddb4a20a3958fbe)\n- Fix #1894\n- Fix #1893\n- Fix provider configs updates [a748903](https://github.com/morpheus65535/bazarr/commit/a748903dc4d8c73e30e4b642aae5534a09571477)\n- Fixed Zimuku provider to bypass yunsuo protection [b9ba99e](https://github.com/morpheus65535/bazarr/commit/b9ba99e189d9273b7f7451e7046f021d240265ac)", "date": "2022-08-31T02:43:17Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.1.1/bazarr.zip"}, {"name": "v1.1.0", "body": "From newest to oldest:\r\n- Fixed Zimuku provider to bypass yunsuo protection [b9ba99e](https://github.com/morpheus65535/bazarr/commit/b9ba99e189d9273b7f7451e7046f021d240265ac)\r\n- Fix #1872, refactor the settings builder\r\n- Fixed Titulky provider to fail silently if media is missing IMDB ID. [331cbbd](https://github.com/morpheus65535/bazarr/commit/331cbbd3bdb2966e7615539e46a08dee525ac51e)\r\n- Add Karagarga.in provider [d2b40bd](https://github.com/morpheus65535/bazarr/commit/d2b40bd781a2b116e3cd32d4aded3d666b2105dd)\r\n- Fixed uploaded subtitles not preserved when it should. #1879 [44ffac6](https://github.com/morpheus65535/bazarr/commit/44ffac67b3639ea954e744f97a44e360a341b345)\r\n- Embedded Subtitles Provider: major update [8086bce](https://github.com/morpheus65535/bazarr/commit/8086bce5c4daf314a45eaaed8591fb9974790d0e)\r\n- update outdated packages [34685b3](https://github.com/morpheus65535/bazarr/commit/34685b3d19960ee119f832a00264a667d777bfeb)\r\n- Add chips input for the provider builder [c45b172](https://github.com/morpheus65535/bazarr/commit/c45b1724157268b38f0a87071062ee4b170f76ca)\r\n- Fix issues when displaying Use Original Format in the language profile modal [2b88c9f](https://github.com/morpheus65535/bazarr/commit/2b88c9f60cce854d2b2d08696b89b1bc11c0ecd0)\r\n- Fixed DNS queries caching issue. [f543368](https://github.com/morpheus65535/bazarr/commit/f543368089f5cbb223621015c22ea068ad8c86a0)\r\n- Fixed LegendasDivx throttling reset time to prevent IP from getting blocked. [c8c815e](https://github.com/morpheus65535/bazarr/commit/c8c815e24095b98882ac58f9961e63b38b63a20c)\r\n- Fixed cutoff being reached for a language even if forced subtitles is available but not requested. [e1bd023](https://github.com/morpheus65535/bazarr/commit/e1bd02396ff4ddde8eb96a91dafc91ad81d15130)\r\n- Subdivx provider: avoid TypeError [14153d7](https://github.com/morpheus65535/bazarr/commit/14153d7688fcd7f00bd6f02f155308cd18050ac3)\r\n- Try to avoid guessit internal exception [#1858](https://github.com/morpheus65535/bazarr/pull/1858)\r\n- Fix redirect issues after logging in (#1857) [b724305](https://github.com/morpheus65535/bazarr/commit/b724305eb4dc22b469c5c5f2033d89de11b4aeef)\r\n- Fix missing images issue when serving via Flask [4b71b8a](https://github.com/morpheus65535/bazarr/commit/4b71b8ae65ac7709b902f6e9efd64dd2be4e5082)\r\n- Fix #1873\r\n- Fixed throttle expiration for providers that reset at midnight. #1863 [a79c218](https://github.com/morpheus65535/bazarr/commit/a79c2187deab7d3effc63516dc1f0007f934104b)\r\n- Improve error handling on UI [c3645c9](https://github.com/morpheus65535/bazarr/commit/c3645c90242ba6fabc62881534b138f098041398)\r\n- Fixed uptime in System--&gt;Status and added time zone info. [f2eb8f1](https://github.com/morpheus65535/bazarr/commit/f2eb8f13421019028ecef5583e296ac9eb8f285e)\r\n- Subf2m provider: improve episode matching [246c2f3](https://github.com/morpheus65535/bazarr/commit/246c2f30046540be6e49bbed4dafa7c7f643648e)\r\n- Add description icon in the history modal [738178a](https://github.com/morpheus65535/bazarr/commit/738178a17318390ba2f62d7cd05907544db80e80)\r\n- Add tooltip to HistoryIcon [23022f5](https://github.com/morpheus65535/bazarr/commit/23022f528260a976baa3668691561e2ce6300256)\r\n- Fix crash when navigating from history view and open the history modal [98937a0](https://github.com/morpheus65535/bazarr/commit/98937a03786f64bcde5a35ab085cb6926b0a7e5e)\r\n- Fixed Wizdom API url. #1870 [a7b1b2a](https://github.com/morpheus65535/bazarr/commit/a7b1b2a454b396c116299eccf00eef47094d850d)\r\n- Fix a issue when trying to edit profile with cutoff selected [fe09ae9](https://github.com/morpheus65535/bazarr/commit/fe09ae9de52c9fa6ba89b4d2d427c78300984d63)\r\n- Fixed root url redirect not working with base_url defined. #1857 [0f4af48](https://github.com/morpheus65535/bazarr/commit/0f4af48be6e4a7da67a08d239898e8224ed1e301)\r\n- Fixed hearing-impaired not taken into account when naming uploaded subtitles #1846 [2a29886](https://github.com/morpheus65535/bazarr/commit/2a298860a4a915099a9daa81658688927b1307dd)\r\n- Improve UI error view [0e7c541](https://github.com/morpheus65535/bazarr/commit/0e7c5410d4fd15dd3c0a9a1041013417b14f3490)\r\n- Change default open delay of action tooltips to 2 seconds [51d1c46](https://github.com/morpheus65535/bazarr/commit/51d1c46b18dce4d677422d1726ed177a3e81bfe3)\r\n- Change audio badge color [4481cda](https://github.com/morpheus65535/bazarr/commit/4481cda6005a653677334244aed9b19436979355)\r\n- Bring back search function of the provider selector #1864 [f09f353](https://github.com/morpheus65535/bazarr/commit/f09f3534a6aad77aaa5bf0395c0984a0bb2e2c48)\r\n- Add tooltip and improve hover behavior for embedded subtitles [a529cba](https://github.com/morpheus65535/bazarr/commit/a529cba08116d08eb63ad6c6c40b68fc2e1e5d9e)\r\n- Rename Tools to Mass Edit [2a5bf95](https://github.com/morpheus65535/bazarr/commit/2a5bf9538dfabbe5578838b6509b6dfdc5d97aef)\r\n- Add color to the icon in upload modal [ed819ce](https://github.com/morpheus65535/bazarr/commit/ed819ce299e2db2eeb00312ee1fec35fbf049be4)\r\n- Improve layout of the settings pages [afcb3b0](https://github.com/morpheus65535/bazarr/commit/afcb3b07768304d6b455b0c939aba0e1f9291e3e)\r\n- Add label to all action buttons [5a5df23](https://github.com/morpheus65535/bazarr/commit/5a5df23b9ccbd4a3787e9a608008f31bceafbb10)\r\n- Fix crash when saving language profiles [059ab59](https://github.com/morpheus65535/bazarr/commit/059ab59fc7cea7abdcc34b6fab16e5aac25e41dd)\r\n- Replace Bootstrap with Mantine [#1795](https://github.com/morpheus65535/bazarr/pull/1795)\r\n- Fix issues of CollapseBox in settings pages [cf15c26](https://github.com/morpheus65535/bazarr/commit/cf15c26f1b65a7d2faf5d4ebe503eb2d0b6a2461)\r\n- Improve layout of the settings pages [4d9d2de](https://github.com/morpheus65535/bazarr/commit/4d9d2de1fc6b994130a324017df76f689d755196)\r\n- not log: Fix base_url issue during development [03de446](https://github.com/morpheus65535/bazarr/commit/03de446691df759ab6002f7886b56ef073d3e7ac)\r\n- Add NO_CLI env var for tests [#1856](https://github.com/morpheus65535/bazarr/pull/1856)\r\n- Small fix when getting Python bundle path [#1854](https://github.com/morpheus65535/bazarr/pull/1854)\r\n- Fixed import order that was preventing Bazarr from starting after initial database creation. [c2f2ce1](https://github.com/morpheus65535/bazarr/commit/c2f2ce17779401bc69a930ce623e91d3067bb5f8)\r\n- Gestdown provider: improve http error handling [5a13046](https://github.com/morpheus65535/bazarr/commit/5a13046d580ffa42213aab53ac43abec1b41a8d2)\r\n- Gestdown provider: ignore incomplete subtitles [2528170](https://github.com/morpheus65535/bazarr/commit/25281709c1f1ffa22da22ac43ebf51d9627797ad)\r\n- Gestdown provider: implement retries on 423 status codes [0e98880](https://github.com/morpheus65535/bazarr/commit/0e98880494149801ac40821a4b0e89a7e2cb10b8)\r\n- Subf2m provider: add season pack support [848836c](https://github.com/morpheus65535/bazarr/commit/848836c5bb66ae4baa5edac3decfe65de4cb9e62)\r\n- Subf2m provider: improve matches parsing [4730411](https://github.com/morpheus65535/bazarr/commit/473041158c614182681b517ee7f3a1b3678019d0)\r\n- Subdivx provider: improve movie searches [0e1120e](https://github.com/morpheus65535/bazarr/commit/0e1120e037febedacb7a051871e9bd4660a8a0c8)\r\n- Add Gestdown.info Provider [c9d63f7](https://github.com/morpheus65535/bazarr/commit/c9d63f717f581661fa9df6436a77c75b6feca92e)\r\n- Added support for unar RAR extraction utility. #1833 [642733f](https://github.com/morpheus65535/bazarr/commit/642733f92f08c64cb048372d7ce92a4bede8dc84)\r\n- Embedded Subtitles provider: fix cached streams with HI fallback\r\n- Embedded Subtitles provider: improve HI fallback [b96fd93](https://github.com/morpheus65535/bazarr/commit/b96fd9326979b08876ef9818bfab0a735dcc0f06)\r\n- Fixed provider Supersubtitles following domain name change [c676095](https://github.com/morpheus65535/bazarr/commit/c676095783548fd96ab03834db1d746678f33c54)\r\n- Fix some foreign providers not being updated properly [5f0f654](https://github.com/morpheus65535/bazarr/commit/5f0f654efe069e51a575c05faf48432b2f2b1a39)\r\n- Improve provider configs updates [23a5ab9](https://github.com/morpheus65535/bazarr/commit/23a5ab9b0ea9cb582ea5574fd8cdd1c9169a7d85)\r\n- Updated apprise to the latest version. #1834 [1dff555](https://github.com/morpheus65535/bazarr/commit/1dff555fc8c9c8a9be29a91b3717fcf8b07cb648)\r\n- Prevent Python dock icon appearing on macOS [fcd67c1](https://github.com/morpheus65535/bazarr/commit/fcd67c1fb09889c8400751a0d4dabeb264681f0c)\r\n- Fixed Bazarr logo on form auth page missing with base_url. #1823 [695edad](https://github.com/morpheus65535/bazarr/commit/695edad3dd6eee5fb1176c4c20f433a7a682cc70)\r\n- Avoid NoneType on providers pool updates [a783515](https://github.com/morpheus65535/bazarr/commit/a783515ad4fc5f54a27c97a2955679c6555a5839)\r\n- Embedded Subtitles provider: improve detection of potentially incomplete subtitles [1346137](https://github.com/morpheus65535/bazarr/commit/134613711ac1ede510cfc023072f0712a175b858)\r\n- Fixed issue with indexing of external subtitles that include utf8 characters in file name. [b7e9086](https://github.com/morpheus65535/bazarr/commit/b7e908645ee7aa27f4631318a86c259e86b0ef75)\r\n- Fixed issues while trying to serve assets. #1822 [89fa25c](https://github.com/morpheus65535/bazarr/commit/89fa25cddf3f3932b8e55bee57d0bad287fe348d)\r\n- Improve subtitles pack extraction [ce33b4f](https://github.com/morpheus65535/bazarr/commit/ce33b4f5fd75450bbd54351c712b87dfe915bb29)\r\n- Subdivx Provider: improve language parser [7423c80](https://github.com/morpheus65535/bazarr/commit/7423c80a28b894812b121a89f38d045f7c87d5ca)\r\n- Fixed mimetype issues for some Windows users. [af95f70](https://github.com/morpheus65535/bazarr/commit/af95f702020a2d79fb6358eedd16b2849ada6136)\r\n- Reworked Bazarr file structure to improve support and optimization [2f01ab8](https://github.com/morpheus65535/bazarr/commit/2f01ab852348669e81c3d19b3a12f5084b04fba8)", "date": "2022-07-02T12:47:36Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.1.0/bazarr.zip"}, {"name": "v1.0.4", "body": "From newest to oldest:\n- Subdivx Provider: avoid false positives on episodes search [3f95784](https://github.com/morpheus65535/bazarr/commit/3f95784bb824484cb37bb0bfc91f491015f74cf4)\n- Fixed history stats time frame not working properly [4358790](https://github.com/morpheus65535/bazarr/commit/4358790fa7cb4ac130ac7501bfad34c753cbec98)\n- Embedded subtitles provider: improve exception handling\n- Fix #1817\n- Legendasdivx provider: improve subtitles ID consistency [23e8472](https://github.com/morpheus65535/bazarr/commit/23e847290bc5353cf3b6e21a1e81880cfc17e31d)\n- Fixed Titulky provider and made some code maintenance [e6551dc](https://github.com/morpheus65535/bazarr/commit/e6551dc4d397ac11b89f391349cd9ee75e77692d)\n- Improved search and fixed some issues with Titulky provider [a4d9eb6](https://github.com/morpheus65535/bazarr/commit/a4d9eb694d675c0b85ad7d1b88574241232d1a82)\n- Subdivx Provider: improve episode searching and downloading [883b0fe](https://github.com/morpheus65535/bazarr/commit/883b0fe7a15d121ea77d2f63f582438c93ad8d76)\n- Add subf2m.co Provider [63eded5](https://github.com/morpheus65535/bazarr/commit/63eded5aa38f2241fabbe99516064b941cf0d16d)\n- Improve episode detection from compressed series packs [75f3ac9](https://github.com/morpheus65535/bazarr/commit/75f3ac9f6cec587bc3b0bb3a41e9fd18d10cd02f)\n- SuperSubtitles Provider: fix filetype detection [31e4f83](https://github.com/morpheus65535/bazarr/commit/31e4f835cf28bd1f5c4840a958126fce046591ee)\n- Embedded subtitles provider: add timeout option [d3e3e31](https://github.com/morpheus65535/bazarr/commit/d3e3e31fa13bce4d8ea29c2c834b04e0b061e287)\n- Fix #1801\n- Supersubtitles provider: Fix AttributeError for some queries (#1792) [7040fbc](https://github.com/morpheus65535/bazarr/commit/7040fbc0711f55a433ea234100baee1c1df8b816)\n- Removed support for BSplayer providers because it was causing way much issue than providing subtitles. [77da09d](https://github.com/morpheus65535/bazarr/commit/77da09d51f549470c0e9be2eb670de74450dd791)\n- Fix ValueError for persistent pools on multithreaded setups [86d6c21](https://github.com/morpheus65535/bazarr/commit/86d6c211af8ac0b81e05937fea1973ec78bc9226)\n- Fixed issue with same subtitles downloaded multiple times. #1785 [4f42cd9](https://github.com/morpheus65535/bazarr/commit/4f42cd9b2860856b3759cd438d9fdcfef5a5fdba)\n- Fixed Plex webhook for series by having a more robust imdb parsing nd some logging in case of exception. #1780 [8e07585](https://github.com/morpheus65535/bazarr/commit/8e075850849a1855386e13f8ec576f3afe84ddf4)\n- Improve subtitle tools [e18657e](https://github.com/morpheus65535/bazarr/commit/e18657e4261cae67d6fe5a235a001dede26721c5)\n- Refactor modal system [658237d](https://github.com/morpheus65535/bazarr/commit/658237dd5076a3d4823552ad17c101d3ba6177fc)\n- Fix notifications settings cannot be edited after saved [87c5d0d](https://github.com/morpheus65535/bazarr/commit/87c5d0d9defdc3f01865eeb844dfe191934411fb)\n- Fixed update mechanism when running Bazarr from %programdata%\\Bazarr on Windows. #1768 [5539938](https://github.com/morpheus65535/bazarr/commit/55399380ada67fae9b98a5ea9884aea08e475e7e)\n- Fix #1778\n- Added defer searching missing subtitles on live synchro with Sonarr and Radarr. #1765 [012dc1c](https://github.com/morpheus65535/bazarr/commit/012dc1cee977e4fb0b72a955736476f748c8314b)\n- Fix #1770\n- Fix #1769\n- Fix interactive search cache issues [d964f04](https://github.com/morpheus65535/bazarr/commit/d964f04c2764bb55cca382e3ecb0ea53e96974e3)\n- Fix history button for episode will open up wrong modal [b4c82c2](https://github.com/morpheus65535/bazarr/commit/b4c82c269ee637e566a3369fd10afe26bc5877ff)\n- Improve feedback in the header [51e1fa1](https://github.com/morpheus65535/bazarr/commit/51e1fa1c35830d2224ea7589730a645d111a5b63)\n- Fix subliminal core: wrong providers being restarted [8c4e83c](https://github.com/morpheus65535/bazarr/commit/8c4e83c18d56c96a036bbe8aa1cd268448099766)\n- Fix display issues in the notification center [aaf4b7c](https://github.com/morpheus65535/bazarr/commit/aaf4b7c6731c35252a7d89cf8cac64eb54d0b064)\n- Fix issues when trying to create background tasks [c1a26fd](https://github.com/morpheus65535/bazarr/commit/c1a26fd9ebd601417fbfe975bf148485384c6475)\n- Frontend improvement and cleanup [#1690](https://github.com/morpheus65535/bazarr/pull/1690)\n- Improved languages profile modal original format dropdown [75100d8](https://github.com/morpheus65535/bazarr/commit/75100d8acacf8f55b4a5ca0433f465071273dec8)\n- Fixed upgrade subtitles function that was trying to upgrade deleted episode/movie subtitles. #1759 [f81972b](https://github.com/morpheus65535/bazarr/commit/f81972b291b73f5771c40359f18d6470b23e2650)\n- Added languages profile settings to prevent downloaded subtitles from being converted to srt. [33a9e51](https://github.com/morpheus65535/bazarr/commit/33a9e512acafe0419d553660c20659d007a23b85)\n- Fixed Invalid IPv6 URL exception with common fix [6edc196](https://github.com/morpheus65535/bazarr/commit/6edc196163061ae233324bacbf918886159f4efb)\n- Fixed backups ordering and rotation [eb63f05](https://github.com/morpheus65535/bazarr/commit/eb63f057684e7c2842ba9dd5a9a658e3e9f8edaf)\n- Improved throttling of LegendasDivx provider to prevent IP address blocking by reaching 150 searches a day. #1757 [040ddb2](https://github.com/morpheus65535/bazarr/commit/040ddb236269c7a27d5d4f9c7fe708e53caba72f)\n- Fixed log rotation under Python 3.9 or greater. [2b889fa](https://github.com/morpheus65535/bazarr/commit/2b889fa975ba234377500dd55022cce35eb3d92a)\n- Fixed titulky provider UnicodeEncodeError [8a9fbbe](https://github.com/morpheus65535/bazarr/commit/8a9fbbeaece5099e6bcac708a1fed8a80de5a2b7)\n- Fixed network issues from preventing Bazarr startup. [d384b93](https://github.com/morpheus65535/bazarr/commit/d384b9307bf86845e4465628b3c6fc138f14d9c6)\n- Implemented number input and used it with backup retention and port settings. [0166d9a](https://github.com/morpheus65535/bazarr/commit/0166d9a0dddbbd3c862c25a3c75c62b2ef48db35)\n- Fixed knowit provider specification. [ce1345a](https://github.com/morpheus65535/bazarr/commit/ce1345a8702c6f90c919174cbff04741802c078e)\n- Fixed subtitles translation when there's an empty string in the source file. [d13bc73](https://github.com/morpheus65535/bazarr/commit/d13bc731891d8ea211b9e5f1d95f10c4e0ba170c)\n- Fixed GetSonarrInfo.version() to make sure it doesn't prevent Bazarr startup. [4b84a9c](https://github.com/morpheus65535/bazarr/commit/4b84a9c64c90d8872266889443ffaa0e44465087)\n- Fixed opensubtitlescom provider to remove filename from query because it was too specific and reduced number of subtitles returned for no good reason. [bd5aae7](https://github.com/morpheus65535/bazarr/commit/bd5aae7f77608a6f3ca80d66e17a5e4c7de932bb)\n- Fixed cache invalidation on providers' password changes. [c2f7446](https://github.com/morpheus65535/bazarr/commit/c2f74465e9c079e288656bfce04e052dd52dd787)\n- Added backup file size to API and table view. [e4c666f](https://github.com/morpheus65535/bazarr/commit/e4c666f82b2dacc7d2d969a3b9ffed54567604e8)", "date": "2022-04-30T13:07:32Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.0.4/bazarr.zip"}, {"name": "v1.0.3", "body": "From newest to oldest:\n- Implemented garbage collection after each subtitles synchronization. [0a0f609](https://github.com/morpheus65535/bazarr/commit/0a0f609de89d2d93d594067f9c803f9449b89071)\n- Reverted auditok to ffsubsync supported version [c23ce4a](https://github.com/morpheus65535/bazarr/commit/c23ce4a4d1929f9b007b64e81f21fcf881e041fb)\n- Fix a issue that seleection of the languages profiles could be Unknown on settings page [63f1e63](https://github.com/morpheus65535/bazarr/commit/63f1e63fa83fbf789061e58652d157f8965ff527)\n- Implemented backup and restore feature for configuration file and database [5bfaba9](https://github.com/morpheus65535/bazarr/commit/5bfaba9360f062965e672dd336fe82208c716d13)\n- Fixed improperly calculated missing subtitles when cutoff is enabled and audio language match desired subtitles language. #1731 [c020a9e](https://github.com/morpheus65535/bazarr/commit/c020a9e89214fa482c7ad5d4071c006ffcf0b1cc)\n- Fix a issue that language profiles won't show in some situations [4bf0555](https://github.com/morpheus65535/bazarr/commit/4bf0555ece29fad429eb967711007d1fb29abd82)\n- Improve Argenteam results matching (Covers #1728) [644617c](https://github.com/morpheus65535/bazarr/commit/644617c7c2698eb8fee4aea4575982e5ccca3021)\n- Clarified SignalR error for Sonarr [ffcbb57](https://github.com/morpheus65535/bazarr/commit/ffcbb57672872b514ff13ea072dfea730ec15bee)\n- Fixed addic7ed subtitles search query to return all languages instead of relying on filter defined in addic7ed profiles. [a22bb0f](https://github.com/morpheus65535/bazarr/commit/a22bb0fd124f7951be4d328740a356c0e1951c9c)\n- Fixed legendastv issue since rarfile upgrade. #1711 [23e2626](https://github.com/morpheus65535/bazarr/commit/23e2626df9fd3cc219eb8b4f612e70d1efe86f5f)\n- Fixed external subtitles with unknown language causing exception during indexing. #1709 [d160f1e](https://github.com/morpheus65535/bazarr/commit/d160f1e4eb633560d7b9128ed9908fe032c0c5b6)\n- Improved sync with Sonarr/Radarr exceptions management when trying to write to database. #1699 [6988d7c](https://github.com/morpheus65535/bazarr/commit/6988d7c7ad64b2bf6b52d7728f288a8f19bbb78c)\n- Added cookies caching to titulky provider [45f085e](https://github.com/morpheus65535/bazarr/commit/45f085eb5dc7a31a411f4cbb93f96cc214bc7607)\n- Fix database issue with LATAM Spanish custom language [a53fc44](https://github.com/morpheus65535/bazarr/commit/a53fc440cdff7ed19ead50a0f81fa036d9debfe4)\n- Fixed JSON parsing exception in supersubtitles provider. #1700 [83e36e4](https://github.com/morpheus65535/bazarr/commit/83e36e4c1c58a73966e4d0f924e071b73e8e00ec)\n- Improved opensubtitles.com resilience and properly deal with the Invalid JSON/Bad Gateway exception. [669bd33](https://github.com/morpheus65535/bazarr/commit/669bd3376ad06f12cb152078c5664c03aa7c5004)\n- Fixed progress not being removed from control center when a search all fails. [63f3454](https://github.com/morpheus65535/bazarr/commit/63f3454c8fb25e76c9c6baf6ff2951ca0011afde)\n- Implemented the functions to read more info from package_info file [c91597f](https://github.com/morpheus65535/bazarr/commit/c91597fdd55049cdc0f69f3137393dc7c4ee8717)\n- Fixed Windows uninstaller being removed during Bazarr upgrade. #1693 [e99d58d](https://github.com/morpheus65535/bazarr/commit/e99d58d77e4c1eada8584f4459e535c91086f964)\n- Improved retry and exception handling in opensubtitles.com provider. [d879128](https://github.com/morpheus65535/bazarr/commit/d879128dcdaa1dc2f25e19816dfebd872bcd04ed)\n- Fixed issue with cutoff and exclude language exclusions were not working as expected. #1691 [aa0c2ff](https://github.com/morpheus65535/bazarr/commit/aa0c2ffca76b75a71847f02f56037bf6414732bd)\n- Fixed missing tzdata module. #1693 [c7500c1](https://github.com/morpheus65535/bazarr/commit/c7500c1d04b8f81e7058ed66fd1db5dbbcdad210)\n- Upgraded vendored Python dependencies to the latest versions and removed the unused dependencies. [0c3c5a0](https://github.com/morpheus65535/bazarr/commit/0c3c5a02a75bc61b6bf6e303de20e11741d2afac)\n- Add Embedded Subtitles mergerfs mode [4c15a50](https://github.com/morpheus65535/bazarr/commit/4c15a50134ef7f6bc839ad51275c10b2f2141917)\n- Fix a issue that the edit modal will clear the languages profiles if without changing anything [dc6bd1f](https://github.com/morpheus65535/bazarr/commit/dc6bd1fd1b2ad477f1769664bade46868551ebf8)\n- Add React-Query to improve network and cache performance [d8d2300](https://github.com/morpheus65535/bazarr/commit/d8d2300980ca69a4ae6511cb49a6dc548c0da793)\n- Fixed issue where episode name was used as hints for guessit instead of series name. [6b82a73](https://github.com/morpheus65535/bazarr/commit/6b82a734e2bc597b219472774c0ec58038630c65)\n- Fixed translation issue when first line is an empty string. #1672 [391892f](https://github.com/morpheus65535/bazarr/commit/391892fdf0450cb30c6ce95ff0c006f6bcaf5a8f)\n- Fixed issue with unrecognized languages while indexing subtitles (mainly for those without a language code in filename). [28663a6](https://github.com/morpheus65535/bazarr/commit/28663a69379d99876547c652ee8e87416e8e6b90)\n- Fixed upgrade leftover cleanup to prevent config reset when Bazarr is installed in the same directory as config and database. #1655 [a3a8ed9](https://github.com/morpheus65535/bazarr/commit/a3a8ed93c7754a288957f6712ee1002f173bf4d4)\n- Fixed indexing of unsupported language codes. #1683 [9d30414](https://github.com/morpheus65535/bazarr/commit/9d30414f9dd1797f17149e403d56f6b0b368a47d)\n- Fixed translation exception on malformed subtitles file. #1672 [3b3d6fe](https://github.com/morpheus65535/bazarr/commit/3b3d6fe36aa33defa694e083713e78c0e4e84cb8)\n- Added some failsafe to deal with improper JSON returned from Radarr tags API endpoint. [62ef614](https://github.com/morpheus65535/bazarr/commit/62ef614d301de5cb52eb3e5f410a7acecdee984c)\n- Fixed issue with deleted episode file raising a 404 while trying to update from Sonarr. #1676 [8764ac5](https://github.com/morpheus65535/bazarr/commit/8764ac581e9d8ba7712e1848090b8db92f591c47)\n- Improve Subdivx provider: use a random cookie to speed up downloads and [8ad4ec9](https://github.com/morpheus65535/bazarr/commit/8ad4ec95f98b5f22dd40dd681a3d42014ace62c1)\n- Fixed issue with addic7ed where alternative titles were not used properly in during searches. #1669 [dbf0bf2](https://github.com/morpheus65535/bazarr/commit/dbf0bf26389990c67dde3f6c858774558b345f27)\n- Added settings to change the hearing-impaired subtitles file extension to use when saving to disk. [58a967c](https://github.com/morpheus65535/bazarr/commit/58a967c892ef78cb6cf8ab790c2af8728e8c079e)\n- Added cookies option for addic7ed to avoid anti-captcha provider requirement [1a03d9f](https://github.com/morpheus65535/bazarr/commit/1a03d9fd12e601a072aeb3d30e3e3693373d841d)\n- Upgraded guessit to latest version and include new dependencies. [f55492a](https://github.com/morpheus65535/bazarr/commit/f55492a25cf617604ff158d1fc516cf7298a4adc)\n- Fixed logging of regex pattern used during upgrade leftover cleanup. #1655 [5d3d453](https://github.com/morpheus65535/bazarr/commit/5d3d4532df65e82fc5477fb570f472edcb7c1ea5)\n- Improved readability and maintenance by splitting get_subtitle.py into multiple files [722014d](https://github.com/morpheus65535/bazarr/commit/722014d822fd0b8e62ca0c2b96965e50e7487a56)\n- Added validation for V.I.P. account status in titulky provider [3653ef1](https://github.com/morpheus65535/bazarr/commit/3653ef163fb76b716d2a00522c6faf9593697003)\n- Add MustGetBlacklisted exception for redundant download_subtitle calls [1261e91](https://github.com/morpheus65535/bazarr/commit/1261e91870a6e08b1942c7a2fbcb74b19d0da4c9)\n- Add hi_fallback option to Embedded Subtitles Provider [b90dab0](https://github.com/morpheus65535/bazarr/commit/b90dab03e89e03b73bfbe15741c8108fc0d2a806)\n- Added support for hearing-impaired version of CustomLanguage [4af4ae1](https://github.com/morpheus65535/bazarr/commit/4af4ae1ae8b4d7ba4df7c36c4d72b40e576c6fec)\n- Subdivx provider: Improve scraper [d373bc8](https://github.com/morpheus65535/bazarr/commit/d373bc8c0e936e8fecaf54cf2e122b0a0cfeca9f)\n- Fix broken Subdivx provider: update scraper [75926dc](https://github.com/morpheus65535/bazarr/commit/75926dc7763fb207e9560b790b154565016122e1)\n- Improved opensubtitles.com server-side error handling [4605164](https://github.com/morpheus65535/bazarr/commit/46051646c620d7db37f0be56df8b9f20c1b42d83)", "date": "2022-02-26T15:03:11Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.0.3/bazarr.zip"}, {"name": "v1.0.2", "body": "From newest to oldest:\r\n- Added support for Python 3.10 in startup warning. [6e52ce2](https://github.com/morpheus65535/bazarr/commit/6e52ce2ecfaad8db92d8bd7d75048fc8b36e0aa9)\r\n- Embedded Subtitles provider: fix language error (fix #1653) [e65d643](https://github.com/morpheus65535/bazarr/commit/e65d643fae345c18cf4ffd2422dafe9500b88f93)\r\n- Added exclusion settings for season 0 to prevent automatic downloading of extras subtitles. [bd7b2eb](https://github.com/morpheus65535/bazarr/commit/bd7b2eb471e86cc3e2ce67fd09707ed188b3bcb0)\r\n- Refactored adaptive searching and made it configurable [10c3175](https://github.com/morpheus65535/bazarr/commit/10c317573292f3534cdc861f13e8489fa299fd5d)\r\n- Fix Addic7ed provider: handle language errors, avoid guessit exception and match version for movies [5d8897f](https://github.com/morpheus65535/bazarr/commit/5d8897f675eed5a9ec0d5b289b37e82741ae33dd)\r\n- Fix Addic7ed provider TypeError [a88f0a7](https://github.com/morpheus65535/bazarr/commit/a88f0a7f198faf1fe2de139a7fb49da41c0ee658)\r\n- Update embeddedsubtitles provider: use original_path instead of name [048307e](https://github.com/morpheus65535/bazarr/commit/048307e4299106ea2340bed72fce8207859d5fa9)\r\n- Fixed issues with release_info for some providers. #1644 [2faed98](https://github.com/morpheus65535/bazarr/commit/2faed982d95e2f5eac41caaca090ea6ce9f6115b)\r\n- Improved providers throttling and prevent hammering providers by updating throttled providers on each iteration of the loop. [5ff3fe4](https://github.com/morpheus65535/bazarr/commit/5ff3fe46845f4ed98c446b6f265c3a2a39ac825a)\r\n- Added vip and daily download limit to addic7ed and try to slow down a little to prevent being ip blocked by the provider. [5d1dccd](https://github.com/morpheus65535/bazarr/commit/5d1dccde995138e631916dcef44a7527b7cdc6b6)\r\n- Fixed exception when creating a new languages profile. [6192df6](https://github.com/morpheus65535/bazarr/commit/6192df6019bcd1c22e1c3ae2da4f83957b8194a5)\r\n- Embedded Subtitles Provider: rebuild language if forced [b5c66e1](https://github.com/morpheus65535/bazarr/commit/b5c66e1470991ac6896b311096a78c8bef21ef9e)\r\n- Update Embedded Subtitles provider: add forced and ass-only support [9b74ad8](https://github.com/morpheus65535/bazarr/commit/9b74ad8610f35275e76b9374643d768c6ef8df53)\r\n- Add Embedded Subtitles provider [ffca84a](https://github.com/morpheus65535/bazarr/commit/ffca84a1b435c479a6e91f37456b7eed95c730f5)\r\n- Add fese module for embedded subtitles [409e1a5](https://github.com/morpheus65535/bazarr/commit/409e1a585428f3d6b44472f8a5a57cb569d732f1)\r\n- Add default attribute to Subtitle [718bd2f](https://github.com/morpheus65535/bazarr/commit/718bd2f8b9b5ed470379381cb51773a3ddc9944f)\r\n- Implemented words/regex ban list for subtitles [63b326a](https://github.com/morpheus65535/bazarr/commit/63b326aa2f12df482f9537a0fec2f7755a152bfc)\r\n- Fixed throttled providers badges in UI throwing exception. #1631 [ca8f3f9](https://github.com/morpheus65535/bazarr/commit/ca8f3f9fd2ae21360549eb80a76d0291d41f96c5)\r\n- Fix hash score in manual search [2cde268](https://github.com/morpheus65535/bazarr/commit/2cde2686fc68a9e9c2d01f3c8a9858bffc5bd225)\r\n- Cleaned up and split API to make it easier to maintain. [204a1c3](https://github.com/morpheus65535/bazarr/commit/204a1c3f3192dcac6688e4a48ea8dd814ffccefe)\r\n- Updated Apprise notification module to the latest providers. [d51dc68](https://github.com/morpheus65535/bazarr/commit/d51dc68ebb3910ca09bb40c33814d43b93d916b8)\r\n- Upgraded some embedded dependencies to be ready for Python 3.10. This doesn't mean that it's fully supported right now. [402c82d](https://github.com/morpheus65535/bazarr/commit/402c82d84f7bd51353348bea7d1a876ad9ecc5b1)\r\n- Added support for upcoming Sonarr v4. [2d214bf](https://github.com/morpheus65535/bazarr/commit/2d214bfbd5f9d1598c01b2e2dd35efa67ccb43af)\r\n- Fixed missing poster or fanart url from raising an exception when rendering UI. [9b273a7](https://github.com/morpheus65535/bazarr/commit/9b273a7a8976f63ff932c1a0cb0be38a98956d05)\r\n- Fix Hosszupuska provider not returning results [247f69c](https://github.com/morpheus65535/bazarr/commit/247f69c210531048186c699de240f5e860ef0b3f)\r\n- Upgraded engine.io module to improve socket.io connection stability. Should help to prevent #1613. [c60c751](https://github.com/morpheus65535/bazarr/commit/c60c7513a5a776b2a15ac3a7b463d0ef9875cf04)\r\n- Improved sanitize function to replace apostrophe and backticks [592dc79](https://github.com/morpheus65535/bazarr/commit/592dc799e967cb68e60ce51ca9295591fc895c01)\r\n- Updated deep-translator module and made some fix to support translation to Chinese. There's still a bug in this module that prevent it but one it's fixed, it should be fine. [f082895](https://github.com/morpheus65535/bazarr/commit/f0828959f3e2561bc3252cb79b2570c6d3122b03)", "date": "2021-12-30T11:51:44Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.0.2/bazarr.zip"}, {"name": "v1.0.1", "body": "From newest to oldest:\n- Improved Titulky provider [d8fffe8](https://github.com/morpheus65535/bazarr/commit/d8fffe8e5206e640689a87ba6fc28b7cadface58)\n- Fix for #1592 [cbd6c05](https://github.com/morpheus65535/bazarr/commit/cbd6c050c9e6604a8215e8e581f25a5350581730)\n- Fixed issue with non-existent episode or movie when indexing embedded subtitles. #1606 [6f17100](https://github.com/morpheus65535/bazarr/commit/6f17100ca4b0ea8be445dad81de775cb56840c28)\n- Implemented some utility functions to subliminal_patch providers to list supported languages and video types. [b53f8ad](https://github.com/morpheus65535/bazarr/commit/b53f8ad80abff1410918bd3b86157d01b08fee6d)\n- Fixed some issues in opensubtitles.com provider. #1602 [faa368b](https://github.com/morpheus65535/bazarr/commit/faa368b6620be68bcf1a3a8872b9305e0e533117)\n- Added uptime to System--&gt;Status [55b7c98](https://github.com/morpheus65535/bazarr/commit/55b7c9826b8b830bcbecc394f1301719c2939064)\n- Improved LegendasDivx provider to get more accurate results and prevent unnecessary throttling [f53ef40](https://github.com/morpheus65535/bazarr/commit/f53ef40d57d4cf23fcf4410028a77bea318b6415)\n- added more improvement to Titulki provider [618bdde](https://github.com/morpheus65535/bazarr/commit/618bddebf97fd962a89a6c9d1f11c19733fbb4d7)\n- Fixed some issues with Titulki provider [006e17b](https://github.com/morpheus65535/bazarr/commit/006e17bdc2541d9eb9022e99b4da96d5ece04f24)\n- Fixed movies missing subtitles computation when there's a forced subtitles track/file. [3bc7002](https://github.com/morpheus65535/bazarr/commit/3bc700225fc69737b466ed560519696094aa987d)\n- Fixed Radarr API call to get version with the latest nightly. [e6c1dba](https://github.com/morpheus65535/bazarr/commit/e6c1dba37bc9351c10612924d56177107d6a46a6)\n- Improved results with Titulki provider [0e50335](https://github.com/morpheus65535/bazarr/commit/0e50335e2d26597cac20ee5264a78ea9c53e7ce0)\n- Fixed Brazilian Portuguese audio language match with Radarr. [aaa8b48](https://github.com/morpheus65535/bazarr/commit/aaa8b48dad3a1d4de5a8f2f04fcc96cf1fb5fb3a)\n- Fixed notification removal from settings. #1594 [58de876](https://github.com/morpheus65535/bazarr/commit/58de876f193e8358836b8fd12d25bc7e9d65005c)\n- Refactored the Titulki provider [801ff0d](https://github.com/morpheus65535/bazarr/commit/801ff0d47895a3fb00a9aa190a2ca1eaac55100a)\n- Removing TuSubtitulo per provider request. #1591 [3d936ff](https://github.com/morpheus65535/bazarr/commit/3d936ffccf767d0a91cbc3b7b3c05306e957e561)\n- Updated apscheduler to it's latest version. [c12c7b4](https://github.com/morpheus65535/bazarr/commit/c12c7b4064c6dae8e06f91d6443b78089a99e793)\n- Moved back from gevent to waitress web server. This should prevent UI disconnection occurring during heavy task like syncing subtitles. [39fe314](https://github.com/morpheus65535/bazarr/commit/39fe3141d51b01479e7b585ad0b5ee5df1767226)\n- Added More Equivalent release groups [8bdf957](https://github.com/morpheus65535/bazarr/commit/8bdf957c066e6f7d453e2164b0f9708ed09fedc2)\n- Fix for series progress bar not updated when a subtitles file is downloaded or deleted. [878a271](https://github.com/morpheus65535/bazarr/commit/878a271a4275258c80912226f7e97ba26f054307)\n- Fixed TuSubtitulo provider [08c797c](https://github.com/morpheus65535/bazarr/commit/08c797c484d93c02d4db2bfa3f702e3926121b9e)", "date": "2021-11-19T01:45:29Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.0.1/bazarr.zip"}, {"name": "v1.0.0", "body": "From newest to oldest:\n- Added subtitrari-noi.ro and improved titrari.ro [788b4b3](https://github.com/morpheus65535/bazarr/commit/788b4b33f0adf856f7066d5f0a325a879bb2b927)\n- Improved language selection for Titrari.ro provider. [0bc2605](https://github.com/morpheus65535/bazarr/commit/0bc2605439184fba36fcfd8e4a61c4b664a827e8)\n- Fixed Ktuvit problem with default empty return values [a819fc3](https://github.com/morpheus65535/bazarr/commit/a819fc34b36a45b21fee6def70003412ca66d41f)\n- Improved support for series for Titrari.ro provider [b1af4ca](https://github.com/morpheus65535/bazarr/commit/b1af4caeebdb9b98e098146afe72b9ef4e781ea6)\n- Added support for series to provider Titrari.ro [294d3eb](https://github.com/morpheus65535/bazarr/commit/294d3eb0eefdd5aad177d4ea08f57eebd9e7bf02)\n- Added a settings to disable SSL certificate validation for Podnapisi. Be careful as it's causing a security risk for a man in the middle (MitM) attack. #1565 [d851c16](https://github.com/morpheus65535/bazarr/commit/d851c16da7e7063fb5ad89993a5933de2bfd88f1)\n- Improved description of proxy ignored addresses. #1566 [9fff275](https://github.com/morpheus65535/bazarr/commit/9fff275f1ced4f77d297aa1775ca72b6067e1c83)\n- Fixed titrari.ro provider [17dd5dd](https://github.com/morpheus65535/bazarr/commit/17dd5ddde5bd9a67f9c3ffea3d9e485927b824e2)\n- Make debug call more verbose [385461d](https://github.com/morpheus65535/bazarr/commit/385461d28cd6c9c7838b2f7fda5ca601a89f7c83)\n- Fixed titrari search endpoint again. #1564 [036600f](https://github.com/morpheus65535/bazarr/commit/036600f15061653ce04a6b16493a14bb71e028a5)\n- Improved opensubtitles.com providers. [679f1e8](https://github.com/morpheus65535/bazarr/commit/679f1e803c75ec6660b16580323db0a8fb250f85)\n- Fixed multiple episodes subtitles upload to use the first episode number found. #1562 [56ffa72](https://github.com/morpheus65535/bazarr/commit/56ffa722e5c0b89e49601993738621511a907e32)\n- Fix Missing subtitles issue with Ktuvit provider [66585ec](https://github.com/morpheus65535/bazarr/commit/66585ec7654d5ba5633f79d5cc51ca18807619a4)\n- Improved queries to opensubtitles.com to reduce footprint on their side. [c1421f1](https://github.com/morpheus65535/bazarr/commit/c1421f1cce7b2553dbcb2f60954203b0293a8e3b)\n- Fixed issue with movies on Addic7ed. #1550 [6d082f8](https://github.com/morpheus65535/bazarr/commit/6d082f8b388341ce70dfb647efb88b0964651f4b)\n- Add titulky.com provider to the UI [8d698fa](https://github.com/morpheus65535/bazarr/commit/8d698fa7e4972f86c09b6d3fda1c3bb4f5787e8c)\n- Prevent broken release (missing asset) from corrupting existing installation. [36a2309](https://github.com/morpheus65535/bazarr/commit/36a2309bcaa2d7ea6ba015b6c6a39884ca2a947f)\n- Fixed opensubtitles.com forced subtitles searching. [cde6246](https://github.com/morpheus65535/bazarr/commit/cde624696d88679b80197fd7b5e78656cc169c87)\n- Fixed Addic7ed AttributeError exception with some movies #1525 [0e01c64](https://github.com/morpheus65535/bazarr/commit/0e01c64079cced6e5ed8b2555c0506ae2bec6a1a)\n- Fixed Ktuvit provider login [b137af0](https://github.com/morpheus65535/bazarr/commit/b137af0908daf71b16873935dbcfc643cc875674)", "date": "2021-10-12T23:44:33Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v1.0.0/bazarr.zip"}, {"name": "v0.9.9", "body": "From newest to oldest:\n- Fixed Titrari providers search endpoint. #1526 [8d5c2db](https://github.com/morpheus65535/bazarr/commit/8d5c2db39d8016ef9e69e42004e1b3f929360c44)\n- Fixed incompatible operator with Python 3.7 in latest patch to Addic7ed. [caf01da](https://github.com/morpheus65535/bazarr/commit/caf01da2edadbbc847f39a92c45c67587e7eb152)\n- Fix redirect issues when accessing root path [9901bf3](https://github.com/morpheus65535/bazarr/commit/9901bf340019d431c1aaba69546589e73ccbd8f3)\n- Added movies searching to Addic7ed provider. #1525 [96a3acf](https://github.com/morpheus65535/bazarr/commit/96a3acf8e945ca8a509a1b653a6d9d2622218eea)\n- Fix incorrect position of provider badge [60de70f](https://github.com/morpheus65535/bazarr/commit/60de70f9e8db1216396cc9275077ece46036dc94)\n- Fix items are not updated when using mass editor [60e2732](https://github.com/morpheus65535/bazarr/commit/60e2732f48f2aa4f2ab1845b40cff8fef10e1bc2)\n- Added provider Ktuvit [de5bcc8](https://github.com/morpheus65535/bazarr/commit/de5bcc8ed0054e0b7d53f1b7919cb2d2f9d0d94f)", "date": "2021-09-11T12:47:07Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v0.9.9/bazarr.zip"}, {"name": "v0.9.8", "body": "From newest to oldest:\n- Fixed wanted list not getting updated when changing exclusion settings for Sonarr and Radarr. [0e9cd7d](https://github.com/morpheus65535/bazarr/commit/0e9cd7d05fd2a9492d61cb552204c08254c0b470)\n- Remove red dot in the notification center [d37eea3](https://github.com/morpheus65535/bazarr/commit/d37eea3c92f489ec66deb2ed212b7c7d08809252)\n- Fixed a search loop issue with missing subtitles for episodes. #1521 [3b4e81d](https://github.com/morpheus65535/bazarr/commit/3b4e81db5eee45a73b7cee8b371e1f0649b01037)\n- Fixed some issues with HI/forced in history and custom post-processing. #1516 [75d85d0](https://github.com/morpheus65535/bazarr/commit/75d85d05517a7547d36195b309809adfc402a8de)\n- Fix a issue that hi and forced is not properly updated on series subtitle upload modal [50fb06b](https://github.com/morpheus65535/bazarr/commit/50fb06b23a502c3a108c2e17038e4bfe85cbcd59)\n- Add forced and hi checkbox to subtitle upload modal [7e48413](https://github.com/morpheus65535/bazarr/commit/7e48413493510f4f7e95b2b49bdd8b051e482d14)\n- Improved how custom post-processing deal with subtitles language modifier (HI and forced). #1516 [5c5d14c](https://github.com/morpheus65535/bazarr/commit/5c5d14c045e7c152806a75c6ee0088e92c09c32b)\n- Score: add a condition class to allow painless future additions [9ced18d](https://github.com/morpheus65535/bazarr/commit/9ced18d0d3053fbdf8887a139d94e718c9abfd55)\n- Support multi-language in subtitle upload modal [4a890b2](https://github.com/morpheus65535/bazarr/commit/4a890b25617b0a50f3882f95e626c0f226382c7f)\n- Fixed throttled providers badge not being updated when pressing the reset button. [e0b988b](https://github.com/morpheus65535/bazarr/commit/e0b988b20f71d6fb6cc8bf5b55be1cbaf436d227)\n- Fix display issues of backend background tasks [499faeb](https://github.com/morpheus65535/bazarr/commit/499faeb94a61c2320e2f99cfd05e92c85a581081)\n- Add a dialog before closing the page to inform user when there're still background tasks running [81507b9](https://github.com/morpheus65535/bazarr/commit/81507b9e750228de6d52cb3f7cbd631ecfac3713)\n- Improve performance of Web UI [1f3e499](https://github.com/morpheus65535/bazarr/commit/1f3e499f3db42d15436be604934770ed21e62548)\n- Fixed Hebrew language translation issue. #1513 [5ceb876](https://github.com/morpheus65535/bazarr/commit/5ceb876171e14f8d5b9d85a21a394710f81fc76d)\n- Improved Subscene provider with single season series. #1510 [8ae5e54](https://github.com/morpheus65535/bazarr/commit/8ae5e5483ec8c9a36d92bb08522a469cdc741da0)\n- Add tooltip in notification center [2c5aecc](https://github.com/morpheus65535/bazarr/commit/2c5aecc0dbbba241512a016fc7bb302767fac600)\n- Upload serie subtitles in background [43ebecb](https://github.com/morpheus65535/bazarr/commit/43ebecbdb26d4bcd3fc6f6cf18a6489bcc34b5fc)\n- Upload movie subtitles in background [87123ab](https://github.com/morpheus65535/bazarr/commit/87123ab4c7874e218db9273ac2bcef8bfa26e3f6)\n- Update merged source formats [34b1782](https://github.com/morpheus65535/bazarr/commit/34b1782e6a1304bd3a4da9c6e9b8b540358888e0)\n- Fixed debug logging when no episode are returned from database for a specific series because they are ignored. #1507 [79621a0](https://github.com/morpheus65535/bazarr/commit/79621a08716d64dff3a80b00ef15acd605068387)\n- Fix some visual bugs of the notification center [70cb16f](https://github.com/morpheus65535/bazarr/commit/70cb16f0a284c36dcb8cfbaf3bc467d426fbcfe3)\n- Fixed Bazarr not starting if Sonarr or Radarr is unavailable #1506. [2f6206f](https://github.com/morpheus65535/bazarr/commit/2f6206f3a66ea2c424cfc39ca2100d95bd4b062d)\n- Added a settings to ignore ffprobe cache on scheduled disk scan. [2762fc4](https://github.com/morpheus65535/bazarr/commit/2762fc4a6a4c3276faab78c1263523ab63054685)\n- Fixed live episode sync when added in Sonarr v3. [156cf18](https://github.com/morpheus65535/bazarr/commit/156cf1882c0de7f7f2d990781a8b6d2c80bb1a6a)\n- Rewrite modal system using stack, fix some visual bugs [82a687c](https://github.com/morpheus65535/bazarr/commit/82a687c8c8f68dea27181bc5edb1359c455d656d)\n- Add a error page when ui is crashed [37da374](https://github.com/morpheus65535/bazarr/commit/37da3742a0de2ff12558a46d2fc2eb14e3f8f7d9)\n- Fix some style issues [a2fc1d7](https://github.com/morpheus65535/bazarr/commit/a2fc1d73ddd0b6ac54cf17827d03ce7e90c3095a)\n- Fix display issues [135d866](https://github.com/morpheus65535/bazarr/commit/135d866249d9f93182ff89181bbc44b4e44ec226)\n- Add a new notification center to the UI [d7533ba](https://github.com/morpheus65535/bazarr/commit/d7533bac5767df6ffa61756ac12887be2a9eeca7)\n- Update Redux Implementation with Redux-Toolkit [6f9c7f3](https://github.com/morpheus65535/bazarr/commit/6f9c7f3da2d0a70aaf0af1bf094211bcd7d3c4b8)", "date": "2021-08-31T16:53:48Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v0.9.8/bazarr.zip"}, {"name": "v0.9.7", "body": "From newest to oldest:\r\n- Fix broken url for titulky.com provider [9b05a3a](https://github.com/morpheus65535/bazarr/commit/9b05a3a63ae950a225d5d720aa648156375a1df7)\r\n- Modified Bazarr user-agent to report real version [d23d36d](https://github.com/morpheus65535/bazarr/commit/d23d36d81c065a16af601040d68092a84f3ba9b8)\r\n- Added a validation of existing credentials for opensubtitles.com provider. [c05db9f](https://github.com/morpheus65535/bazarr/commit/c05db9f8c5731351304ec01aec9d7ad3ce3cebf8)\r\n- Fixed empty strings being saved as 'None' in config.ini. [c205958](https://github.com/morpheus65535/bazarr/commit/c2059584db9ae3a3c19c3fcfe205f7d72b9e4fcf)\r\n- Added some logging to Sonarr/Radarr get_version. [83764bb](https://github.com/morpheus65535/bazarr/commit/83764bb07b152edb8009bbcd70b6809da8cfcd20)\r\n- Changed the warning when Sonarr SignalR return malformed JSON because of a permissions issue on its config directory. [f2d4ac8](https://github.com/morpheus65535/bazarr/commit/f2d4ac8266c48d69c76ccbdfb558e2f831b243aa)\r\n- Removed the --no-color argument to pip command when installing requirements as it's not supported by older version of pip. [aa6dcbd](https://github.com/morpheus65535/bazarr/commit/aa6dcbd7328fe8c08119e67debaee96fb6d810c9)\r\n- Removed unused variables in check_releases function. [144db06](https://github.com/morpheus65535/bazarr/commit/144db064cda4502fbe100307c458e0b233980585)\r\n- Added some debug logging to the upgrade leftover cleaner. [a5852e6](https://github.com/morpheus65535/bazarr/commit/a5852e605251c4c90076269fbea9669be3abb22c)\r\n- Removed media.resume from Plex webhook to prevent over usage in specific scenarios. [28e28fb](https://github.com/morpheus65535/bazarr/commit/28e28fbd5c7c89fead99b1ff055b66f597d6a88b)\r\n- Fixed issue with upgrade leftover cleanup on Windows. [275aa24](https://github.com/morpheus65535/bazarr/commit/275aa24f58d061362e9ff8b0f6d14c0983bce844)\r\n- Fixed issue when trying to remove non-empty directory when cleaning leftover files after upgrade. [2cea2d2](https://github.com/morpheus65535/bazarr/commit/2cea2d22bfb0e050e95ba3c50dba9631210886d0)\r\n- Fixed issue with upgrade cleanup routine when installed with Windows Installer. [dd80fec](https://github.com/morpheus65535/bazarr/commit/dd80fec9d4a6d5a5af1e7d41bbcebb07c43ae35c)\r\n- Added search progress modal when searching for an episode subtitles triggered by Plex webhook. [91c53ba](https://github.com/morpheus65535/bazarr/commit/91c53ba475eacb66f3c5673ff3122b7cf5c238da)\r\n- Improved how Bazarr get Sonarr/Radarr version to use caching and reduce the number of calls made to their respective API. [842dbf0](https://github.com/morpheus65535/bazarr/commit/842dbf08275b6c6218d29ca1cedb90a5eaca9443)\r\n- Added plex webhooks endpoint to API to search for subtitles on media.play and media.resume events. [bf037f1](https://github.com/morpheus65535/bazarr/commit/bf037f1573ad7f991d5b4d949726b7d5b4ec305c)\r\n- Added a leftover cleanup function to be run after an upgrade. [638d0a8](https://github.com/morpheus65535/bazarr/commit/638d0a8c6857eb302c75d066ebcdf9353e745c11)\r\n- Fixed subtitles sync issues when sync debug is enable. [9372c0b](https://github.com/morpheus65535/bazarr/commit/9372c0b821c25030359a94ca4d05269195634886)\r\n- Fixed root folders check health functions to use the proper path separator. [360e0cf](https://github.com/morpheus65535/bazarr/commit/360e0cfea19a2d1a285ec42dbbd733fde55ddaf6)\r\n- Added settings to download only featured subtitles for LegendasTV [33a600a](https://github.com/morpheus65535/bazarr/commit/33a600a714474495a58fdf038d7a636c0e339c44)\r\n- Fixed settings saving not completing properly when the SignalR client didn't start properly. #1474 [1d20bbb](https://github.com/morpheus65535/bazarr/commit/1d20bbb4b9d83a583d4950c94829733d8d0a4267)\r\n- Update React to version 17 [b912ca4](https://github.com/morpheus65535/bazarr/commit/b912ca4e41cfa7b91620100b14611fb9f8f58747)\r\n- Added virtualenv detection to better deal with requirements installation. [d1f86a3](https://github.com/morpheus65535/bazarr/commit/d1f86a3cbf569155ef1a86b5968c393e08b4ecc8)\r\n- Added chmod execution (if required) after manually triggered tools execution. #1478 [aca9941](https://github.com/morpheus65535/bazarr/commit/aca99415b8f4bcc5b2b43dc338e41b6f4ed96bd9)\r\n- Improved Sonarr versions prior to 3.x detection. [96b8b5c](https://github.com/morpheus65535/bazarr/commit/96b8b5ccce9c45c44daf2c3a11ddb41506f369a3)\r\n- Fixed gevent import in main.py [e2b4912](https://github.com/morpheus65535/bazarr/commit/e2b4912067932dc505fead42ad837c1f1fa03cd1)\r\n- Improved how Bazarr deals with Sonarr SignalR feed issues that are raising exceptions. [5e3ce8c](https://github.com/morpheus65535/bazarr/commit/5e3ce8c8c3e9402d98b6ccdc1b5de5c70e2759ad)\r\n- Fix wanted pages are not updated properly after changing language profile [ed30f76](https://github.com/morpheus65535/bazarr/commit/ed30f76c57dd4b6ac511edf430ebe358ef24ccee)\r\n- Fixed incomplete SQL queries. #1479 [d5b8980](https://github.com/morpheus65535/bazarr/commit/d5b898064d7188d5bc47bcb3a73405a38133d5d6)\r\n- Fixed wanted list ordering. #1475 [6eeaa46](https://github.com/morpheus65535/bazarr/commit/6eeaa46bbf3d3fdc46f6cca39c25284c9459cf12)", "date": "2021-08-13T12:05:29Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v0.9.7/bazarr.zip"}, {"name": "v0.9.6", "body": "From newest to oldest:\r\n- Added fix for inconsistent movie/tv naming to yavkanet provider [31dcd37](https://github.com/morpheus65535/bazarr/commit/31dcd37d52dfb52d091c1131754bba7f1bd94609)\r\n- Fixed zimuku provider to prevent unexpected exception. #1459 [8cdd362](https://github.com/morpheus65535/bazarr/commit/8cdd362caab710923f6d6247d8e24c5cfc8ac72c)\r\n- Updated pysubs2 module to support newer SSA files. [09a8335](https://github.com/morpheus65535/bazarr/commit/09a8335a03fa741be6c4cc5b030bac01d1c84b0f)\r\n- Fixed missing subtitles language badge in movies view to show if it's forced or hi. [60353c0](https://github.com/morpheus65535/bazarr/commit/60353c036743574c64f10bcff0c8a06461c8cafc)\r\n- Added missing authentication on system/status API endpoint. #1467 [2428399](https://github.com/morpheus65535/bazarr/commit/24283992066b8cbafed815bf6be253a93ac06a30)\r\n- Added setuptools to requirements.txt. It should be already installed in most case but just in case. [0a1ad41](https://github.com/morpheus65535/bazarr/commit/0a1ad41f9df173bff7eb053334c50d593f51fd75)\r\n- Added an unsupported warning on startup if using Python 3.9 [3b1b67d](https://github.com/morpheus65535/bazarr/commit/3b1b67d70142c6a6f77dac7e84755a1519449447)\r\n- Fixed wanted subtitles searching for ones that should be excluded for search. #1458 [8bfcf97](https://github.com/morpheus65535/bazarr/commit/8bfcf97ed59efb10850c70e45e2be7660437893a)\r\n- Added inconsistent tv naming entries to Subssabbz and Subsunacs providers [b1654bb](https://github.com/morpheus65535/bazarr/commit/b1654bb7475bfd80b64eaaa390ca228804f81bbc)\r\n- Fixed Wizdom provider by adding some failsafe to imdb_id logic. #1456 [86fb10e](https://github.com/morpheus65535/bazarr/commit/86fb10e3affc478fe8e4dceca378ac7aa6bda3c5)\r\n- Fixed movies not getting properly excluded from wanted subtitles search task. [ae475f2](https://github.com/morpheus65535/bazarr/commit/ae475f283e0cff87a057727345aef44c48ccf19f)\r\n- Fixed colors name without the required dash (light and dark ones). #1457 [cb623c3](https://github.com/morpheus65535/bazarr/commit/cb623c3c2689561047810958d5d760c89c00d00a)\r\n- Fixed subdivx provider when there's a year in series directory name but not on the provider website. #1454 [4a78f3b](https://github.com/morpheus65535/bazarr/commit/4a78f3b6b8847a3562969cde26f8cada8e2e3bf4)\r\n- Fixed proxy ignore list input that was splitting on dots. #1453 [64cf2bc](https://github.com/morpheus65535/bazarr/commit/64cf2bc5d2b3e7b53f0f101d52c44647d8791429)\r\n- Fixed file encoding guessing by extending Arabic, Farsi and Persian language possible encoding. [2b67c5b](https://github.com/morpheus65535/bazarr/commit/2b67c5bcba5e15af6983c8c786898434f9c3c2ca)\r\n- Fixed the Bazarr update process that was preventing moving back to master branch once you've started to use development/nightly. #1331 [4e03ec0](https://github.com/morpheus65535/bazarr/commit/4e03ec0acc5d07638ce5424bd68fa44b63e31fdc)\r\n- Fix argenteam TypeError and JSONDecodeError [38d2332](https://github.com/morpheus65535/bazarr/commit/38d2332c3989d0ec29326d4f9b13588c46ee08c7)\r\n- Fixed supersubtitles provider not able to search for Loki series subtitles #1447 [81b5700](https://github.com/morpheus65535/bazarr/commit/81b5700209e84b60460d00551b5c59b73c2b1128)\r\n- Fixed Chinese characters decoding by using gb18030 after big5 [5d5a1e8](https://github.com/morpheus65535/bazarr/commit/5d5a1e866c1daea5b52fd8b7db5b0f8feba9d7f1)\r\n- Fixed removal of IPv4 and apikey from logs [73691b2](https://github.com/morpheus65535/bazarr/commit/73691b28a275b58896a640e6c2e35f5282408018)\r\n- Fixed token issue with opensubtitles.com [557a061](https://github.com/morpheus65535/bazarr/commit/557a06173df4239ab1bd089779c09b049fb41ac4)\r\n- Added missing column to movies upgrade query. [d0c7c5d](https://github.com/morpheus65535/bazarr/commit/d0c7c5d5e073c80af90569158c49072a87da89e6)\r\n- Implemented database migration function [50d4a7a](https://github.com/morpheus65535/bazarr/commit/50d4a7a0479751fa2bd67058ec0266093970ddbc)\r\n- Fixed external subtitles not shown when no languages profile is selected. [fd6c959](https://github.com/morpheus65535/bazarr/commit/fd6c959ec8baed6a13619f0df5ab404e6dd99b61)\r\n- Reset window position when backing to previous page [030ca2a](https://github.com/morpheus65535/bazarr/commit/030ca2a4e36bce65c75bd55a410cdc6777bcffca)\r\n- Improved events sent to browser. [b9c27d5](https://github.com/morpheus65535/bazarr/commit/b9c27d53ad51ab3f30a3d37148ffd1cca4a35b0c)\r\n- Upgraded calls to Sonarr API in order to use the new v3 API when available. [ee41b78](https://github.com/morpheus65535/bazarr/commit/ee41b78f4e4fe015ce915d1721b880b6d2d4d40f)\r\n- Fixed Regielive provider #1438 [26e978b](https://github.com/morpheus65535/bazarr/commit/26e978b14b2289fa189a789f6f2c03570c55eb08)\r\n- Fixed Titrari provider #1438 [f4df07f](https://github.com/morpheus65535/bazarr/commit/f4df07fb83b37bc4022723f2af572d5d77ae2316)\r\n- Fixed an issue with languages filter in history stats. [de4ff35](https://github.com/morpheus65535/bazarr/commit/de4ff35e055c0d69c0bfbebb47733792c5196a27)\r\n- Fixed scan disk function to not use cached ffprobe result and force a refresh of the cache. #1434 [a3d0e1d](https://github.com/morpheus65535/bazarr/commit/a3d0e1d192cf5e0a86189cc7336847d882487c61)\r\n- Finish providers migration to subliminal_patch's guess_matches [a39a9e8](https://github.com/morpheus65535/bazarr/commit/a39a9e8bd52542ff34a4a7b94a9504073e5aeabd)\r\n- Improved subtitles scoring system [e86d537](https://github.com/morpheus65535/bazarr/commit/e86d537ca2ffe49a6911b44d3c3611365461e232)\r\n- Fixed subtitles being looking for during a search in loop even if cutoff have been reached during this actual search. #1435 [f9997ca](https://github.com/morpheus65535/bazarr/commit/f9997ca9694ba3b8f2e3d446f1cdadaa0ed9938c)\r\n- Fix languages/providers filters issue in history stats [c21e501](https://github.com/morpheus65535/bazarr/commit/c21e501ebe139fa76a4b1c6407731e0fd25997c7)\r\n- Added backend API call for upcoming languages/providers filters fix to history stats. [054d117](https://github.com/morpheus65535/bazarr/commit/054d1174000cae371ff17fe348db4ce26e71e947)\r\n- Fixed History stats actions filter values not being properly implemented. [db5d37f](https://github.com/morpheus65535/bazarr/commit/db5d37f5d48aa5b582c55cee1fe509e625a3d206)\r\n- Update half of providers to use subliminal_patch's guess_matches [14d467e](https://github.com/morpheus65535/bazarr/commit/14d467e645c1d60d20067efaa9ad515934f67111)\r\n- Fix Sucha provider: AttributeError and wrong matches [5b1f479](https://github.com/morpheus65535/bazarr/commit/5b1f4799014f9e8a91e1bd037a86db585c5423e8)\r\n- Fixed failed upload #1429 [90ba573](https://github.com/morpheus65535/bazarr/commit/90ba573ebe9f3660a0aa61f81a0a885df5468213)\r\n- Updated signalrcore module following PR #61 merging. [23ede4e](https://github.com/morpheus65535/bazarr/commit/23ede4e7f9a69c6cc6f8545d90789457eb0d6c9a)\r\n- Fixed BetaSeries AttributeError exception #1423 [cb09f8d](https://github.com/morpheus65535/bazarr/commit/cb09f8d4405d4a873cd9097924fe4da932cbfed4)\r\n- Added custom language class to make it easier to implement non-standard/regional languages [4ebcd49](https://github.com/morpheus65535/bazarr/commit/4ebcd49546ed7772cb6f3a9c83079e5aea08e15a)\r\n- Fixed external subtitles were being filtered by show only desired embedded subtitles settings. [d4aed74](https://github.com/morpheus65535/bazarr/commit/d4aed7457a428685c3883eca6a1bc14f3352d707)\r\n- Fixed movies subtitles paths being shown in UI without being mapped. [d562faf](https://github.com/morpheus65535/bazarr/commit/d562faf15149a51c885a9a09fda02dc2b284ab84)\r\n- Fixed log viewer showing last line of log two times. [11fb78f](https://github.com/morpheus65535/bazarr/commit/11fb78fafabbb6529080cb8eecb6f1aed51ad7ec)\r\n- Fixed series and episodes sync issues when adding and removing series from Sonarr. [727c8f5](https://github.com/morpheus65535/bazarr/commit/727c8f5d140a92a836258d4bba25e7244befd7ce)\r\n- Fixed some queries trowing exception when no existing row were found #1425 [8d570fd](https://github.com/morpheus65535/bazarr/commit/8d570fd3b76883455200905f1b3cf4ab60e6d2da)\r\n- Fixed some incompatible calls with Radarr v4 API [57080ed](https://github.com/morpheus65535/bazarr/commit/57080eda1119960e91fbda46e1a5c5d7191107b1)\r\n- Fix some incompatible calls in URL test button [7687013](https://github.com/morpheus65535/bazarr/commit/768701327025471e04619db80ead6ebe6c6e08c0)\r\n- Fixed some incompatible calls with Radarr v4 API [e9ef40c](https://github.com/morpheus65535/bazarr/commit/e9ef40c6296a1e2e0c81b0ee42e5d2a2955c5f60)\r\n- Disabled SSL verify for Sonarr SignalR feed connection and added Bazarr user agent to both Sonarr and Radarr feeds. [b8512cc](https://github.com/morpheus65535/bazarr/commit/b8512ccf0908c0d0eea932fadb8a432e0ab6cfaf)\r\n- Fixed API call to Radarr since the are removing legacy API from nightly builds. [3468601](https://github.com/morpheus65535/bazarr/commit/3468601d4d4b9078e76b7a5335da44ab19f97f1e)\r\n- Fixed some other queries to properly exclude episodes/movies. [e710488](https://github.com/morpheus65535/bazarr/commit/e710488d7a70d95993de05759fd625ddccd4482e)\r\n- Added exclusion in get rootfolder functions for the ones that aren't used by any series/movies. #1413 [b2b9795](https://github.com/morpheus65535/bazarr/commit/b2b979581075788d320a1b76158f4607d2e41ca0)\r\n- Fix a issue that ui will crash when first entering history view [7b9c19d](https://github.com/morpheus65535/bazarr/commit/7b9c19d7c00b9f73b3b2fe2b25dedc4d4b337033)\r\n- Fixed query to add url to get_notifier_providers function [833ecb3](https://github.com/morpheus65535/bazarr/commit/833ecb34e8beb22d1ca2411a5e1ecdc06fa92162)\r\n- Fixed more peewee implementation issues. [f442d7d](https://github.com/morpheus65535/bazarr/commit/f442d7d09190439d92a8ef40cc039492d787bb39)\r\n- Fixed database init issue. [9cc00eb](https://github.com/morpheus65535/bazarr/commit/9cc00ebd65597f3690bc16a0d4f9fa73e8bb6d08)\r\n- Fix subtitle badges display issues in some situations [5732828](https://github.com/morpheus65535/bazarr/commit/573282863d3b4321be3a23a60a7082baff8c4b39)\r\n- Improved Opensubtitles.com provider caching of token [7209bad](https://github.com/morpheus65535/bazarr/commit/7209bad0c4ca2ae090c239b0b72a349ef2fcf8bd)\r\n- Improved Opensubtitles.com providers [bb842b9](https://github.com/morpheus65535/bazarr/commit/bb842b92af4049e4ac1d342aaf4a960b8b0bba09)\r\n- Fixed other issues with peewee [75c003a](https://github.com/morpheus65535/bazarr/commit/75c003ab674aadf5e9e451ecaa24a1b3cad1e8f3)\r\n- Fixed some issues after peewee implementation [d34add9](https://github.com/morpheus65535/bazarr/commit/d34add9fa4a7e9e894953ca55222643e76d433bd)\r\n- Implemented Peewee ORM in replacement of raw SQL queries. [2b9d892](https://github.com/morpheus65535/bazarr/commit/2b9d892ca9151118917c2d682c9bde204a5c72fa)\r\n- Added series' episodes sync when required by Sonarr (ex.: episodes monitored status change) [47bb77a](https://github.com/morpheus65535/bazarr/commit/47bb77ac6f229afb366e40af4e8233933d23ebd2)\r\n- Tweaked the Radarr SignalR feed keep-alive and reconnection intervals. [c8e02e2](https://github.com/morpheus65535/bazarr/commit/c8e02e280a2a9157391dc37802fc3ebe71bfa070)\r\n- Fixed events storm to UI on SignalR (re)connection with large libraries. [bf8c4ca](https://github.com/morpheus65535/bazarr/commit/bf8c4caef7d4f819e2f5704b345c3d056ca103a9)\r\n- Updated SignalRCore to support websocket-client 1.0.0. [2643023](https://github.com/morpheus65535/bazarr/commit/2643023240ec4a3a253b06d134862bb8d27442f4)\r\n- Fixed the movies not getting synced after a reconnection of Radarr SignalR feed. [1fdad49](https://github.com/morpheus65535/bazarr/commit/1fdad498f6cdbfd238ce75e21a4e06801b6355e9)\r\n- Added argument to disable the socketio events emit process to avoid events storm rendering the UI unresponsive. I still need to use it when calling sync functions from signalr_client.py. [5f99836](https://github.com/morpheus65535/bazarr/commit/5f99836801e2b63438a5026135f738dbb9090414)\r\n- Upgraded websocket-client module to fix Radarr SignalRCore disconnection issues. [70a0f68](https://github.com/morpheus65535/bazarr/commit/70a0f6835e68e72e70340d0153cd0f2bcd5a9b53)\r\n- Switched to Gevent scheduler instead of background threaded. [dd8072a](https://github.com/morpheus65535/bazarr/commit/dd8072adf8d80016c958ddf1b396e8f229adc52f)\r\n- Improved reconnection process for Sonarr SignalR feeds. [44dd478](https://github.com/morpheus65535/bazarr/commit/44dd478c48fd394bac9fc7782c4c526dd26e7970)\r\n- Ignore embedded commentary subtitles [d6da0ee](https://github.com/morpheus65535/bazarr/commit/d6da0eec9500e119718cbe2cc775943e11450cf6)\r\n- Improved readability of toast progress label, overflow of toast message with ellipsis and removed the toast close button considering the auto-dismiss. [cf35f4a](https://github.com/morpheus65535/bazarr/commit/cf35f4a9d2ccb792e0fbaa6d110ed5dd39af212d)\r\n- Added successful completion message to tasks progress toast. [821ff13](https://github.com/morpheus65535/bazarr/commit/821ff13b80a0559bb07ca46d3df8a4b5e1ea3102)\r\n- Fixed Radarr SignalR connection started even if Radarr integration isn't enabled. [e239562](https://github.com/morpheus65535/bazarr/commit/e23956278bc7b8c77fd0cecdcf90fd7b5b9642fc)\r\n- Improved how we deal with requirements.txt installation process results. [4ef35c5](https://github.com/morpheus65535/bazarr/commit/4ef35c5527e3ba4da27090f5b040786e0e47214d)\r\n- Improved Radarr SignalR feed reconnection process. [84a1c26](https://github.com/morpheus65535/bazarr/commit/84a1c265cea6ddc59c5dc4b43f756af7090e8cf8)\r\n- Fix for Bazarr version parsing. [ffe8a20](https://github.com/morpheus65535/bazarr/commit/ffe8a201a9066bfc47d94f5bb2edc718447e7898)\r\n- Handling progress delete event in UI [0031e69](https://github.com/morpheus65535/bazarr/commit/0031e69db6b8b129eefcd2e8c2ea87056c529761)\r\n- Added header to toasts and bring back the hide_progress backend function. todo: deal with delete method in frontend. [fe0a34a](https://github.com/morpheus65535/bazarr/commit/fe0a34aae58e478c0265cada2e9b08842e8cb92c)\r\n- Fix some style issues [4bb6e87](https://github.com/morpheus65535/bazarr/commit/4bb6e87db9168d6246ea72fcd1aa37ecadbe67e6)\r\n- Tweak style of progress notification [38fc3b3](https://github.com/morpheus65535/bazarr/commit/38fc3b36fed578aa3eba3752f8e66e87ea90c8c5)\r\n- Add background task notification [678b2f0](https://github.com/morpheus65535/bazarr/commit/678b2f0e555e4cc431c61a83907b2a656c8ef25f)\r\n- Added progress events to most batch functions. [9d153a1](https://github.com/morpheus65535/bazarr/commit/9d153a1d9cd263097f89fa4e6e93d929e9067160)\r\n- Fix an issue that cause subtitles missing in movie page [b403744](https://github.com/morpheus65535/bazarr/commit/b4037442b15eda59e6b7bc1a48988d28e78a30e1)\r\n- Added real-time sync with Sonarr v3 and Radarr v3 by feeding from SignalR feeds. You can now reduce frequency of sync tasks to something like once a day. [44c51b2](https://github.com/morpheus65535/bazarr/commit/44c51b2e2c3bffdfc0e0c447c038f6cd0bfd2cbe)\r\n- Added live update of UI using websocket. Make sure your reverse proxy upgrade the connection! [72b6ab3](https://github.com/morpheus65535/bazarr/commit/72b6ab3c6a11e1c12d86563989d88d73e4e64377)", "date": "2021-07-19T01:29:01Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v0.9.6/bazarr.zip"}, {"name": "v0.9.5", "body": "From newest to oldest:\r\n- Added filter for unknown providers in settings. [b72d476](https://github.com/morpheus65535/bazarr/commit/b72d476d2eb3309a499e4060199092a6840fb005)\r\n- Tried to fix the issues with bsplayer [a555617](https://github.com/morpheus65535/bazarr/commit/a5556177386207d5635a5e8aeb201bf4f5850186)\r\n- Added cache to Titlovi request to prevent doing the same request over and over again for each and every episode of a show. [29ad8c6](https://github.com/morpheus65535/bazarr/commit/29ad8c61221659a0aa3807d1bee6b30f870d89d1)\r\n- Improved the ffprobe call caching mechanism by storing result to DB and using it for indexing and subtitles search. [33e1555](https://github.com/morpheus65535/bazarr/commit/33e155531119dc07e1deaf339de0cb4577122a8c)\r\n- Fix for database cleanup routine on new DB. [386bf19](https://github.com/morpheus65535/bazarr/commit/386bf19b6e4d82f6378c4f53c2806139ef178be7)\r\n- Add database cleanup routine to remove unused table columns. [1ebc618](https://github.com/morpheus65535/bazarr/commit/1ebc61818bb3c3a79bdd960a2ab6f19500b6e4a3)\r\n- Fix for subtitulamos.tv provider #1397 [1c9945f](https://github.com/morpheus65535/bazarr/commit/1c9945fadad9f25ce804f9a4dd0d110ac8544a77)\r\n- Downgrade auditok to version 0.1.5 [30ef713](https://github.com/morpheus65535/bazarr/commit/30ef713fa260dc9d9d3120359cd052808606badb)\r\n- Fix for automatic subsync history logging [cc3628a](https://github.com/morpheus65535/bazarr/commit/cc3628a19f92112e1493e0f6adbfbd876a3de816)\r\n- Fix for manual subsync history logging [84a20a3](https://github.com/morpheus65535/bazarr/commit/84a20a3146e52870908ae5182af832cc557a2ce8)\r\n- Added Movie/Series Year to Notifier (based on #1393). [6b1d712](https://github.com/morpheus65535/bazarr/commit/6b1d71282b6fd74fe5420cdc90dd24bf6f173306)\r\n- Fixed episodes manual search button not disabled when no languages profile is selected. [cc17e81](https://github.com/morpheus65535/bazarr/commit/cc17e8144f1410c18935c5a7608d499c92c5499b)\r\n- Fix for forced subtitles improperly upgraded to non-forced. [053adcc](https://github.com/morpheus65535/bazarr/commit/053adcc2795e1e79c7958bd7ef02721c2f899724)\r\n- Use new wiki address in System Status page [09e98b7](https://github.com/morpheus65535/bazarr/commit/09e98b73678101af3f643f2648ab65a0bd089cfa)\r\n- Fix display issue of badges when Sonarr or Radarr is disabled [01b7698](https://github.com/morpheus65535/bazarr/commit/01b76983a797cf8c93fa281c0dcaebd76be4b42f)\r\n- Improve UI performance [71423d2](https://github.com/morpheus65535/bazarr/commit/71423d2029c2c5c845b1e3db3800d0755033b5b9)\r\n- Add description text on providers selector [6de9a34](https://github.com/morpheus65535/bazarr/commit/6de9a3451d8b97b99032e886cb62fd154a378cdd)\r\n- Fix auth issues by only check auth when requesting webui [a742e3c](https://github.com/morpheus65535/bazarr/commit/a742e3c5e3336c2d3e56410bad5eb432d07da056)\r\n- Fix some potential issues when displaying languages [d6d9e93](https://github.com/morpheus65535/bazarr/commit/d6d9e93843964b0473fe01aa0411677690b40f8b)\r\n- Fix issues in language profile modal [65e68d4](https://github.com/morpheus65535/bazarr/commit/65e68d44098a01b13277fdc6a20651c51403014a)\r\n- Fix subtitles display issue on episode page [e462686](https://github.com/morpheus65535/bazarr/commit/e462686c178bb2c611b873b65b896ec99b6d414a)", "date": "2021-05-08T14:06:05Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v0.9.5/bazarr.zip"}, {"name": "v0.9.4", "body": "From newest to oldest:\n- Reworked the monitored status of movie detailed view. [3eef317](https://github.com/morpheus65535/bazarr/commit/3eef31762334c5c25834cf3c769dc7bae7b9e5d3)\n- Added monitored status to movie detailed view. [e848e10](https://github.com/morpheus65535/bazarr/commit/e848e107c841eb795f29b352db817de59c52056d)\n- Fixed Yavkanet provider [d9e0e2e](https://github.com/morpheus65535/bazarr/commit/d9e0e2ec9232bc0b9a90249e3091f30282860e3f)\n- Added eligible for upgrade icon in histories. [5f66724](https://github.com/morpheus65535/bazarr/commit/5f66724d2a6a5d5c65d32e6c86671741299e5119)\n- Fixed upgrade process to properly use bazarr.zip asset #1379 [b02e0ec](https://github.com/morpheus65535/bazarr/commit/b02e0ec7ec462b9236a413aa9363d0825d3c6e3e)\n- Fix app route redirect issues [c3c1891](https://github.com/morpheus65535/bazarr/commit/c3c1891f5696cd261e560dceade92f23ab5b6652)\n- Fix issues when testing notifications [385cc21](https://github.com/morpheus65535/bazarr/commit/385cc214b348815105bc654df3fc710b25a3e620)\n- Fixed subtitles upgrade process to properly deal with manual actions [99fc282](https://github.com/morpheus65535/bazarr/commit/99fc282b07f4be07ebf84255603385bd6899bcf8)\n- Update ffsubsync and srt module [4a0932b](https://github.com/morpheus65535/bazarr/commit/4a0932b5d3052867f7f92984300d2ab4ec54fb0d)\n- Extend simpleconfigparser to not strip quotes from the string value [8e91bee](https://github.com/morpheus65535/bazarr/commit/8e91beed83e6b5a4bec680d15b226a77ff3e224e)\n- Fixed package_info parsing to deal with single line text with `\\n` separator [d2b7dba](https://github.com/morpheus65535/bazarr/commit/d2b7dba3bf36501cf4eccb2af3d1889542bc02a2)\n- Properly log error when Post processing subtitles [29f73a6](https://github.com/morpheus65535/bazarr/commit/29f73a6c09c561aedd468f69e441e1a575cb756c)\n- Fix issues after updating schedule settings [0107150](https://github.com/morpheus65535/bazarr/commit/0107150933ad95e59efdd4b8c2f7fe86db470963)\n- Added Bazarr user-agent to requests to Sonarr and Radarr [a193576](https://github.com/morpheus65535/bazarr/commit/a193576b639d4a3995b3172e47e70c1a9a6b3281)\n- Fix Installed badge won't display in release page [74da491](https://github.com/morpheus65535/bazarr/commit/74da491bd17d4317c5f277236b0f65be0f7acc87)\n- Revert \"Merged the series and episodes sync process. Episodes are only synced if series sizeOnDisk reported by Sonarr changes.\" [97caf44](https://github.com/morpheus65535/bazarr/commit/97caf44a5ad049a6eba12dd0c1ce740505745c68)\n- Merged the series and episodes sync process. Episodes are only synced if series sizeOnDisk reported by Sonarr changes. [5aadcea](https://github.com/morpheus65535/bazarr/commit/5aadcea83a395c4de5c8a067f4a5e6966d69703e)\n- Disable add Profile button when enabled languages are empty [a99c4d5](https://github.com/morpheus65535/bazarr/commit/a99c4d5438b1b53dcc376c2737816c98ab8e4eb5)\n- Added cache support for TuSubtitulo provider [95bef2d](https://github.com/morpheus65535/bazarr/commit/95bef2d51904c1ab4f296d671236c9c6770be526)\n- Fixed bad protocol for Argenteam provider [940b642](https://github.com/morpheus65535/bazarr/commit/940b6428ddb744c46a35d6e547400b5e7238bada)\n- Fixed subtitles deletion after blacklisting. [986912b](https://github.com/morpheus65535/bazarr/commit/986912b2cc7426a065cd65052a5c3504a61652b3)\n- Fixed update mechanism to store bazarr.zip asset download url (if available) instead of zipball. [4bc379f](https://github.com/morpheus65535/bazarr/commit/4bc379fabcf456e1b7b5830a706aaa80bbde1cbe)\n- Update npm packages to latest version [df70a8b](https://github.com/morpheus65535/bazarr/commit/df70a8b6fe28ef4c202bf0833e23cc47e5a3a242)\n- Fix issues on Movies Blacklist API [26dc355](https://github.com/morpheus65535/bazarr/commit/26dc35556e3ac14c4702b9d0e413d2125475d762)\n- Added tooltips on hover for history and blacklist pretty date [84adea2](https://github.com/morpheus65535/bazarr/commit/84adea2a28744607f072d828084abef61ee9bd3a)\n- Fixed tvsubtitles provider #1354 [1a025b6](https://github.com/morpheus65535/bazarr/commit/1a025b693b0ec150a5a71d533451d529062d00ad)\n- Fixed some providers key to match the actual providers name in subliminal_patch #1353 [cc80bd4](https://github.com/morpheus65535/bazarr/commit/cc80bd4be6fcc351d274b60aff667804a7660695)\n- Updated the init process to get the branch from package_info in docker images [0c09bc2](https://github.com/morpheus65535/bazarr/commit/0c09bc2360bbd60fe414af516236b77c66632259)\n- Add popover to movies and episodes for showing original name [08be95c](https://github.com/morpheus65535/bazarr/commit/08be95c8b736527312ccefc04fc1337e5a6584c3)\n- Add feature of going to default page by clicking bazarr icon [b1478da](https://github.com/morpheus65535/bazarr/commit/b1478da6b6b64a71dd07d48731a75676ac6027d3)\n- Keep expanded state when anything changes in episodes page [3744991](https://github.com/morpheus65535/bazarr/commit/3744991f9348fea8b0fecb3438fd7c95a928fd2c)\n- Fixed manual search modal matches popover position and width. [1613501](https://github.com/morpheus65535/bazarr/commit/1613501ea4dc5c9a19cd9b09980813a4a56f1b97)\n- Improved the manual search releases dropdown [b50e7f4](https://github.com/morpheus65535/bazarr/commit/b50e7f4fff67b3237b026cb313dfdf091f517206)\n- Added Subsynchro provider [25894c4](https://github.com/morpheus65535/bazarr/commit/25894c477fc05ecbf5952b95675ef80de16a84b9)\n- Add a error page when ui is crashed [04fd39b](https://github.com/morpheus65535/bazarr/commit/04fd39bc8a43b4ab20447d6c5af177301fd8ee99)\n- Fix a crash in ui when only one post-processing option is selected [6ef7795](https://github.com/morpheus65535/bazarr/commit/6ef7795ac5af3a8bb12b4e4606b5cdbc84071f0b)\n- Fix for #1336 [14fe4b0](https://github.com/morpheus65535/bazarr/commit/14fe4b0594739b3499c3124bcd6883253cd2020a)\n- Fix issues on release_beta_to_dev and bring back CI [e0779cb](https://github.com/morpheus65535/bazarr/commit/e0779cb3c13ee8214be2f47de16a776d50287d21)\n- Fixed packages.json creation path [272a1c1](https://github.com/morpheus65535/bazarr/commit/272a1c11416241a94f222d4a15e42768e21a11f1)\n- Re-enabled dev pipeline with build committed. [6abca59](https://github.com/morpheus65535/bazarr/commit/6abca59c83a1a6ce1c040af02a89d351d23897d2)\n- Manually build UI to reflect the changes [eb4fbc6](https://github.com/morpheus65535/bazarr/commit/eb4fbc6a24f11b721175a16c123421a521aa0b1b)\n- Fixed issue with notifications when deleting [89a3d7f](https://github.com/morpheus65535/bazarr/commit/89a3d7f6e31eb84e110744679a918b120ceb369a)\n- Fixed missing build [61e45bb](https://github.com/morpheus65535/bazarr/commit/61e45bbb6d7cf2c647480b3b3d1d1c0eeee06d2f)\n- Fixed issue with log entries without exception stack [ac8eb62](https://github.com/morpheus65535/bazarr/commit/ac8eb62de0b3491ed9feefc78e86291afc677263)\n- Fixed image proxy when you don't use a base url in Sonarr. [54eb0fc](https://github.com/morpheus65535/bazarr/commit/54eb0fc17411107ed85e9bca0ace4210ae2ad52b)\n- Fixed get_exclusion_clause that do not have the required column due to a missing join. [a4684fe](https://github.com/morpheus65535/bazarr/commit/a4684feaba914b6957135d08cdef68808f209380)\n- Fix a issue when requesting wanted movies [d0f8219](https://github.com/morpheus65535/bazarr/commit/d0f82198eb835a0e0dfa9ebd87c5987a8bdabc5b)\n- Commented out build from pipeline. [dfc14a6](https://github.com/morpheus65535/bazarr/commit/dfc14a60b8356c605b9390aab08e1147802653a5)\n- Manually built React templates [184c5f8](https://github.com/morpheus65535/bazarr/commit/184c5f89acfa6718295959ef0585b8d17d4e73fe)\n- Removed build from .gitignore [2edabe2](https://github.com/morpheus65535/bazarr/commit/2edabe28dcfe87b097962cd7e0dca01d80f9187b)\n- Refactored Web UI using React [f64719e](https://github.com/morpheus65535/bazarr/commit/f64719e4ad27df82d93f4eb2ea67c6fc81cf9d16)", "date": "2021-04-19T13:26:51Z", "prerelease": false, "download_link": "https://github.com/morpheus65535/bazarr/releases/download/v0.9.4/bazarr.zip"}]