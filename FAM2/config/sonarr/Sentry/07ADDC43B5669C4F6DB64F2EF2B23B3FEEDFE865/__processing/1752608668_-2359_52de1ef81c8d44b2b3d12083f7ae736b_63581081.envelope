{"sdk":{"name":"sentry.dotnet","version":"4.0.2"},"event_id":"52de1ef81c8d44b2b3d12083f7ae736b","trace":{"trace_id":"f5c3eaf33db747dc8bdff916f59c87dd","public_key":"e2adcbe52caf46aeaebb6b1dcdfe10a1","release":"4.0.15.2941-main","environment":"main"}}
{"type":"event","length":55394}
{"modules":{"System.Private.CoreLib":"*******","Sonarr":"4.0.15.2941","System.Runtime":"*******","Sonarr.Common":"4.0.15.2941","System.Collections":"*******","System.Net.Primitives":"*******","Microsoft.Win32.Primitives":"*******","Sonarr.Host":"4.0.15.2941","Sonarr.RuntimePatches":"4.0.15.2941","Microsoft.Extensions.Hosting.Abstractions":"*******","NLog":"*******","netstandard":"*******","System.Console":"*******","Microsoft.AspNetCore.Connections.Abstractions":"*******","0Harmony":"*******","System.Linq":"*******","System.Collections.Concurrent":"*******","System.Text.RegularExpressions":"*******","System.Memory":"*******","System.Reflection.Emit.ILGeneration":"*******","System.Reflection.Emit.Lightweight":"*******","System.Threading":"*******","System.Reflection.Primitives":"*******","NLog.Layouts.ClefJsonLayout":"*******","System.ComponentModel":"*******","System.Net.Mail":"*******","System.Private.Uri":"*******","System.IO.FileSystem.Watcher":"*******","System.Diagnostics.Process":"*******","System.ComponentModel.Primitives":"*******","Sentry":"*******","System.IO.Compression":"*******","System.Text.Json":"*******","System.Security.Cryptography.Algorithms":"*******","System.Security.Cryptography.Primitives":"*******","System.Net.Http":"*******","System.Threading.Thread":"*******","System.Runtime.InteropServices.RuntimeInformation":"*******","System.Diagnostics.DiagnosticSource":"*******","System.ObjectModel":"*******","Sonarr.Core":"4.0.15.2941","System.Text.Encoding.CodePages":"*******","Microsoft.Extensions.Configuration.Abstractions":"*******","Microsoft.Extensions.Hosting":"*******","Microsoft.Extensions.DependencyInjection.Abstractions":"*******","DryIoc":"*******","DryIoc.Microsoft.DependencyInjection":"*******","Microsoft.Extensions.Hosting.WindowsServices":"*******","Microsoft.Extensions.Configuration":"*******","Microsoft.Extensions.Configuration.Xml":"*******","Microsoft.Extensions.Configuration.EnvironmentVariables":"*******","Microsoft.Extensions.Configuration.FileExtensions":"*******","Microsoft.Extensions.FileProviders.Abstractions":"*******","Microsoft.Extensions.FileProviders.Physical":"*******","Microsoft.Extensions.Primitives":"*******","System.Xml.ReaderWriter":"*******","System.Private.Xml":"*******","System.Security.Cryptography.Xml":"*******","System.Text.Encoding.Extensions":"*******","Microsoft.Extensions.Configuration.Binder":"*******","Microsoft.Extensions.Logging":"*******","Microsoft.AspNetCore.Hosting.Abstractions":"*******","Microsoft.AspNetCore.Hosting":"*******","System.ComponentModel.TypeConverter":"*******","Microsoft.Extensions.DependencyInjection":"*******","System.Diagnostics.StackTrace":"*******","Microsoft.AspNetCore.Server.Kestrel.Core":"*******","Microsoft.AspNetCore.Server.Kestrel":"*******","Microsoft.AspNetCore.Server.Kestrel.Transport.Quic":"*******","System.Net.Quic":"*******","System.Runtime.InteropServices":"*******","System.Diagnostics.Tracing":"*******","Microsoft.Extensions.Options":"*******","Microsoft.Extensions.Logging.Abstractions":"*******","Microsoft.Extensions.Options.ConfigurationExtensions":"*******","Microsoft.AspNetCore.Http.Abstractions":"*******","Microsoft.Extensions.Features":"*******","Microsoft.AspNetCore.Http":"*******","Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets":"*******","Microsoft.AspNetCore.Hosting.Server.Abstractions":"*******","Microsoft.AspNetCore.HttpOverrides":"*******","Microsoft.AspNetCore.Routing":"*******","Microsoft.AspNetCore.Routing.Abstractions":"*******","Microsoft.AspNetCore.ResponseCompression":"*******","Microsoft.AspNetCore.Cors":"*******","Microsoft.AspNetCore.Mvc.Core":"*******","Microsoft.AspNetCore.Mvc":"*******","Sonarr.Api.V3":"4.0.15.2941","Microsoft.AspNetCore.Mvc.ViewFeatures":"*******","Microsoft.AspNetCore.Mvc.Abstractions":"*******","Sonarr.Http":"4.0.15.2941","Swashbuckle.AspNetCore.SwaggerGen":"*******","Microsoft.AspNetCore.SignalR":"*******","Microsoft.AspNetCore.SignalR.Core":"*******","Microsoft.AspNetCore.SignalR.Common":"*******","Microsoft.AspNetCore.SignalR.Protocols.Json":"*******","Microsoft.AspNetCore.DataProtection":"*******","Microsoft.AspNetCore.Authorization":"*******","Microsoft.AspNetCore.Authorization.Policy":"*******","Microsoft.AspNetCore.Authentication":"*******","Microsoft.AspNetCore.Authentication.Abstractions":"*******","NLog.Extensions.Logging":"*******","Microsoft.Extensions.ObjectPool":"*******","System.Runtime.Loader":"*******","Microsoft.AspNetCore.Mvc.ApiExplorer":"*******","Microsoft.AspNetCore.Authentication.Core":"*******","System.Security.Claims":"*******","Microsoft.AspNetCore.Mvc.Cors":"*******","Microsoft.AspNetCore.Mvc.DataAnnotations":"*******","FluentValidation":"9.0.0.0","Microsoft.AspNetCore.Metadata":"*******","Swashbuckle.AspNetCore.Swagger":"*******","Microsoft.OpenApi":"1.6.14.0","Microsoft.AspNetCore.WebSockets":"*******","Microsoft.AspNetCore.Http.Connections":"*******","Microsoft.AspNetCore.DataProtection.Abstractions":"*******","Microsoft.AspNetCore.Cryptography.Internal":"*******","Microsoft.AspNetCore.Authentication.Cookies":"*******","Microsoft.Extensions.WebEncoders":"*******","System.Text.Encodings.Web":"*******","System.Data.SQLite":"1.0.115.5","Sonarr.SignalR":"4.0.15.2941","Sonarr.Mono":"4.0.15.2941","System.Linq.Expressions":"*******","System.Security.Cryptography.X509Certificates":"*******","System.Collections.Immutable":"*******","FFMpegCore":"*******","Equ":"*******","Newtonsoft.Json":"********","System.Runtime.Numerics":"*******","FluentMigrator":"*******","FluentMigrator.Abstractions":"*******","FluentMigrator.Runner.SQLite":"*******","FluentMigrator.Runner.Core":"*******","System.Data.Common":"*******","Dapper":"*******","System.Collections.Specialized":"*******","System.Net.Requests":"*******","System.IO.FileSystem.DriveInfo":"*******","System.Net.Sockets":"*******","System.IO.Pipelines":"*******","Mono.Posix.NETStandard":"*******","System.ServiceProcess.ServiceController":"*******","System.Collections.NonGeneric":"*******","System.Xml.XDocument":"*******","System.Private.Xml.Linq":"*******","Microsoft.AspNetCore.Diagnostics":"*******","Npgsql":"*******","FluentMigrator.Runner":"*******","FluentMigrator.Runner.Postgres":"*******","System.Threading.ThreadPool":"*******","System.ComponentModel.Annotations":"*******","System.Transactions.Local":"*******","System.Diagnostics.TraceSource":"*******","System.Net.ServicePoint":"*******","System.Net.Security":"*******","Instances":"*******","System.IO.Pipes":"*******","System.Runtime.Intrinsics":"*******","System.Numerics.Vectors":"*******","System.Runtime.CompilerServices.Unsafe":"*******","SixLabors.ImageSharp":"*******","Microsoft.AspNetCore.Http.Connections.Common":"*******","Microsoft.Extensions.Localization.Abstractions":"*******","Microsoft.Net.Http.Headers":"*******","Microsoft.AspNetCore.Http.Features":"*******","Microsoft.AspNetCore.Http.Extensions":"*******","Microsoft.AspNetCore.StaticFiles":"*******","Microsoft.CSharp":"*******","Microsoft.AspNetCore.WebUtilities":"*******","System.Net.WebSockets":"*******","System.Net.NameResolution":"*******","System.Net.NetworkInformation":"*******","System.Security.Cryptography.Encoding":"*******","System.Formats.Asn1":"*******","System.Security.Cryptography.OpenSsl":"*******","System.Threading.Channels":"*******","System.IO.Compression.Brotli":"*******","System.Runtime.Serialization.Formatters":"*******","System.Runtime.Serialization.Primitives":"*******","System.Reflection.Metadata":"*******","System.IO.MemoryMappedFiles":"*******","System.Net.WebProxy":"*******","ICSharpCode.SharpZipLib":"********"},"event_id":"52de1ef81c8d44b2b3d12083f7ae736b","timestamp":"2025-07-15T19:44:27.9275407+00:00","logentry":{"message":"Failed to Update Scene Mappings"},"logger":"SceneMappingService","platform":"csharp","release":"4.0.15.2941-main","exception":{"values":[{"type":"System.Net.Sockets.SocketException","value":"Resource temporarily unavailable","module":"System.Net.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","thread_id":17,"stacktrace":{"frames":[{"function":"async ValueTask\u003CValueTuple\u003CSocket, Stream\u003E\u003E HttpConnectionPool.ConnectToTcpHostAsync(string host, int port, HttpRequestMessage initialRequest, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x2e2","addr_mode":"rel:2","function_id":"0x783"},{"filename":"./Sonarr.Common/Http/Dispatchers/ManagedHttpDispatcher.cs","function":"async ValueTask\u003CStream\u003E ManagedHttpDispatcher.onConnect(SocketsHttpConnectionContext context, CancellationToken cancellationToken)","lineno":314,"colno":9,"in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x1b3","addr_mode":"rel:1","function_id":"0x69c"},{"filename":"./Sonarr.Common/Http/Dispatchers/ManagedHttpDispatcher.cs","function":"async ValueTask\u003CStream\u003E ManagedHttpDispatcher.attemptConnection(AddressFamily addressFamily, SocketsHttpConnectionContext context, CancellationToken cancellationToken)","lineno":338,"colno":9,"in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xfb","addr_mode":"rel:1","function_id":"0x69a"},{"function":"async Task Socket.ConnectAsync(EndPoint remoteEP)\u002BWaitForConnectWithCancellation(?)","in_app":false,"package":"System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x12c","addr_mode":"rel:0","function_id":"0x2c9"},{"function":"void AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x10","addr_mode":"rel:0","function_id":"0x2ba"}]},"mechanism":{"type":"chained","source":"InnerException","handled":true,"exception_id":1,"parent_id":0}},{"type":"System.Net.Http.HttpRequestException","value":"Resource temporarily unavailable (thexem.info:443)","module":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","thread_id":17,"stacktrace":{"frames":[{"filename":"./Sonarr.Core/DataAugmentation/Scene/SceneMappingService.cs","function":"void SceneMappingService.UpdateMappings()","lineno":143,"colno":21,"in_app":true,"package":"Sonarr.Core, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x2f","addr_mode":"rel:3","function_id":"0x3249"},{"filename":"./Sonarr.Core/DataAugmentation/Xem/XemService.cs","function":"List\u003CSceneMapping\u003E XemService.GetSceneMappings()","lineno":232,"colno":13,"in_app":true,"package":"Sonarr.Core, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xc","addr_mode":"rel:3","function_id":"0x3206"},{"filename":"./Sonarr.Core/DataAugmentation/Xem/XemProxy.cs","function":"List\u003CSceneMapping\u003E XemProxy.GetSceneTvdbNames()","lineno":81,"colno":13,"in_app":true,"package":"Sonarr.Core, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x3c","addr_mode":"rel:3","function_id":"0x31ff"},{"function":"HttpResponse\u003CT\u003E HttpClient.Get\u003CT\u003E(HttpRequest request)","in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x0","addr_mode":"rel:1","function_id":"0x1fa"},{"filename":"./Sonarr.Common/Http/HttpClient.cs","function":"async Task\u003CHttpResponse\u003CT\u003E\u003E HttpClient.GetAsync\u003CT\u003E(HttpRequest request)","lineno":336,"colno":9,"in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xb4","addr_mode":"rel:1","function_id":"0x671"},{"filename":"./Sonarr.Common/Http/HttpClient.cs","function":"async Task\u003CHttpResponse\u003E HttpClient.ExecuteAsync(HttpRequest request)","lineno":70,"colno":13,"in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x8d","addr_mode":"rel:1","function_id":"0x66d"},{"filename":"./Sonarr.Common/Http/HttpClient.cs","function":"async Task\u003CHttpResponse\u003E HttpClient.ExecuteRequestAsync(HttpRequest request, CookieContainer cookieContainer)","lineno":157,"colno":13,"in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x18a","addr_mode":"rel:1","function_id":"0x66f"},{"filename":"./Sonarr.Common/Http/Dispatchers/ManagedHttpDispatcher.cs","function":"async Task\u003CHttpResponse\u003E ManagedHttpDispatcher.GetResponseAsync(HttpRequest request, CookieContainer cookies)","lineno":115,"colno":17,"in_app":true,"package":"Sonarr.Common, Version=4.0.15.2941, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x2e0","addr_mode":"rel:1","function_id":"0x698"},{"function":"async Task\u003CHttpResponseMessage\u003E HttpClient.SendAsync(HttpRequestMessage request)\u002BCore(?)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x216","addr_mode":"rel:2","function_id":"0x23f"},{"function":"async ValueTask\u003CHttpResponseMessage\u003E DecompressionHandler.SendAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x255","addr_mode":"rel:2","function_id":"0x580"},{"function":"async ValueTask\u003CHttpResponseMessage\u003E AuthenticationHelper.SendWithAuthAsync(HttpRequestMessage request, Uri authUri, bool async, ICredentials credentials, bool preAuthenticate, bool isProxyAuth, bool doRequestAuth, HttpConnectionPool pool, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x737","addr_mode":"rel:2","function_id":"0x434"},{"function":"async ValueTask\u003CHttpResponseMessage\u003E HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, bool async, bool doRequestAuth, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x64c","addr_mode":"rel:2","function_id":"0x775"},{"function":"async ValueTask\u003CHttp2Connection\u003E HttpConnectionPool.GetHttp2ConnectionAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x27f","addr_mode":"rel:2","function_id":"0x76f"},{"function":"async ValueTask\u003CT\u003E TaskCompletionSourceWithCancellation\u003CT\u003E.WaitWithCancellationAsync(CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0xf3","addr_mode":"rel:2","function_id":"0xc2"},{"function":"async Task HttpConnectionPool.AddHttp2ConnectionAsync(HttpRequestMessage request)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x421","addr_mode":"rel:2","function_id":"0x76b"},{"function":"async ValueTask\u003CValueTuple\u003CSocket, Stream, TransportContext\u003E\u003E HttpConnectionPool.ConnectAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x41a","addr_mode":"rel:2","function_id":"0x781"},{"function":"async ValueTask\u003CValueTuple\u003CSocket, Stream\u003E\u003E HttpConnectionPool.ConnectToTcpHostAsync(string host, int port, HttpRequestMessage initialRequest, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x2e2","addr_mode":"rel:2","function_id":"0x783"}]},"mechanism":{"type":"generic","handled":true,"exception_id":0}}]},"level":"error","request":{},"contexts":{"Current Culture":{"display_name":"Invariant Language (Invariant Country)","calendar":"GregorianCalendar"},"Dynamic Code":{"Compiled":true,"Supported":true},"Memory Info":{"allocated_bytes":10708939352,"fragmented_bytes":13282088,"heap_size_bytes":41592328,"high_memory_load_threshold_bytes":11333007360,"total_available_memory_bytes":12592230400,"memory_load_bytes":3777669120,"total_committed_bytes":44011520,"promoted_bytes":39616,"pause_time_percentage":0.01,"index":41506,"finalization_pending_count":5,"compacted":true,"concurrent":false,"pause_durations":[3.758,0]},"ThreadPool Info":{"min_worker_threads":1,"min_completion_port_threads":1,"max_worker_threads":32767,"max_completion_port_threads":1000,"available_worker_threads":32767,"available_completion_port_threads":1000},"app":{"type":"app","app_start_time":"2025-07-09T22:31:54.4759923+00:00","device_app_hash":"0d29a1a5","app_name":"Sonarr","app_version":"4.0.15.2941","app_build":"4.0.15.2941-main"},"device":{"type":"device","timezone":"Asia/Shanghai","timezone_display_name":"(UTC\u002B08:00) China Standard Time (Shanghai)","boot_time":"2025-07-09T22:31:44.1128145+00:00"},"os":{"type":"os","raw_description":"Linux 6.14.10-orbstack-00291-g1b252bd3edea #1 SMP Sat Jun  7 02:45:18 UTC 2025"},"runtime":{"type":"runtime","name":".NET","version":"6.0.13","raw_description":".NET 6.0.13","identifier":"alpine.3.21-arm64"},"trace":{"type":"trace","span_id":"034c04973c94fff4","trace_id":"f5c3eaf33db747dc8bdff916f59c87dd"}},"user":{"id":"0d29a1a5","ip_address":"{{auto}}"},"environment":"main","sdk":{"packages":[{"name":"nuget:sentry.dotnet","version":"4.0.2"}],"name":"sentry.dotnet","version":"4.0.2"},"fingerprint":["Error","SceneMappingService","Failed to Update Scene Mappings","System.Net.Http.HttpRequestException","Void MoveNext()","System.Net.Sockets.SocketException"],"breadcrumbs":[{"timestamp":"2025-07-14T02:20:54.340Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T02:39:54.992Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T02:41:55.170Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T02:58:55.421Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T03:17:55.931Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T03:36:56.436Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T03:41:56.657Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T03:55:56.946Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T04:14:57.574Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T04:34:22.994Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T04:35:24.801Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T04:42:23.269Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T04:42:23.280Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T04:42:28.291Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T04:53:23.548Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T05:12:24.160Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T05:31:24.609Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T05:42:54.951Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T05:50:25.071Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T06:09:25.436Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T06:28:25.899Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T06:42:56.433Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T06:47:26.494Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T07:06:27.060Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T07:25:27.559Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T07:42:58.207Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T07:43:03.223Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T07:44:28.199Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T08:03:28.773Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T08:22:29.252Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T08:41:29.659Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T08:43:29.776Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T09:00:29.997Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T09:19:30.621Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T09:38:31.084Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T09:44:31.343Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T09:57:31.553Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T10:16:32.049Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T10:35:32.364Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T10:35:34.189Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T10:45:02.573Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T10:45:02.584Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T10:45:07.605Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T10:54:32.805Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T11:13:33.316Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T11:32:33.710Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T11:45:33.991Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T11:51:33.996Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T12:10:34.253Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T12:29:34.697Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T12:45:35.089Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T12:48:35.103Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T13:07:35.432Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T13:26:35.806Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T13:45:36.121Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T13:46:06.200Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T13:46:11.206Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T14:04:36.415Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T14:23:36.782Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T14:42:37.174Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T14:46:07.313Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T15:01:37.552Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T15:20:37.882Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T15:39:38.256Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T15:47:08.497Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T15:58:38.627Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T16:17:38.910Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T16:35:40.107Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T16:36:39.253Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T16:47:39.480Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T16:47:39.485Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T16:47:44.502Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T16:55:39.585Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T17:14:39.946Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T17:33:40.339Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T17:47:40.693Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T17:52:40.727Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T18:11:41.109Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T18:30:41.397Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T18:48:41.723Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T18:49:41.656Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T19:08:42.091Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T19:27:42.500Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T19:46:42.928Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T19:48:43.008Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T19:48:48.025Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T20:05:43.179Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T20:24:43.432Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T20:43:43.686Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T20:48:43.805Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T21:02:44.036Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T21:21:44.467Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T21:40:44.892Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T21:48:45.106Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T21:59:45.207Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T22:18:45.432Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T22:35:46.811Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T22:37:45.944Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T22:48:46.455Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T22:48:46.463Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T22:48:51.485Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-14T22:56:46.722Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T23:15:47.266Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T23:34:47.809Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-14T23:48:48.313Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-14T23:53:48.350Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T00:12:48.989Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T00:31:49.586Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T00:48:50.461Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T00:50:50.428Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T01:09:51.194Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T01:28:52.094Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T01:47:52.931Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T01:49:53.140Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T01:49:58.167Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T02:06:53.666Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T02:25:54.341Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T02:44:54.894Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T02:49:55.283Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T03:03:55.728Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T03:22:56.337Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T03:41:56.933Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T03:50:27.245Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T04:00:57.544Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T04:19:58.162Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T04:36:00.450Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T04:38:58.816Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T04:50:29.308Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T04:50:29.318Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T04:50:34.335Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T04:57:59.480Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T05:16:59.996Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T05:36:00.705Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T05:51:31.560Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T05:55:01.542Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T06:14:02.383Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T06:33:03.033Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T06:52:03.664Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T06:52:03.730Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T07:11:04.204Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T07:30:04.892Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T07:49:05.665Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T07:52:05.836Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T07:52:10.886Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T08:08:06.284Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T08:27:07.098Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T08:46:07.811Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T08:52:38.280Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T09:05:08.507Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T09:24:09.119Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T09:43:09.892Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T09:53:40.411Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T10:02:10.627Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T10:21:11.266Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T10:36:12.937Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T10:40:12.032Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T10:53:42.592Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T10:53:42.596Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T10:53:47.609Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T10:59:12.646Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T11:18:13.125Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T11:37:13.896Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T11:53:44.372Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T11:56:14.335Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T12:15:14.738Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T12:34:15.047Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T12:53:15.330Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T12:54:15.429Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T13:12:15.693Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T13:31:16.033Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T13:50:16.467Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T13:54:16.623Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T13:54:21.643Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T14:09:16.870Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T14:28:17.201Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T14:47:17.520Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T14:55:17.750Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T15:06:17.894Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T15:25:18.278Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T15:44:18.618Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T15:56:18.853Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T16:03:18.944Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T16:22:19.234Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T16:36:20.738Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T16:41:19.587Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T16:56:49.931Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T16:56:49.938Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T16:56:54.957Z","message":"HTTP Error - Res: HTTP/1.1 [POST] http://************:60219/api/v2/auth/login: 403.Forbidden (50 bytes)\n\u4F60\u7684IP\u4F4D\u5740\u56E0\u591A\u6B21\u9A57\u8B49\u5931\u6557\u800C\u88AB\u5C01\u9396\u3002","category":"HttpClient","level":"warning"},{"timestamp":"2025-07-15T17:00:19.942Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T17:19:20.280Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T17:38:20.572Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T17:56:50.971Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T17:57:20.910Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T18:16:21.230Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T18:35:21.564Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T18:54:21.899Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T18:56:52.018Z","message":"Unable to retrieve queue and history items from qBittorrent","category":"DownloadMonitoringService","level":"warning"},{"timestamp":"2025-07-15T19:13:22.289Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T19:32:22.680Z","message":"No available indexers. check your configuration.","category":"FetchAndParseRssService","level":"warning"},{"timestamp":"2025-07-15T19:44:27.925Z","message":"Failed to Update Scene Mappings","category":"SceneMappingService","level":"error"}],"tags":{"branch":"main","database_migration":"217","sqlite_version":"3.48.0","is_docker":"True","runtime_version":".NET 6.0.13","culture":""},"debug_meta":{"images":[{"type":"pe_dotnet","debug_id":"717b1e4f-3a32-4aee-b2a9-2d845f22043b-ce01dac6","debug_file":"/__w/1/s/artifacts/obj/System.Net.Sockets/net6.0-Unix-Release/System.Net.Sockets.pdb","code_id":"CE01DAC6d1400","code_file":"/app/sonarr/bin/System.Net.Sockets.dll"},{"type":"pe_dotnet","debug_id":"351f4f1d-ba00-44d4-ba7d-327a2cd9e260-d6eb4f50","debug_checksum":"SHA256:1d4f1f3500bad4e4fa7d327a2cd9e260504febd6f812612bbc1e21088c27d37a","debug_file":"D:\\a\\Sonarr\\Sonarr\\_temp\\obj\\Sonarr.Common\\Release\\net6.0\\linux-musl-arm64\\Sonarr.Common.pdb","code_id":"D6F02C5B48000","code_file":"/app/sonarr/bin/Sonarr.Common.dll"},{"type":"pe_dotnet","debug_id":"e99123c3-4eec-440d-b2c6-eb4e6625c8a6-ec347b33","debug_file":"/__w/1/s/artifacts/obj/System.Net.Http/net6.0-Linux-Release/System.Net.Http.pdb","code_id":"EC347B33210a00","code_file":"/app/sonarr/bin/System.Net.Http.dll"},{"type":"pe_dotnet","debug_id":"27da7555-e663-4d35-8afe-fb32a7616a30-9b8c51f3","debug_checksum":"SHA256:5575da2763e6351d4afefb32a7616a30f3518c9b46af7eddfb7a2107496e5b7a","debug_file":"D:\\a\\Sonarr\\Sonarr\\_temp\\obj\\Sonarr.Core\\Release\\net6.0\\linux-musl-arm64\\Sonarr.Core.pdb","code_id":"C2DC2BF1278000","code_file":"/app/sonarr/bin/Sonarr.Core.dll"}]}}
