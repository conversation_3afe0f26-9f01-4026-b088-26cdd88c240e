# 多阶段构建优化的Docker镜像
# 第一阶段：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    && rm -rf /var/cache/apk/*

# 复制package文件
COPY package*.json ./

# 安装依赖（仅生产依赖）
RUN npm ci --only=production --silent

# 复制源代码
COPY . .

# 运行代码质量检查和构建
RUN npm run lint:fix || true
RUN npm run build || true

# 清理开发依赖和缓存
RUN npm prune --production
RUN npm cache clean --force

# 第二阶段：运行阶段
FROM node:18-alpine AS runtime

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S blackbear -u 1001

# 安装运行时依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder --chown=blackbear:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=blackbear:nodejs /app/package*.json ./
COPY --from=builder --chown=blackbear:nodejs /app/app ./app
COPY --from=builder --chown=blackbear:nodejs /app/config ./config
COPY --from=builder --chown=blackbear:nodejs /app/database ./database
COPY --from=builder --chown=blackbear:nodejs /app/typings ./typings

# 创建日志目录
RUN mkdir -p /app/logs && chown blackbear:nodejs /app/logs

# 设置环境变量
ENV NODE_ENV=production
ENV EGG_SERVER_ENV=prod
ENV PORT=7001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/api/health || exit 1

# 切换到非root用户
USER blackbear

# 暴露端口
EXPOSE 7001

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["npm", "start"]
