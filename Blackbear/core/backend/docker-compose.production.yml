version: '3.8'

services:
  # 应用服务
  blackbear-backend:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        NODE_ENV: production
    container_name: blackbear-backend
    restart: unless-stopped
    ports:
      - "7001:7001"
    environment:
      - NODE_ENV=production
      - EGG_SERVER_ENV=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=${DB_USERNAME:-blackbear}
      - DB_PASSWORD=${DB_PASSWORD:-blackbear123}
      - DB_DATABASE=${DB_DATABASE:-blackbear}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-key}
      - BB_API_URL=${BB_API_URL:-http://localhost:7001}
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - blackbear-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: blackbear-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root123}
      - MYSQL_DATABASE=${DB_DATABASE:-blackbear}
      - MYSQL_USER=${DB_USERNAME:-blackbear}
      - MYSQL_PASSWORD=${DB_PASSWORD:-blackbear123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
      - ./config/mysql.cnf:/etc/mysql/conf.d/custom.cnf:ro
    networks:
      - blackbear-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root123}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    command: --default-authentication-plugin=mysql_native_password
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: blackbear-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - blackbear-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: blackbear-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/sites:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - blackbear-backend
    networks:
      - blackbear-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: blackbear-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - blackbear-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: blackbear-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - blackbear-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  # 日志收集 - Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: blackbear-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/app/logs:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=${ELASTICSEARCH_HOSTS:-http://elasticsearch:9200}
    networks:
      - blackbear-network
    depends_on:
      - blackbear-backend

# 网络配置
networks:
  blackbear-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local
