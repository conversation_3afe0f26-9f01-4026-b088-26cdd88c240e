'use strict';

// 核心依赖
const LocalStrategy = require('passport-local').Strategy;
const crypto = require('crypto');
const uuid = require('uuid');
const lodash = require('lodash');
const moment = require('moment');
const path = require('path');
const fs = require('fs');

// 应用模块
const { permissionsToRedis, generateApikey } = require('./app-boot-hook-do');

// 可选模块
// const puppeteerPool = require('./utils/puppeteer-pool');

/**
 * 本地登录处理器
 * 处理用户名密码登录
 * @param {Object} ctx - Koa上下文
 * @param {Object} credentials - 登录凭据
 * @param {string} credentials.username - 用户名
 * @param {string} credentials.password - 密码
 * @return {Object|null} 用户信息
 */
const localHandler = async (ctx, { username, password }) => {
  try {
    // 查找用户
    const userInfo = await ctx.model.User.findOne({
      where: {
        username,
        password: crypto.createHash('md5').update(password).digest('hex'),
      },
    });

    // 登录用户
    if (userInfo) {
      ctx.login(userInfo);
    }

    return userInfo;
  } catch (error) {
    ctx.logger.error('Local login error:', error);
    return null;
  }
};

/**
 * 微博登录处理器
 * 处理微博第三方登录，创建或更新用户信息
 * @param {Object} ctx - Koa上下文
 * @param {Object} user - 微博用户信息
 * @return {Object|null} 用户信息
 */
const weiboHandler = async (ctx, user) => {
  const transaction = await ctx.model.transaction();

  try {
    // 查找现有用户
    let existUser = await ctx.model.User.findOne({
      where: { weiboUserId: user.id },
    }, { transaction });

    // 获取默认角色
    const defaultRole = await ctx.model.Role.findOne({
      where: { isDefault: true },
    }, { transaction });

    // 构建用户数据
    const userData = {
      weiboUserId: user.id,
      username: `WB${user.id}`,
      nickname: user.name,
      weiboToken: user.accessToken,
      photo: user.photo,
      dict: JSON.stringify(user),
    };

    if (!existUser) {
      // 创建新用户
      existUser = await ctx.model.User.create({
        id: uuid.v4(),
        ...userData,
        roleId: defaultRole ? defaultRole.code : 'tourists',
        homeUrl: 'admin-welcome',
        from: 'weibo',
      }, { transaction });
    } else {
      // 更新现有用户
      existUser = await existUser.update(userData, { transaction });
    }

    // 缓存微博访问令牌
    await ctx.service.cache.setex('weiboAccessToken', user.accessToken);

    // 生成应用令牌并更新用户登录信息
    const token = await ctx.service.users.generateToken(existUser.dataValues);
    await existUser.update({
      ip: ctx.request.ip,
      token,
      lastLogin: moment().format('YYYY-MM-DD HH:mm:ss'),
    }, { transaction });

    // 提交事务
    await transaction.commit();

    // 登录用户
    ctx.login(existUser);
    return existUser;

  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    ctx.logger.error('Weibo login error:', error);
    return null;
  }
};

/**
 * 应用启动钩子类
 * 处理应用启动过程中的各个生命周期阶段
 */
class AppBootHook {
  constructor(app) {
    this.app = app;

    // 设置应用运行状态，供健康监测接口使用
    app.running_status = true;

    // 监听进程信号，优雅关闭
    process.on('SIGINT', () => {
      app.running_status = false;
    });
  }

  /**
   * 配置文件已读取合并但还未生效
   * 修改配置的最后时机，仅支持同步操作
   */
  configWillLoad() {
    // 配置预处理逻辑
  }

  /**
   * 所有配置已经加载完毕
   * 用于自定义 Loader 挂载和 Passport 策略配置
   */
  configDidLoad() {
    // 配置本地登录策略
    this.app.passport.use(
      new LocalStrategy({
        passReqToCallback: true,
      }, (req, username, password, done) => {
        const user = {
          provider: 'local',
          username,
          password,
        };
        this.app.passport.doVerify(req, user, done);
      })
    );

    // 配置用户验证处理器
    this.app.passport.verify(async (ctx, user) => {
      ctx.logger.debug('passport.verify', user);

      // 根据登录方式选择处理器
      const handler = user.provider === 'weibo' ? weiboHandler : localHandler;
      const existUser = await handler(ctx, user);

      return existUser;
    });

    // 序列化用户信息到session
    this.app.passport.serializeUser(async (ctx, user) => {
      return user;
    });

    // 从session反序列化用户信息
    this.app.passport.deserializeUser(async (ctx, user) => {
      return user;
    });
  }

  /**
   * 插件初始化阶段
   * 初始化第三方插件和工具
   */
  async didLoad() {
    // 初始化 Puppeteer 连接池（可选）
    // this.app.pool = puppeteerPool();

    // 初始化 Socket.IO 性能监控工具
    await this.initSocketPerformanceUtils();
  }

  /**
   * 初始化 Socket.IO 性能监控工具
   * @private
   */
  async initSocketPerformanceUtils() {
    try {
      const SocketPerformanceUtils = require('./app/io/utils/socket-performance');
      this.app.socketPerformanceUtils = new SocketPerformanceUtils(this.app);

      // 监听 Socket.IO 连接事件
      if (this.app.io) {
        this.app.io.on('connection', socket => {
          // 记录新连接
          this.app.socketPerformanceUtils.recordConnection(
            socket.id,
            socket.handshake.query
          );

          // 监听连接断开
          socket.on('disconnect', () => {
            this.app.socketPerformanceUtils.removeConnection(socket.id);
          });
        });

        this.app.logger.info('Socket.IO performance monitoring initialized');
      }
    } catch (error) {
      this.app.logger.warn('Socket.IO performance utils initialization failed:', error.message);
    }
  }

  /**
   * 应用启动前的准备工作
   * 所有插件启动完毕，执行必要的前置操作
   */
  async willReady() {
    // 挂载工具库
    this.app.lodash = lodash;

    // 数据库同步（可选）
    // await this.app.model.sync({ force: false });

    // 加载权限数据到Redis
    await this.loadPermissionsToRedis();

    // 生成API密钥
    await this.generateSystemApikey();
  }

  /**
   * 加载权限数据到Redis
   * @private
   */
  async loadPermissionsToRedis() {
    try {
      await permissionsToRedis(this.app);
      this.app.logger.info('Permissions loaded to Redis successfully');
    } catch (error) {
      this.app.logger.error('Failed to load permissions to Redis:', error);
      throw error;
    }
  }

  /**
   * 生成系统API密钥
   * @private
   */
  async generateSystemApikey() {
    try {
      await generateApikey(this.app);
      this.app.logger.info('System API key generated successfully');
    } catch (error) {
      this.app.logger.error('Failed to generate API key:', error);
      throw error;
    }
  }

  /**
   * 应用启动完毕后的初始化工作
   * 创建必要的目录、用户和启动监控服务
   */
  async didReady() {
    // 初始化上传目录
    await this.initUploadDirectories();

    // 创建系统用户
    await this.createSystemUsers();

    // 启动性能监控
    await this.startPerformanceMonitoring();
  }

  /**
   * 初始化上传目录
   * @private
   */
  async initUploadDirectories() {
    try {
      const { dir, upload_dir } = this.app.config.static;
      const public_uploads = path.join(dir, upload_dir);

      // 设置上传路径配置
      this.app.config.static.public_uploads_path = public_uploads;

      // 创建上传目录
      if (!fs.existsSync(public_uploads)) {
        fs.mkdirSync(public_uploads, { recursive: true });
        this.app.logger.info(`Upload directory created: ${public_uploads}`);
      }
    } catch (error) {
      this.app.logger.error('Failed to initialize upload directories:', error);
      throw error;
    }
  }

  /**
   * 创建系统用户
   * @private
   */
  async createSystemUsers() {
    try {
      // 创建系统机器人用户
      await this.app.model.User.findOrCreate({
        where: {
          id: this.app.config.systemUserId,
        },
        defaults: {
          id: this.app.config.systemUserId,
          username: 'robot',
          name: '机器人',
          nickname: '系统',
        },
      });

      this.app.logger.info('System users initialized');
    } catch (error) {
      this.app.logger.error('Failed to create system users:', error);
      throw error;
    }
  }

  /**
   * 启动性能监控服务
   * @private
   */
  async startPerformanceMonitoring() {
    if (!this.app.socketPerformanceUtils) {
      return;
    }

    try {
      // 开发环境启动定期性能监控
      if (this.app.config.env !== 'prod') {
        setInterval(() => {
          const metrics = this.app.socketPerformanceUtils.getPerformanceMetrics();
          this.app.logger.info('Socket.IO Performance Metrics:', metrics);
        }, 60000); // 每分钟输出一次
      }

      // 定期内存优化
      setInterval(() => {
        this.app.socketPerformanceUtils.optimizeMemory();
      }, 10 * 60 * 1000); // 每10分钟优化一次

      this.app.logger.info('Socket.IO performance monitoring started');
    } catch (error) {
      this.app.logger.error('Failed to start performance monitoring:', error);
    }
  }

  /**
   * 服务器启动完成
   * 可以开始处理外部请求
   */
  async serverDidReady() {
    // 服务器超时处理（可选）
    // this.app.server.on('timeout', socket => {
    //   this.app.logger.warn('Server timeout detected');
    // });

    this.app.logger.info('Server is ready to handle requests');
  }

  /**
   * 应用即将关闭
   * 执行清理工作
   */
  async beforeClose() {
    this.app.logger.info('Application is shutting down...');

    // 清理 Puppeteer 连接池（如果使用）
    // if (this.app.pool && this.app.pool.drain) {
    //   await this.app.pool.drain().then(() => {
    //     this.app.pool.clear();
    //   });
    // }

    // 清理 Socket.IO 性能监控
    if (this.app.socketPerformanceUtils) {
      this.app.socketPerformanceUtils.optimizeMemory();
    }

    this.app.logger.info('Application cleanup completed');
  }
}

module.exports = AppBootHook;
