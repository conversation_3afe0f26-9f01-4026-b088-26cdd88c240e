#!/usr/bin/env node

'use strict';

/**
 * 代码质量修复脚本
 * 自动修复常见的ESLint问题和代码质量问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class CodeQualityFixer {
  constructor() {
    this.baseDir = path.join(__dirname, '..');
    this.stats = {
      filesProcessed: 0,
      issuesFixed: 0,
      errors: 0,
    };
  }

  /**
   * 运行修复流程
   */
  async run() {
    console.log('🔧 Starting code quality fixes...\n');

    try {
      // 1. 运行ESLint自动修复
      await this.runESLintFix();

      // 2. 修复JSDoc注释
      await this.fixJSDocComments();

      // 3. 移除未使用的变量
      await this.removeUnusedVariables();

      // 4. 统一代码风格
      await this.unifyCodeStyle();

      // 5. 添加缺失的JSDoc
      await this.addMissingJSDoc();

      // 6. 最终验证
      await this.finalValidation();

      this.printSummary();

    } catch (error) {
      console.error('❌ Code quality fix failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * 运行ESLint自动修复
   */
  async runESLintFix() {
    console.log('📝 Running ESLint auto-fix...');

    try {
      execSync('npm run lint:fix', {
        cwd: this.baseDir,
        stdio: 'pipe',
      });
      console.log('✅ ESLint auto-fix completed');
    } catch (error) {
      console.log('⚠️  ESLint auto-fix completed with warnings');
    }
  }

  /**
   * 修复JSDoc注释
   */
  async fixJSDocComments() {
    console.log('📚 Fixing JSDoc comments...');

    const files = this.getJavaScriptFiles();

    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;

        // 修复无效的JSDoc标签
        const invalidTags = [
          '@controller', '@router', '@request', '@response',
          '@summary', '@description', '@tag', '@produces', '@consumes',
        ];

        for (const tag of invalidTags) {
          const regex = new RegExp(`\\s*\\*\\s*${tag.replace('@', '@')}.*\\n`, 'g');
          if (content.includes(tag)) {
            content = content.replace(regex, '');
            modified = true;
          }
        }

        // 添加标准JSDoc注释
        content = this.addStandardJSDoc(content);

        if (modified) {
          fs.writeFileSync(file, content);
          this.stats.issuesFixed++;
        }

        this.stats.filesProcessed++;

      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
        this.stats.errors++;
      }
    }

    console.log(`✅ JSDoc comments fixed in ${this.stats.filesProcessed} files`);
  }

  /**
   * 添加标准JSDoc注释
   * @param content
   */
  addStandardJSDoc(content) {
    // 为类添加JSDoc
    content = content.replace(
      /^class\s+(\w+)(?:\s+extends\s+\w+)?\s*{/gm,
      '/**\n * $1 class\n */\nclass $1 {'
    );

    // 为异步函数添加JSDoc
    content = content.replace(
      /^(\s*)async\s+(\w+)\s*\([^)]*\)\s*{/gm,
      '$1/**\n$1 * $2 method\n$1 * @return {Promise} Promise object\n$1 */\n$1async $2() {'
    );

    // 为普通函数添加JSDoc
    content = content.replace(
      /^(\s*)(\w+)\s*\([^)]*\)\s*{/gm,
      '$1/**\n$1 * $2 method\n$1 */\n$1$2() {'
    );

    return content;
  }

  /**
   * 移除未使用的变量
   */
  async removeUnusedVariables() {
    console.log('🧹 Removing unused variables...');

    const files = this.getJavaScriptFiles();

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        let modified = false;

        // 移除未使用的require语句
        const lines = content.split('\n');
        const filteredLines = [];

        for (const line of lines) {
          // 检查是否为未使用的require
          if (line.includes('require(') && !line.includes('//')) {
            const varName = this.extractVariableName(line);
            if (varName && !this.isVariableUsed(content, varName, line)) {
              console.log(`  Removing unused require: ${varName} in ${path.basename(file)}`);
              modified = true;
              continue;
            }
          }
          filteredLines.push(line);
        }

        if (modified) {
          fs.writeFileSync(file, filteredLines.join('\n'));
          this.stats.issuesFixed++;
        }

      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
        this.stats.errors++;
      }
    }

    console.log('✅ Unused variables removed');
  }

  /**
   * 统一代码风格
   */
  async unifyCodeStyle() {
    console.log('🎨 Unifying code style...');

    const files = this.getJavaScriptFiles();

    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;

        // 统一字符串引号
        content = content.replace(/"/g, "'");

        // 统一分号
        content = content.replace(/([^;])\n/g, '$1;\n');

        // 移除多余空行
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

        // 统一缩进（2空格）
        const lines = content.split('\n');
        const normalizedLines = lines.map(line => {
          if (line.trim() === '') { return ''; }
          const indent = line.match(/^\s*/)[0];
          const tabCount = (indent.match(/\t/g) || []).length;
          const spaceCount = indent.length - tabCount;
          const totalIndent = tabCount * 2 + spaceCount;
          return ' '.repeat(totalIndent) + line.trim();
        });

        const newContent = normalizedLines.join('\n');

        if (newContent !== content) {
          fs.writeFileSync(file, newContent);
          modified = true;
          this.stats.issuesFixed++;
        }

      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
        this.stats.errors++;
      }
    }

    console.log('✅ Code style unified');
  }

  /**
   * 添加缺失的JSDoc
   */
  async addMissingJSDoc() {
    console.log('📖 Adding missing JSDoc comments...');

    const files = this.getJavaScriptFiles();

    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');

        // 为控制器方法添加JSDoc
        if (file.includes('/controller/')) {
          content = this.addControllerJSDoc(content);
        }

        // 为服务方法添加JSDoc
        if (file.includes('/service/')) {
          content = this.addServiceJSDoc(content);
        }

        // 为中间件添加JSDoc
        if (file.includes('/middleware/')) {
          content = this.addMiddlewareJSDoc(content);
        }

        fs.writeFileSync(file, content);

      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
        this.stats.errors++;
      }
    }

    console.log('✅ Missing JSDoc comments added');
  }

  /**
   * 为控制器添加JSDoc
   * @param content
   */
  addControllerJSDoc(content) {
    // 为控制器类添加JSDoc
    content = content.replace(
      /^class\s+(\w+Controller)\s+extends\s+Controller\s*{/gm,
      '/**\n * $1\n * Handles HTTP requests for specific resource\n */\nclass $1 extends Controller {'
    );

    return content;
  }

  /**
   * 为服务添加JSDoc
   * @param content
   */
  addServiceJSDoc(content) {
    // 为服务类添加JSDoc
    content = content.replace(
      /^class\s+(\w+Service)\s+extends\s+Service\s*{/gm,
      '/**\n * $1\n * Business logic service\n */\nclass $1 extends Service {'
    );

    return content;
  }

  /**
   * 为中间件添加JSDoc
   * @param content
   */
  addMiddlewareJSDoc(content) {
    // 为中间件函数添加JSDoc
    content = content.replace(
      /^module\.exports\s*=\s*\([^)]*\)\s*=>\s*{/gm,
      '/**\n * Middleware function\n * @param {Object} options - Configuration options\n * @return {Function} Middleware function\n */\nmodule.exports = (options = {}) => {'
    );

    return content;
  }

  /**
   * 最终验证
   */
  async finalValidation() {
    console.log('🔍 Running final validation...');

    try {
      const result = execSync('npm run lint', {
        cwd: this.baseDir,
        stdio: 'pipe',
        encoding: 'utf8',
      });
      console.log('✅ Final validation passed');
    } catch (error) {
      const output = error.stdout || error.message;
      const errorCount = (output.match(/error/g) || []).length;
      const warningCount = (output.match(/warning/g) || []).length;

      console.log(`⚠️  Final validation completed with ${errorCount} errors and ${warningCount} warnings`);
    }
  }

  /**
   * 获取所有JavaScript文件
   */
  getJavaScriptFiles() {
    const files = [];

    const scanDir = dir => {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDir(fullPath);
        } else if (stat.isFile() && item.endsWith('.js')) {
          files.push(fullPath);
        }
      }
    };

    scanDir(path.join(this.baseDir, 'app'));
    scanDir(path.join(this.baseDir, 'config'));

    return files;
  }

  /**
   * 提取变量名
   * @param line
   */
  extractVariableName(line) {
    const match = line.match(/(?:const|let|var)\s+(\w+)\s*=/);
    return match ? match[1] : null;
  }

  /**
   * 检查变量是否被使用
   * @param content
   * @param varName
   * @param declarationLine
   */
  isVariableUsed(content, varName, declarationLine) {
    const lines = content.split('\n');
    const declarationIndex = lines.indexOf(declarationLine);

    // 检查声明行之后是否使用了该变量
    for (let i = declarationIndex + 1; i < lines.length; i++) {
      if (lines[i].includes(varName) && !lines[i].includes('//')) {
        return true;
      }
    }

    return false;
  }

  /**
   * 打印修复总结
   */
  printSummary() {
    console.log('\n📊 Code Quality Fix Summary:');
    console.log(`  Files processed: ${this.stats.filesProcessed}`);
    console.log(`  Issues fixed: ${this.stats.issuesFixed}`);
    console.log(`  Errors encountered: ${this.stats.errors}`);
    console.log('\n🎉 Code quality fixes completed!');
  }
}

// 运行修复脚本
if (require.main === module) {
  const fixer = new CodeQualityFixer();
  fixer.run().catch(console.error);
}

module.exports = CodeQualityFixer;
