#!/usr/bin/env node

'use strict';

/**
 * 性能基准测试脚本
 * 验证三个阶段优化的效果
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

class PerformanceBenchmark {
  constructor(options = {}) {
    this.baseURL = options.baseURL || 'http://localhost:7001';
    this.concurrency = options.concurrency || 10;
    this.duration = options.duration || 30000; // 30秒
    this.warmupTime = options.warmupTime || 5000; // 5秒预热
    
    this.results = {
      baseline: {},
      optimized: {},
      comparison: {},
    };
    
    this.token = null;
  }

  /**
   * 运行完整的基准测试
   */
  async run() {
    console.log('🚀 Starting Performance Benchmark Tests\n');
    
    try {
      // 1. 初始化和认证
      await this.initialize();
      
      // 2. 预热系统
      await this.warmup();
      
      // 3. 运行基准测试
      await this.runBenchmarkSuite();
      
      // 4. 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Benchmark failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * 初始化测试环境
   */
  async initialize() {
    console.log('🔧 Initializing test environment...');
    
    try {
      // 检查服务健康状态
      const healthResponse = await axios.get(`${this.baseURL}/api/health`);
      if (healthResponse.status !== 200) {
        throw new Error('Service is not healthy');
      }
      
      // 获取认证token
      const loginResponse = await axios.post(`${this.baseURL}/api/auth/login`, {
        username: 'testuser',
        password: 'TestPass123!',
      });
      
      if (loginResponse.status === 200) {
        this.token = loginResponse.data.data.token;
      } else {
        console.log('⚠️  Authentication failed, running without token');
      }
      
      console.log('✅ Test environment initialized\n');
      
    } catch (error) {
      console.log('⚠️  Initialization warning:', error.message);
    }
  }

  /**
   * 系统预热
   */
  async warmup() {
    console.log('🔥 Warming up system...');
    
    const warmupRequests = [
      '/api/health',
      '/api/stocks?page=1&limit=10',
      '/api/news?page=1&limit=10',
    ];
    
    const startTime = Date.now();
    while (Date.now() - startTime < this.warmupTime) {
      const promises = warmupRequests.map(endpoint => 
        this.makeRequest(endpoint).catch(() => {}) // 忽略预热错误
      );
      await Promise.all(promises);
      await this.sleep(100);
    }
    
    console.log('✅ System warmed up\n');
  }

  /**
   * 运行基准测试套件
   */
  async runBenchmarkSuite() {
    const testSuites = [
      {
        name: 'API Response Time',
        test: () => this.testApiResponseTime(),
      },
      {
        name: 'Database Query Performance',
        test: () => this.testDatabasePerformance(),
      },
      {
        name: 'Cache Performance',
        test: () => this.testCachePerformance(),
      },
      {
        name: 'Concurrent Load',
        test: () => this.testConcurrentLoad(),
      },
      {
        name: 'Memory Usage',
        test: () => this.testMemoryUsage(),
      },
      {
        name: 'Response Compression',
        test: () => this.testResponseCompression(),
      },
    ];

    for (const suite of testSuites) {
      console.log(`📊 Running ${suite.name} benchmark...`);
      try {
        const result = await suite.test();
        this.results.optimized[suite.name] = result;
        console.log(`✅ ${suite.name} completed: ${this.formatResult(result)}\n`);
      } catch (error) {
        console.error(`❌ ${suite.name} failed:`, error.message);
        this.results.optimized[suite.name] = { error: error.message };
      }
    }
  }

  /**
   * 测试API响应时间
   */
  async testApiResponseTime() {
    const endpoints = [
      '/api/health',
      '/api/stocks?page=1&limit=20',
      '/api/news?page=1&limit=20',
      '/api/stocks/search?keyword=测试',
    ];

    const results = {};
    
    for (const endpoint of endpoints) {
      const times = [];
      
      for (let i = 0; i < 50; i++) {
        const startTime = performance.now();
        try {
          await this.makeRequest(endpoint);
          const endTime = performance.now();
          times.push(endTime - startTime);
        } catch (error) {
          // 记录错误但继续测试
        }
      }
      
      if (times.length > 0) {
        results[endpoint] = {
          avg: this.average(times),
          min: Math.min(...times),
          max: Math.max(...times),
          p95: this.percentile(times, 95),
          p99: this.percentile(times, 99),
          count: times.length,
        };
      }
    }
    
    return results;
  }

  /**
   * 测试数据库查询性能
   */
  async testDatabasePerformance() {
    const queries = [
      '/api/stocks?page=1&limit=100',
      '/api/news?page=1&limit=100',
      '/api/stocks/search?keyword=科技',
    ];

    const results = {};
    
    for (const query of queries) {
      const times = [];
      
      for (let i = 0; i < 20; i++) {
        const startTime = performance.now();
        try {
          const response = await this.makeRequest(query);
          const endTime = performance.now();
          times.push(endTime - startTime);
          
          // 检查响应时间头
          if (response.headers['x-response-time']) {
            const serverTime = parseFloat(response.headers['x-response-time']);
            times.push(serverTime);
          }
        } catch (error) {
          // 记录错误
        }
      }
      
      if (times.length > 0) {
        results[query] = {
          avg: this.average(times),
          min: Math.min(...times),
          max: Math.max(...times),
          p95: this.percentile(times, 95),
        };
      }
    }
    
    return results;
  }

  /**
   * 测试缓存性能
   */
  async testCachePerformance() {
    const endpoint = '/api/stocks?page=1&limit=50';
    
    // 第一次请求（缓存未命中）
    const cacheMissTimes = [];
    for (let i = 0; i < 10; i++) {
      const startTime = performance.now();
      try {
        await this.makeRequest(endpoint + `&_t=${Date.now()}`); // 避免缓存
        const endTime = performance.now();
        cacheMissTimes.push(endTime - startTime);
      } catch (error) {
        // 忽略错误
      }
    }
    
    // 等待缓存生效
    await this.sleep(1000);
    
    // 第二次请求（缓存命中）
    const cacheHitTimes = [];
    for (let i = 0; i < 10; i++) {
      const startTime = performance.now();
      try {
        await this.makeRequest(endpoint);
        const endTime = performance.now();
        cacheHitTimes.push(endTime - startTime);
      } catch (error) {
        // 忽略错误
      }
    }
    
    const cacheMissAvg = this.average(cacheMissTimes);
    const cacheHitAvg = this.average(cacheHitTimes);
    const improvement = ((cacheMissAvg - cacheHitAvg) / cacheMissAvg * 100);
    
    return {
      cacheMiss: {
        avg: cacheMissAvg,
        count: cacheMissTimes.length,
      },
      cacheHit: {
        avg: cacheHitAvg,
        count: cacheHitTimes.length,
      },
      improvement: `${improvement.toFixed(2)}%`,
    };
  }

  /**
   * 测试并发负载
   */
  async testConcurrentLoad() {
    const endpoint = '/api/stocks?page=1&limit=20';
    const concurrency = 20;
    const requestsPerWorker = 10;
    
    const startTime = performance.now();
    
    const workers = Array.from({ length: concurrency }, async () => {
      const times = [];
      for (let i = 0; i < requestsPerWorker; i++) {
        const reqStart = performance.now();
        try {
          await this.makeRequest(endpoint);
          const reqEnd = performance.now();
          times.push(reqEnd - reqStart);
        } catch (error) {
          // 记录错误
        }
      }
      return times;
    });
    
    const results = await Promise.all(workers);
    const allTimes = results.flat();
    const totalTime = performance.now() - startTime;
    
    return {
      totalRequests: concurrency * requestsPerWorker,
      successfulRequests: allTimes.length,
      totalTime: totalTime,
      requestsPerSecond: (allTimes.length / (totalTime / 1000)).toFixed(2),
      avgResponseTime: this.average(allTimes),
      p95ResponseTime: this.percentile(allTimes, 95),
      successRate: `${(allTimes.length / (concurrency * requestsPerWorker) * 100).toFixed(2)}%`,
    };
  }

  /**
   * 测试内存使用
   */
  async testMemoryUsage() {
    try {
      const response = await this.makeRequest('/api/health/metrics');
      const metrics = response.data.metrics;
      
      return {
        heapUsed: `${(metrics.memory.heapUsed / 1024 / 1024).toFixed(2)}MB`,
        heapTotal: `${(metrics.memory.heapTotal / 1024 / 1024).toFixed(2)}MB`,
        rss: `${(metrics.memory.rss / 1024 / 1024).toFixed(2)}MB`,
        external: `${(metrics.memory.external / 1024 / 1024).toFixed(2)}MB`,
        heapUsagePercent: `${(metrics.memory.heapUsed / metrics.memory.heapTotal * 100).toFixed(2)}%`,
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * 测试响应压缩
   */
  async testResponseCompression() {
    const endpoint = '/api/stocks?page=1&limit=100';
    
    // 不压缩的请求
    const uncompressedResponse = await this.makeRequest(endpoint, {
      'Accept-Encoding': 'identity',
    });
    
    // 压缩的请求
    const compressedResponse = await this.makeRequest(endpoint, {
      'Accept-Encoding': 'gzip, deflate, br',
    });
    
    const uncompressedSize = JSON.stringify(uncompressedResponse.data).length;
    const compressedSize = compressedResponse.headers['content-length'] || uncompressedSize;
    const compressionRatio = ((uncompressedSize - compressedSize) / uncompressedSize * 100);
    
    return {
      uncompressedSize: `${(uncompressedSize / 1024).toFixed(2)}KB`,
      compressedSize: `${(compressedSize / 1024).toFixed(2)}KB`,
      compressionRatio: `${compressionRatio.toFixed(2)}%`,
      encoding: compressedResponse.headers['content-encoding'] || 'none',
    };
  }

  /**
   * 发起HTTP请求
   */
  async makeRequest(endpoint, headers = {}) {
    const config = {
      url: `${this.baseURL}${endpoint}`,
      method: 'GET',
      headers: {
        ...headers,
      },
      timeout: 10000,
    };
    
    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }
    
    return await axios(config);
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    console.log('\n📈 Performance Benchmark Report');
    console.log('='.repeat(50));
    
    for (const [testName, result] of Object.entries(this.results.optimized)) {
      console.log(`\n${testName}:`);
      console.log('-'.repeat(30));
      
      if (result.error) {
        console.log(`❌ Error: ${result.error}`);
        continue;
      }
      
      this.printResult(result, '  ');
    }
    
    console.log('\n🎯 Performance Summary:');
    console.log('-'.repeat(30));
    this.generateSummary();
  }

  /**
   * 生成性能总结
   */
  generateSummary() {
    const apiResult = this.results.optimized['API Response Time'];
    const cacheResult = this.results.optimized['Cache Performance'];
    const loadResult = this.results.optimized['Concurrent Load'];
    
    if (apiResult && Object.keys(apiResult).length > 0) {
      const avgTimes = Object.values(apiResult).map(r => r.avg);
      const overallAvg = this.average(avgTimes);
      console.log(`  Average API Response Time: ${overallAvg.toFixed(2)}ms`);
    }
    
    if (cacheResult && cacheResult.improvement) {
      console.log(`  Cache Performance Improvement: ${cacheResult.improvement}`);
    }
    
    if (loadResult && loadResult.requestsPerSecond) {
      console.log(`  Requests Per Second: ${loadResult.requestsPerSecond}`);
      console.log(`  Success Rate: ${loadResult.successRate}`);
    }
    
    console.log('\n✅ Benchmark completed successfully!');
  }

  /**
   * 打印结果
   */
  printResult(obj, indent = '') {
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        console.log(`${indent}${key}:`);
        this.printResult(value, indent + '  ');
      } else {
        console.log(`${indent}${key}: ${value}`);
      }
    }
  }

  /**
   * 格式化结果
   */
  formatResult(result) {
    if (result.avg) {
      return `${result.avg.toFixed(2)}ms avg`;
    }
    if (result.requestsPerSecond) {
      return `${result.requestsPerSecond} req/s`;
    }
    if (result.improvement) {
      return `${result.improvement} improvement`;
    }
    return 'completed';
  }

  /**
   * 计算平均值
   */
  average(numbers) {
    return numbers.reduce((a, b) => a + b, 0) / numbers.length;
  }

  /**
   * 计算百分位数
   */
  percentile(numbers, p) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index];
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行基准测试
if (require.main === module) {
  const benchmark = new PerformanceBenchmark({
    baseURL: process.env.BB_API_URL || 'http://localhost:7001',
    concurrency: parseInt(process.env.BENCHMARK_CONCURRENCY) || 10,
    duration: parseInt(process.env.BENCHMARK_DURATION) || 30000,
  });
  
  benchmark.run().catch(console.error);
}

module.exports = PerformanceBenchmark;
