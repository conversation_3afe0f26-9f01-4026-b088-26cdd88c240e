#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Load environment variables
const envFile = path.join(__dirname, '../../prod.env');
const envContent = fs.readFileSync(envFile, 'utf8');

const envVars = {};
envContent
  .split('\n')
  .filter(line => line.trim() && !line.startsWith('#'))
  .forEach(line => {
    const [ key, ...valueParts ] = line.split('=');
    const value = valueParts.join('=');
    envVars[key] = value;
  });

// Database connection info
const dbConfig = {
  host: envVars.BB_MYSQL_HOST,
  port: envVars.BB_MYSQL_PORT,
  user: envVars.BB_MYSQL_USER,
  password: envVars.BB_MYSQL_PASSWD,
  database: envVars.BB_MYSQL_DBNAME,
};

console.log('Database config:', {
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.user,
  database: dbConfig.database,
});

// Use mysql2 for better async support
const mysql = require('mysql2/promise');

async function checkDatabase() {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);

    // Check total research count
    const [ totalRows ] = await connection.execute(
      'SELECT COUNT(*) as total FROM research WHERE deletedAt IS NULL'
    );
    console.log('Total research records:', totalRows[0].total);

    // Check starred research count
    const [ starredRows ] = await connection.execute(
      'SELECT COUNT(*) as starred FROM research WHERE deletedAt IS NULL AND star = true'
    );
    console.log('Starred research records:', starredRows[0].starred);

    // Check a few sample records
    const [ sampleRows ] = await connection.execute(
      'SELECT id, title, star, `read`, type FROM research WHERE deletedAt IS NULL LIMIT 5'
    );
    console.log('Sample research records:');
    sampleRows.forEach(row => {
      console.log(`  ID: ${row.id}, Title: ${row.title}, Star: ${row.star}, Read: ${row.read}, Type: ${row.type}`);
    });

  } catch (error) {
    console.error('Database connection error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkDatabase();
