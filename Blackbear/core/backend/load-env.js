#!/usr/bin/env node

/**
 * 环境变量加载脚本
 * 用于本地开发时加载环境变量
 *
 * 使用方法：
 * node load-env.js && npm run dev
 * 或者
 * source <(node load-env.js) && npm run dev
 */

const fs = require('fs');
const path = require('path');

// 读取prod.env文件
const envFile = path.join(__dirname, '../../prod.env');

if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8');

  // 解析环境变量
  const envVars = envContent
    .split('\n')
    .filter(line => line.trim() && !line.startsWith('#'))
    .map(line => {
      const [ key, ...valueParts ] = line.split('=');
      const value = valueParts.join('=');
      return `${key}=${value}`;
    })
    .join('\n');

  // 输出环境变量（用于source命令）
  console.log(envVars);
} else {
  console.error('prod.env file not found at:', envFile);
  process.exit(1);
}
