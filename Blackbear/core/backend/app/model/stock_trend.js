'use strict';

module.exports = app => {
  const { STRING, INTEGER, DATE, BOOLEAN, DOUBLE, TEXT } = app.Sequelize;

  const StockTrend = app.model.define('stockTrend', {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    code: {
      type: STRING(6),
    },
    score: {
      type: INTEGER,
    },
    rank: {
      type: INTEGER,
    },
    type: {
      type: STRING(12),
    },
    createdAt: DATE,
    updatedAt: DATE,
    
  }, {
    // 禁用软删除，因为股票数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  StockTrend.associate = () => {
    app.model.StockTrend.belongsTo(app.model.Source, { foreignKey: 'code', targetKey: 'code' });
    app.model.StockTrend.belongsTo(app.model.Stock, { foreignKey: 'code', targetKey: 'code' });
  };

  return StockTrend;
};
