'use strict';

module.exports = app => {
  const {
    STRING,
    DATE,
    BOOLEAN,
    TEXT,
    DOUBLE,
  } = app.Sequelize;

  const Stockkline = app.model.define('stockkline', {
    func: {
      type: STRING(20),
      primaryKey: true,
    },
    name: STRING(32),
    ename: STRING(32),
    desc: STRING(360),
    type: STRING(16),
    rate: DOUBLE,
    using: BOOLEAN,
    createdAt: DATE,
    updatedAt: DATE,
    
  }, {
    // 禁用软删除，因为股票数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  return Stockkline;
};
