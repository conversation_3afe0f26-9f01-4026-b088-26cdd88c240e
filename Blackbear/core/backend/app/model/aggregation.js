'use strict';
module.exports = app => {
  const { Sequelize } = app;
  const {
    STRING,
    DATE,
    INTEGER,
    TEXT,
    BOOLEAN,
  } = app.Sequelize;

  const Aggregation = app.model.define('aggregation', {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: STRING(36),
    read: BOOLEAN,
    type: STRING(12),
    mediaId: INTEGER,
    researchId: STRING(36),
    newsId: STRING(64),
    createdAt: DATE,
    updatedAt: DATE,
    deletedAt: DATE,
  });


  Aggregation.associate = function(models) {
    // associations can be defined here
    app.model.Aggregation.belongsTo(app.model.User, {
      foreignKey: 'userId',
      sourceKey: 'id',
    });
    app.model.Aggregation.belongsTo(app.model.Research, {
      foreignKey: 'researchId',
      sourceKey: 'id',
    });
    app.model.Aggregation.belongsTo(app.model.Media, {
      foreignKey: 'mediaId',
      sourceKey: 'id',
      as: 'media', // Added alias to match query
    });
    app.model.Aggregation.belongsTo(app.model.News, {
      foreignKey: 'newsId',
      sourceKey: 'newsID',
    });
  };
  return Aggregation;
};
