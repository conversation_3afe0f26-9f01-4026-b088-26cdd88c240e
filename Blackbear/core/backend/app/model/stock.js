'use strict';

module.exports = app => {
  const { STRING, INTEGER, DATE, BOOLEAN, DOUBLE, TEXT } = app.Sequelize;

  const Stock = app.model.define('stock', {
    code: {
      type: STRING(6),
      primaryKey: true,
    },
    black: {
      type: BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    white: {
      type: BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    firstLevelId: {
      type: INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    secondLevelId: INTEGER,
    rate: DOUBLE,
    comments: INTEGER,
    lastComments: INTEGER,
    diffComments: DOUBLE,
    recentReportCount: INTEGER,
    yoypni: DOUBLE,
    keyword: STRING(32),
    remarks: STRING(125),
    profit_time: STRING(120),
    profit: STRING(120),
    hot: STRING(125),
    desc: TEXT,
    shortDesc: STRING(120),
    limit: INTEGER,
    momentum: <PERSON><PERSON><PERSON>,
    star: Boolean,
    quant: Boolean,
    ret: DOUBLE,
    ret10: DOUBLE,
    ret20: DOUBLE,
    ret100: DOUBLE,
    beta: DOUBLE,
    alpha: DOUBLE,
    roe: DOUBLE,
    peg: DOUBLE,
    md: DOUBLE,
    decisionPercent: DOUBLE,
    isTup: BOOLEAN,
    isHeavyVolume: BOOLEAN,
    isBottomInversion: BOOLEAN,
    emv: BOOLEAN,
    ma: BOOLEAN,
    tags: STRING(120),
    lastscore: DOUBLE,
    score: DOUBLE,
    score1: DOUBLE,
    score2: DOUBLE,
    score3: DOUBLE,
    score4: DOUBLE,
    thisYearEps: DOUBLE,
    lastYearActualEps: DOUBLE,
    desc1: STRING(120),
    desc2: STRING(120),
    desc3: STRING(120),
    desc4: STRING(120),
    kline: STRING(240),
    eps1: DOUBLE,
    eps2: DOUBLE,
    eps3: DOUBLE,
    eps4: DOUBLE,
    macd: DOUBLE,
    businessAnalysis: TEXT,
    createdAt: DATE,
    updatedAt: DATE,
  }, {
    // 禁用软删除，因为股票基础数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  Stock.associate = () => {
    app.model.Stock.belongsTo(app.model.Sort, { foreignKey: 'firstLevelId', targetKey: 'id' });
    app.model.Stock.belongsTo(app.model.SortSec, { foreignKey: 'secondLevelId', targetKey: 'id' });
    app.model.Stock.hasOne(app.model.Source, { foreignKey: 'code', targetKey: 'code' });
    app.model.Stock.belongsTo(app.model.Source, { foreignKey: 'code', targetKey: 'code' });
    app.model.Stock.hasMany(app.model.StockTrend, { foreignKey: 'code', targetKey: 'code' });
    app.model.Stock.hasMany(app.model.OrgStock, { foreignKey: 'code', targetKey: 'code' });
    app.model.Stock.hasMany(app.model.Report, {
      foreignKey: 'secCode',
      sourceKey: 'code',
    });
    // app.model.Stock.hasMany(app.model.News, {
    //   foreignKey: 'code',
    //   sourceKey: 'code',
    // });
    app.model.Stock.hasMany(app.model.Research, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.TagStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.EventStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.NewsStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.MediaStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.FundStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.PostStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.GroupStock, {
      foreignKey: 'code',
      sourceKey: 'code',
    });
    app.model.Stock.hasMany(app.model.ReportTime, {
      foreignKey: 'code',
      targetKey: 'code',
    });
  };

  return Stock;
};
