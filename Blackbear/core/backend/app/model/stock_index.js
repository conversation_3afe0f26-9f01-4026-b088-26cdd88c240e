'use strict';

module.exports = app => {
  const { INTEGER, DATE, STRING, FLOAT, BOOLEAN, DOUBLE } = app.Sequelize;

  const StockIndex = app.model.define('stockindex', {
    id: {
      type: STRING(32),
      primaryKey: true,
    },
    code: STRING(6),
    date: STRING(10),
    tradedate: STRING(8),
    open: DOUBLE,
    close: DOUBLE,
    high: DOUBLE,
    low: DOUBLE,
    turn: DOUBLE,
    volume: DOUBLE,
    pctChg: DOUBLE,
    amount: DOUBLE,
    createdAt: DATE,
    updatedAt: DATE,
    
  }, {
    // 禁用软删除，因为股票数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  return StockIndex;
};
