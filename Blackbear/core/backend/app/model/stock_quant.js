'use strict';

module.exports = app => {
  const { INTEGER, DATE, STRING, FLOAT, BOOLEAN, DOUBLE } = app.Sequelize;

  const StockQuant = app.model.define('stockquant', {
    code: {
      type: STRING(6),
      primaryKey: true,
    },
    date: STRING(8),
    reason: STRING(32),
    rate: DOUBLE,
    createdAt: DATE,
    updatedAt: DATE,
    
  }, {
    // 禁用软删除，因为股票数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  StockQuant.associate = () => {
    app.model.StockQuant.hasOne(app.model.Source, { foreignKey: 'code', targetKey: 'code' });
  };

  return StockQuant;
};
