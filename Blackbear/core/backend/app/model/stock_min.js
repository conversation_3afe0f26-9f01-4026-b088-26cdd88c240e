'use strict';

module.exports = app => {
  const { INTEGER, DATE, STRING, FLOAT, BOOLEAN, DOUBLE } = app.Sequelize;

  const StockMin = app.model.define('stockmin', {
    id: {
      type: STRING(32),
      primaryKey: true,
    },
    code: STRING(6),
    date: STRING(16),
    tradedate: STRING(10),
    preclose: DOUBLE,
    open: DOUBLE,
    close: DOUBLE,
    high: DOUBLE,
    low: DOUBLE,
    now: DOUBLE,
    turnover: DOUBLE,
    volume: DOUBLE,
    pctchg: DOUBLE,
    spike: BOOLEAN,
    createdAt: DATE,
    updatedAt: DATE,
    
  }, {
    // 禁用软删除，因为股票数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  StockMin.associate = () => {
    app.model.StockMin.belongsTo(app.model.Source, { foreignKey: 'code', targetKey: 'code' });
  };

  return StockMin;
};
