'use strict';
module.exports = app => {
  const { Sequelize } = app;
  const {
    STRING,
    DATE,
    INTEGER,
    TEXT,
    BOOLEAN,
  } = app.Sequelize;

  const Message = app.model.define('message', {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: STRING(36),
    title: STRING(16),
    isRead: BOOLEAN,
    type: STRING(12),
    postId: INTEGER,
    tagId: INTEGER,
    mediaId: INTEGER,
    researchId: STRING(36),
    newsId: STRING(64),
    aimName: STRING(32),
    content: TEXT,
    url: STRING(255),
    comment: STRING(60),
    createdAt: DATE,
    updatedAt: DATE,
    deletedAt: DATE,
  },
  {});

  Message.addHook('afterCreate', async (message, options) => {
    const ctx = await app.createAnonymousContext();
    // 发送socket消息
    ctx.helper.sendSocketToClientOfRoom(message, 'create:message');
  });

  Message.addHook('afterUpdate', async (message, options) => {
    const ctx = await app.createAnonymousContext();

    if (message.type == 'news') {
      await ctx.model.News.update({
        mark: message.comment,
      }, {
        where: { newsID: message.newsId },
      });
    }
  });

  // Message.addHook('afterUpdate', async (message, options) => {
  //   const ctx = await app.createAnonymousContext();
  //   const { receiver_id } = message;
  //   ctx.helper.sendMessageToSocket(receiver_id, message, 'update:message');
  // });

  Message.associate = function(models) {
    // associations can be defined here
    app.model.Message.belongsTo(app.model.User, {
      foreignKey: 'userId',
      sourceKey: 'id',
    });
    app.model.Message.belongsTo(app.model.Post, {
      foreignKey: 'postId',
      sourceKey: 'id',
    });
    app.model.Message.belongsTo(app.model.Tag, {
      foreignKey: 'tagId',
      sourceKey: 'id',
    });
    app.model.Message.belongsTo(app.model.Research, {
      foreignKey: 'researchId',
      sourceKey: 'id',
    });
    app.model.Message.belongsTo(app.model.Media, {
      foreignKey: 'mediaId',
      sourceKey: 'id',
      as: 'media',
    });
    app.model.Message.belongsTo(app.model.News, {
      foreignKey: 'newsId',
      sourceKey: 'newsID',
    });
  };
  return Message;
};
