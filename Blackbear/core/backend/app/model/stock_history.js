'use strict';

module.exports = app => {
  const { INTEGER, DATE, STRING, FLOAT, BOOLEAN, DOUBLE } = app.Sequelize;

  const StockHistory = app.model.define('stockhistory', {
    id: {
      type: STRING(32),
      primaryKey: true,
    },
    code: STRING(6),
    date: STRING(10),
    tradedate: STRING(8),
    open: DOUBLE,
    close: DOUBLE,
    high: DOUBLE,
    low: DOUBLE,
    turn: DOUBLE,
    volume: DOUBLE,
    pctChg: DOUBLE,
    amount: DOUBLE,
    ret30: DOUBLE,
    max: STRING(1),
    min: STRING(1),
    rose3: STRING(1),
    fall3: STRING(1),
    pe: DOUBLE,
    pe_ttm: DOUBLE,
    pb: DOUBLE,
    ps: DOUBLE,
    ps_ttm: DOUBLE,
    dv_ratio: DOUBLE,
    dv_ttm: DOUBLE,
    total_mv: DOUBLE,
    peTTM: DOUBLE,
    psTTM: DOUBLE,
    pcfNcfTTM: DOUBLE,
    pbMRQ: DOUBLE,
    createdAt: DATE,
    updatedAt: DATE,
  }, {
    // 禁用软删除，因为历史数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  return StockHistory;
};
