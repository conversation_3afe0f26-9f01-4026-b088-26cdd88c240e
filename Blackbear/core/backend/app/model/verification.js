'use strict';
module.exports = app => {
  const { Sequelize } = app;
  const {
    STRING,
    DATE,
    BOOLEAN,
    INTEGER,
    UUID,
  } = app.Sequelize;

  const Verification = app.model.define('verification', {
    id: {
      type: UUID,
      primaryKey: true,
      allowNull: false,
    },
    code: STRING(60),
    target: STRING(60),
    type: BOOLEAN,
    available: BOOLEAN,
    expirationTime: DATE,
    createdAt: DATE,
    updatedAt: DATE,
    deletedAt: DATE,
  },
  {}
  );
  Verification.associate = function(models) {
    // associations can be defined here
  };
  return Verification;
};
