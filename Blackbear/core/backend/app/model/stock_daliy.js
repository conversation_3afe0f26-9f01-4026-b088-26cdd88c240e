'use strict';

module.exports = app => {
  const { INTEGER, DATE, STRING, FLOAT, BOOLEAN, TEXT } = app.Sequelize;

  const StockDaliy = app.model.define('stockDaliy', {
    date: {
      type: STRING(8),
      primaryKey: true,
    },
    rzye: FLOAT,
    rzmre: FLOAT,
    rzche: FLOAT,
    rqye: FLOAT,
    rqmcl: FLOAT,
    rzrqye: FLOAT,
    rqyl: FLOAT,
    shOpen: FLOAT,
    shHigh: FLOAT,
    shLow: FLOAT,
    shClose: FLOAT,
    szClose: FLOAT,
    cybOpen: FLOAT,
    cybHigh: FLOAT,
    cybLow: FLOAT,
    cybClose: FLOAT,
    shClosePct: FLOAT,
    szClosePct: FLOAT,
    cybClosePct: FLOAT,
    volume: FLOAT,
    shVolume: FLOAT,
    szVolume: FLOAT,
    cybVolume: FLOAT,
    NorthMoney: FLOAT,
    HSMoney: FLOAT,
    SSMoney: FLOAT,
    GGHSMoney: FLOAT,
    GGSSMoney: FLOAT,
    SouthSumMoney: FLOAT,
    isOpen: BOOLEAN,
    hot: TEXT,
    sentiment: FLOAT,
    sentimentDesc: TEXT,
    funds: FLOAT,
    fundsDesc: TEXT,
    shiborON: FLOAT,
    EMM00166466: FLOAT,
    EMG00001310: FLOAT,
    SMB: FLOAT,
    HML: FLOAT,
    RMW: FLOAT,
    UMD: FLOAT,
    PMO: FLOAT,
    UES: FLOAT,
    AR: FLOAT,
    limitdown: INTEGER,
    limitup: INTEGER,
    newhigh: INTEGER,
    newlow: INTEGER,
    averagechange: FLOAT,
    averageprice: FLOAT,
    changeratio: FLOAT,
    totalvalue: FLOAT,
    crowding: FLOAT,
    medianincrease: FLOAT,
    brokenpb: INTEGER,
    rose3: INTEGER,
    fall3: INTEGER,
    starcount: INTEGER,
    style: String,
    createdAt: DATE,
    updatedAt: DATE,
  }, {
    // 禁用软删除，因为股票日数据不需要软删除
    paranoid: false,
    // 禁用时间戳，如果数据库表没有这些字段
    timestamps: false,
  });

  return StockDaliy;
};
