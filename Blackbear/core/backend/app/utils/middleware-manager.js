'use strict';

/**
 * 中间件管理器
 * 统一管理和监控所有中间件的执行状态
 */
class MiddlewareManager {
  constructor(app) {
    this.app = app;
    this.middlewares = new Map();
    this.stats = new Map();
    this.config = app.config.middleware || {};
  }

  /**
   * 注册中间件
   * @param {string} name - 中间件名称
   * @param {Object} middlewareInfo - 中间件信息
   */
  registerMiddleware(name, middlewareInfo) {
    this.middlewares.set(name, {
      name,
      ...middlewareInfo,
      registeredAt: new Date(),
      status: 'registered',
    });

    // 初始化统计信息
    this.stats.set(name, {
      totalExecutions: 0,
      successExecutions: 0,
      failedExecutions: 0,
      totalDuration: 0,
      averageDuration: 0,
      lastExecutionTime: null,
      lastExecutionStatus: null,
    });
  }

  /**
   * 获取所有中间件信息
   */
  getAllMiddlewares() {
    return Array.from(this.middlewares.values());
  }

  /**
   * 获取中间件统计信息
   * @param {string} name - 中间件名称
   */
  getMiddlewareStats(name) {
    return this.stats.get(name) || null;
  }

  /**
   * 更新中间件统计
   * @param {string} name - 中间件名称
   * @param {Object} executionInfo - 执行信息
   */
  updateMiddlewareStats(name, executionInfo) {
    const stats = this.stats.get(name);
    if (!stats) { return; }

    stats.totalExecutions++;
    stats.lastExecutionTime = executionInfo.startTime;
    stats.lastExecutionStatus = executionInfo.status;
    stats.totalDuration += executionInfo.duration;
    stats.averageDuration = Math.round(stats.totalDuration / stats.totalExecutions);

    if (executionInfo.status === 'success') {
      stats.successExecutions++;
    } else {
      stats.failedExecutions++;
    }

    this.stats.set(name, stats);
  }

  /**
   * 获取中间件健康状态
   */
  getMiddlewaresHealth() {
    const middlewares = this.getAllMiddlewares();
    const health = {
      total: middlewares.length,
      healthy: 0,
      warning: 0,
      error: 0,
      details: [],
    };

    middlewares.forEach(middleware => {
      const stats = this.getMiddlewareStats(middleware.name);
      if (!stats) { return; }

      const successRate = stats.totalExecutions > 0 ?
        (stats.successExecutions / stats.totalExecutions) : 1;

      let status = 'healthy';
      if (successRate < 0.5) {
        status = 'error';
        health.error++;
      } else if (successRate < 0.8) {
        status = 'warning';
        health.warning++;
      } else {
        health.healthy++;
      }

      health.details.push({
        name: middleware.name,
        type: middleware.type || 'unknown',
        status,
        successRate: Math.round(successRate * 100),
        averageDuration: stats.averageDuration,
        totalExecutions: stats.totalExecutions,
        lastExecutionTime: stats.lastExecutionTime,
      });
    });

    return health;
  }

  /**
   * 生成中间件报告
   */
  generateMiddlewareReport() {
    const health = this.getMiddlewaresHealth();
    const now = new Date();

    return {
      generatedAt: now,
      summary: {
        totalMiddlewares: health.total,
        healthyMiddlewares: health.healthy,
        warningMiddlewares: health.warning,
        errorMiddlewares: health.error,
      },
      middlewares: health.details.map(middleware => ({
        name: middleware.name,
        type: middleware.type,
        status: middleware.status,
        successRate: `${middleware.successRate}%`,
        totalExecutions: middleware.totalExecutions,
        averageDuration: `${middleware.averageDuration}ms`,
        lastExecution: middleware.lastExecutionTime,
      })),
      recommendations: this.getMiddlewareRecommendations(health.details),
    };
  }

  /**
   * 获取中间件优化建议
   * @param middlewares
   */
  getMiddlewareRecommendations(middlewares) {
    const recommendations = [];

    middlewares.forEach(middleware => {
      if (middleware.status === 'error') {
        recommendations.push({
          type: 'error',
          middleware: middleware.name,
          message: `中间件 ${middleware.name} 成功率过低 (${middleware.successRate}%)，建议检查实现逻辑`,
        });
      }

      if (middleware.averageDuration > 100) {
        recommendations.push({
          type: 'performance',
          middleware: middleware.name,
          message: `中间件 ${middleware.name} 平均执行时间过长 (${middleware.averageDuration}ms)，建议优化性能`,
        });
      }

      if (middleware.totalExecutions === 0) {
        recommendations.push({
          type: 'warning',
          middleware: middleware.name,
          message: `中间件 ${middleware.name} 从未执行过，请检查配置`,
        });
      }
    });

    return recommendations;
  }

  /**
   * 检查中间件配置
   */
  validateMiddlewareConfig() {
    const errors = [];
    const warnings = [];

    // 检查必需的中间件
    const requiredMiddlewares = [
      'errorHandler',
      'permissionHandler',
      'operateLog',
    ];

    requiredMiddlewares.forEach(name => {
      if (!this.middlewares.has(name)) {
        errors.push(`缺少必需的中间件: ${name}`);
      }
    });

    // 检查中间件顺序
    const middlewareOrder = Array.from(this.middlewares.keys());
    if (middlewareOrder.indexOf('errorHandler') !== 0) {
      warnings.push('errorHandler 应该是第一个中间件');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 获取中间件性能指标
   */
  getPerformanceMetrics() {
    const metrics = {
      totalExecutions: 0,
      totalDuration: 0,
      averageDuration: 0,
      slowestMiddleware: null,
      fastestMiddleware: null,
      mostUsedMiddleware: null,
    };

    let slowestDuration = 0;
    let fastestDuration = Infinity;
    let mostExecutions = 0;

    for (const [ name, stats ] of this.stats.entries()) {
      metrics.totalExecutions += stats.totalExecutions;
      metrics.totalDuration += stats.totalDuration;

      if (stats.averageDuration > slowestDuration) {
        slowestDuration = stats.averageDuration;
        metrics.slowestMiddleware = { name, duration: stats.averageDuration };
      }

      if (stats.averageDuration < fastestDuration && stats.totalExecutions > 0) {
        fastestDuration = stats.averageDuration;
        metrics.fastestMiddleware = { name, duration: stats.averageDuration };
      }

      if (stats.totalExecutions > mostExecutions) {
        mostExecutions = stats.totalExecutions;
        metrics.mostUsedMiddleware = { name, executions: stats.totalExecutions };
      }
    }

    metrics.averageDuration = metrics.totalExecutions > 0 ?
      Math.round(metrics.totalDuration / metrics.totalExecutions) : 0;

    return metrics;
  }

  /**
   * 清理过期的统计数据
   * @param {number} retentionDays - 保留天数
   */
  cleanupStats(retentionDays = 7) {
    const cutoffTime = new Date();
    cutoffTime.setDate(cutoffTime.getDate() - retentionDays);

    for (const [ name, stats ] of this.stats.entries()) {
      if (stats.lastExecutionTime && stats.lastExecutionTime < cutoffTime) {
        // 重置统计数据
        this.stats.set(name, {
          totalExecutions: 0,
          successExecutions: 0,
          failedExecutions: 0,
          totalDuration: 0,
          averageDuration: 0,
          lastExecutionTime: null,
          lastExecutionStatus: null,
        });
      }
    }
  }

  /**
   * 导出中间件配置
   */
  exportMiddlewareConfig() {
    const middlewares = this.getAllMiddlewares();
    return {
      exportedAt: new Date(),
      version: '1.0',
      middlewares: middlewares.map(middleware => ({
        name: middleware.name,
        type: middleware.type || 'unknown',
        enabled: middleware.status !== 'disabled',
        config: middleware.config || {},
      })),
    };
  }

  /**
   * 监控中间件执行
   * @param middlewareName
   * @param executionFn
   */
  monitorExecution(middlewareName, executionFn) {
    return async (...args) => {
      const startTime = Date.now();
      let status = 'success';
      let error = null;

      try {
        const result = await executionFn(...args);
        return result;
      } catch (err) {
        status = 'failed';
        error = err;
        throw err;
      } finally {
        const duration = Date.now() - startTime;

        this.updateMiddlewareStats(middlewareName, {
          startTime: new Date(startTime),
          duration,
          status,
          error,
        });
      }
    };
  }
}

module.exports = MiddlewareManager;
