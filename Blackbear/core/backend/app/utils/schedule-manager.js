'use strict';

/**
 * 定时任务管理器
 * 提供定时任务的统一管理、监控、配置等功能
 */
class ScheduleManager {
  constructor(app) {
    this.app = app;
    this.tasks = new Map();
    this.stats = new Map();
    this.config = app.config.schedule || {};
  }

  /**
   * 注册定时任务
   * @param {string} name - 任务名称
   * @param {Object} taskInfo - 任务信息
   */
  registerTask(name, taskInfo) {
    this.tasks.set(name, {
      name,
      ...taskInfo,
      registeredAt: new Date(),
      status: 'registered',
    });
  }

  /**
   * 获取所有任务信息
   */
  getAllTasks() {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取任务统计信息
   * @param {string} name - 任务名称
   */
  getTaskStats(name) {
    return this.stats.get(name) || {
      totalRuns: 0,
      successRuns: 0,
      failedRuns: 0,
      lastRunTime: null,
      lastRunStatus: null,
      lastRunDuration: 0,
      averageDuration: 0,
    };
  }

  /**
   * 更新任务统计
   * @param {string} name - 任务名称
   * @param {Object} runInfo - 运行信息
   */
  updateTaskStats(name, runInfo) {
    const stats = this.getTaskStats(name);

    stats.totalRuns++;
    stats.lastRunTime = runInfo.startTime;
    stats.lastRunStatus = runInfo.status;
    stats.lastRunDuration = runInfo.duration;

    if (runInfo.status === 'success') {
      stats.successRuns++;
    } else {
      stats.failedRuns++;
    }

    // 计算平均执行时间
    stats.averageDuration = Math.round(
      (stats.averageDuration * (stats.totalRuns - 1) + runInfo.duration) / stats.totalRuns
    );

    this.stats.set(name, stats);
  }

  /**
   * 检查任务是否应该执行
   * @param {string} name - 任务名称
   * @param {Object} ctx - 上下文
   */
  async shouldTaskRun(name, ctx) {
    try {
      // 检查全局配置
      if (this.config.disabled === true) {
        return false;
      }

      // 检查特定任务配置
      const taskConfig = await this.getTaskConfig(name, ctx);
      if (taskConfig && taskConfig.disabled === true) {
        return false;
      }

      // 检查环境配置
      const { env } = this.app.config;
      if (taskConfig && taskConfig.disabledEnvs && taskConfig.disabledEnvs.includes(env)) {
        return false;
      }

      return true;

    } catch (error) {
      ctx.logger.error(`检查任务 ${name} 执行条件失败:`, error);
      return true; // 默认允许执行
    }
  }

  /**
   * 获取任务配置
   * @param {string} name - 任务名称
   * @param {Object} ctx - 上下文
   */
  async getTaskConfig(name, ctx) {
    try {
      if (ctx.service.configurations) {
        const config = await ctx.service.configurations.getConfig('schedule');
        return config && config[name] ? config[name] : null;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 记录任务执行日志
   * @param {string} name - 任务名称
   * @param {Object} logInfo - 日志信息
   * @param {Object} ctx - 上下文
   */
  async logTaskExecution(name, logInfo, ctx) {
    try {
      const logData = {
        taskName: name,
        timestamp: new Date(),
        ...logInfo,
      };

      // 记录到应用日志
      if (logInfo.status === 'success') {
        ctx.logger.info(`任务执行成功 [${name}]:`, logData);
      } else {
        ctx.logger.error(`任务执行失败 [${name}]:`, logData);
      }

      // 可以扩展为存储到数据库
      // await this.saveTaskLog(logData, ctx);

    } catch (error) {
      ctx.logger.error(`记录任务日志失败 [${name}]:`, error);
    }
  }

  /**
   * 获取任务健康状态
   */
  getTasksHealth() {
    const tasks = this.getAllTasks();
    const health = {
      total: tasks.length,
      healthy: 0,
      warning: 0,
      error: 0,
      details: [],
    };

    tasks.forEach(task => {
      const stats = this.getTaskStats(task.name);
      const successRate = stats.totalRuns > 0 ? (stats.successRuns / stats.totalRuns) : 1;

      let status = 'healthy';
      if (successRate < 0.5) {
        status = 'error';
        health.error++;
      } else if (successRate < 0.8) {
        status = 'warning';
        health.warning++;
      } else {
        health.healthy++;
      }

      health.details.push({
        name: task.name,
        status,
        successRate: Math.round(successRate * 100),
        ...stats,
      });
    });

    return health;
  }

  /**
   * 生成任务报告
   */
  generateTaskReport() {
    const health = this.getTasksHealth();
    const now = new Date();

    return {
      generatedAt: now,
      summary: {
        totalTasks: health.total,
        healthyTasks: health.healthy,
        warningTasks: health.warning,
        errorTasks: health.error,
      },
      tasks: health.details.map(task => ({
        name: task.name,
        status: task.status,
        successRate: `${task.successRate}%`,
        totalRuns: task.totalRuns,
        lastRun: task.lastRunTime,
        averageDuration: `${task.averageDuration}ms`,
      })),
    };
  }

  /**
   * 清理过期的统计数据
   * @param {number} retentionDays - 保留天数
   */
  cleanupStats(retentionDays = 30) {
    const cutoffTime = new Date();
    cutoffTime.setDate(cutoffTime.getDate() - retentionDays);

    for (const [ name, stats ] of this.stats.entries()) {
      if (stats.lastRunTime && stats.lastRunTime < cutoffTime) {
        // 重置统计数据，但保留基本信息
        this.stats.set(name, {
          totalRuns: 0,
          successRuns: 0,
          failedRuns: 0,
          lastRunTime: null,
          lastRunStatus: null,
          lastRunDuration: 0,
          averageDuration: 0,
        });
      }
    }
  }

  /**
   * 导出任务配置
   */
  exportTaskConfig() {
    const tasks = this.getAllTasks();
    return {
      exportedAt: new Date(),
      version: '1.0',
      tasks: tasks.map(task => ({
        name: task.name,
        schedule: task.schedule,
        description: task.description || '',
        enabled: task.status !== 'disabled',
      })),
    };
  }

  /**
   * 获取任务执行建议
   */
  getTaskRecommendations() {
    const health = this.getTasksHealth();
    const recommendations = [];

    health.details.forEach(task => {
      if (task.status === 'error') {
        recommendations.push({
          type: 'error',
          task: task.name,
          message: `任务 ${task.name} 成功率过低 (${task.successRate}%)，建议检查任务逻辑`,
        });
      }

      if (task.averageDuration > 300000) { // 5分钟
        recommendations.push({
          type: 'performance',
          task: task.name,
          message: `任务 ${task.name} 平均执行时间过长 (${task.averageDuration}ms)，建议优化性能`,
        });
      }

      if (task.totalRuns === 0) {
        recommendations.push({
          type: 'warning',
          task: task.name,
          message: `任务 ${task.name} 从未执行过，请检查任务配置`,
        });
      }
    });

    return recommendations;
  }
}

module.exports = ScheduleManager;
