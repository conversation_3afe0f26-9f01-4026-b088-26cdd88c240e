'use strict';

const EventEmitter = require('events');

/**
 * 资源管理器
 * 提供内存监控、资源池管理、内存泄漏检测等功能
 */
class ResourceManager extends EventEmitter {
  constructor(options = {}) {
    super();

    this.config = {
      // 内存监控配置
      memory: {
        enabled: true,
        checkInterval: 30000, // 30秒检查一次
        warningThreshold: 0.8, // 80%内存使用率警告
        criticalThreshold: 0.9, // 90%内存使用率严重警告
        maxHeapSize: 1024 * 1024 * 1024, // 1GB
        gcThreshold: 0.85, // 85%时触发GC
      },
      // 资源池配置
      pools: {
        enabled: true,
        maxSize: 100,
        minSize: 10,
        acquireTimeout: 30000, // 30秒获取超时
        idleTimeout: 300000, // 5分钟空闲超时
      },
      // 内存泄漏检测配置
      leakDetection: {
        enabled: true,
        checkInterval: 60000, // 1分钟检查一次
        maxListeners: 100, // 最大监听器数量
        maxObjects: 10000, // 最大对象数量
        heapGrowthThreshold: 50 * 1024 * 1024, // 50MB增长阈值
      },
      // 清理配置
      cleanup: {
        enabled: true,
        interval: 300000, // 5分钟清理一次
        maxAge: 3600000, // 1小时最大存活时间
      },
      ...options,
    };

    this.stats = {
      memory: {
        peak: 0,
        current: 0,
        gcCount: 0,
        lastGC: null,
      },
      pools: new Map(),
      leaks: [],
      cleanup: {
        lastRun: null,
        objectsCleared: 0,
      },
    };

    this.resourcePools = new Map();
    this.objectRegistry = new Map();
    this.memoryHistory = [];

    this.init();
  }

  /**
   * 初始化资源管理器
   */
  init() {
    if (this.config.memory.enabled) {
      this.startMemoryMonitoring();
    }

    if (this.config.leakDetection.enabled) {
      this.startLeakDetection();
    }

    if (this.config.cleanup.enabled) {
      this.startPeriodicCleanup();
    }

    // 监听进程事件
    this.setupProcessListeners();
  }

  /**
   * 启动内存监控
   */
  startMemoryMonitoring() {
    this.memoryTimer = setInterval(() => {
      this.checkMemoryUsage();
    }, this.config.memory.checkInterval);

    // 监听GC事件
    if (global.gc) {
      const originalGC = global.gc;
      global.gc = () => {
        this.stats.memory.gcCount++;
        this.stats.memory.lastGC = new Date();
        this.emit('gc-triggered', this.stats.memory);
        return originalGC();
      };
    }
  }

  /**
   * 检查内存使用情况
   */
  checkMemoryUsage() {
    const memoryUsage = process.memoryUsage();
    const { heapUsed } = memoryUsage;
    const { heapTotal } = memoryUsage;
    const heapUsageRatio = heapUsed / heapTotal;

    // 更新统计信息
    this.stats.memory.current = heapUsed;
    if (heapUsed > this.stats.memory.peak) {
      this.stats.memory.peak = heapUsed;
    }

    // 记录内存历史
    this.memoryHistory.push({
      timestamp: new Date(),
      heapUsed,
      heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss,
    });

    // 保持历史记录在合理范围内
    if (this.memoryHistory.length > 100) {
      this.memoryHistory.shift();
    }

    // 检查内存阈值
    if (heapUsageRatio > this.config.memory.criticalThreshold) {
      this.emit('memory-critical', {
        usage: heapUsageRatio,
        heapUsed,
        heapTotal,
      });
      this.handleCriticalMemory();
    } else if (heapUsageRatio > this.config.memory.warningThreshold) {
      this.emit('memory-warning', {
        usage: heapUsageRatio,
        heapUsed,
        heapTotal,
      });
    }

    // 自动触发GC
    if (heapUsageRatio > this.config.memory.gcThreshold && global.gc) {
      global.gc();
    }
  }

  /**
   * 处理严重内存问题
   */
  handleCriticalMemory() {
    console.warn('Critical memory usage detected, attempting cleanup...');

    // 强制清理资源池
    this.cleanupAllPools();

    // 清理对象注册表
    this.cleanupObjectRegistry();

    // 强制GC
    if (global.gc) {
      global.gc();
    }

    // 发送告警
    this.emit('alert', {
      type: 'critical-memory',
      data: this.getMemoryStats(),
    });
  }

  /**
   * 启动内存泄漏检测
   */
  startLeakDetection() {
    this.leakTimer = setInterval(() => {
      this.detectMemoryLeaks();
    }, this.config.leakDetection.checkInterval);
  }

  /**
   * 检测内存泄漏
   */
  detectMemoryLeaks() {
    const currentMemory = process.memoryUsage().heapUsed;
    const lastMemory = this.memoryHistory.length > 0 ?
      this.memoryHistory[this.memoryHistory.length - 1].heapUsed : 0;

    const memoryGrowth = currentMemory - lastMemory;

    // 检查内存增长
    if (memoryGrowth > this.config.leakDetection.heapGrowthThreshold) {
      const leak = {
        timestamp: new Date(),
        growth: memoryGrowth,
        currentMemory,
        lastMemory,
        type: 'heap-growth',
      };

      this.stats.leaks.push(leak);
      this.emit('memory-leak-detected', leak);
    }

    // 检查事件监听器泄漏
    this.checkEventListenerLeaks();

    // 检查对象数量
    this.checkObjectCount();
  }

  /**
   * 检查事件监听器泄漏
   */
  checkEventListenerLeaks() {
    const emitters = [ process, this ];

    for (const emitter of emitters) {
      if (emitter.listenerCount && emitter.eventNames) {
        const eventNames = emitter.eventNames();
        for (const eventName of eventNames) {
          const listenerCount = emitter.listenerCount(eventName);
          if (listenerCount > this.config.leakDetection.maxListeners) {
            const leak = {
              timestamp: new Date(),
              type: 'event-listeners',
              eventName,
              listenerCount,
              emitter: emitter.constructor.name,
            };

            this.stats.leaks.push(leak);
            this.emit('memory-leak-detected', leak);
          }
        }
      }
    }
  }

  /**
   * 检查对象数量
   */
  checkObjectCount() {
    const objectCount = this.objectRegistry.size;

    if (objectCount > this.config.leakDetection.maxObjects) {
      const leak = {
        timestamp: new Date(),
        type: 'object-count',
        objectCount,
        threshold: this.config.leakDetection.maxObjects,
      };

      this.stats.leaks.push(leak);
      this.emit('memory-leak-detected', leak);
    }
  }

  /**
   * 创建资源池
   * @param {string} name - 池名称
   * @param {Function} factory - 资源工厂函数
   * @param {Function} destroyer - 资源销毁函数
   * @param {Object} options - 选项
   * @return {ResourcePool} 资源池
   */
  createPool(name, factory, destroyer, options = {}) {
    const poolConfig = { ...this.config.pools, ...options };
    const pool = new ResourcePool(name, factory, destroyer, poolConfig);

    this.resourcePools.set(name, pool);
    this.stats.pools.set(name, {
      created: 0,
      acquired: 0,
      released: 0,
      destroyed: 0,
    });

    return pool;
  }

  /**
   * 获取资源池
   * @param {string} name - 池名称
   * @return {ResourcePool} 资源池
   */
  getPool(name) {
    return this.resourcePools.get(name);
  }

  /**
   * 注册对象
   * @param {string} id - 对象ID
   * @param {Object} object - 对象
   * @param {Object} metadata - 元数据
   */
  registerObject(id, object, metadata = {}) {
    this.objectRegistry.set(id, {
      object,
      metadata,
      createdAt: new Date(),
      lastAccessed: new Date(),
    });
  }

  /**
   * 注销对象
   * @param {string} id - 对象ID
   */
  unregisterObject(id) {
    return this.objectRegistry.delete(id);
  }

  /**
   * 启动定期清理
   */
  startPeriodicCleanup() {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanup.interval);
  }

  /**
   * 执行清理
   */
  performCleanup() {
    const now = new Date();
    let objectsCleared = 0;

    // 清理过期对象
    for (const [ id, entry ] of this.objectRegistry.entries()) {
      const age = now - entry.createdAt;
      if (age > this.config.cleanup.maxAge) {
        this.objectRegistry.delete(id);
        objectsCleared++;
      }
    }

    // 清理内存历史
    if (this.memoryHistory.length > 50) {
      this.memoryHistory.splice(0, this.memoryHistory.length - 50);
    }

    // 清理泄漏记录
    if (this.stats.leaks.length > 100) {
      this.stats.leaks.splice(0, this.stats.leaks.length - 100);
    }

    // 更新清理统计
    this.stats.cleanup.lastRun = now;
    this.stats.cleanup.objectsCleared += objectsCleared;

    this.emit('cleanup-completed', {
      objectsCleared,
      timestamp: now,
    });
  }

  /**
   * 清理所有资源池
   */
  cleanupAllPools() {
    for (const pool of this.resourcePools.values()) {
      pool.drain();
    }
  }

  /**
   * 清理对象注册表
   */
  cleanupObjectRegistry() {
    const before = this.objectRegistry.size;
    this.objectRegistry.clear();
    const cleared = before;

    this.emit('object-registry-cleared', { cleared });
  }

  /**
   * 设置进程监听器
   */
  setupProcessListeners() {
    // 优雅关闭
    process.on('SIGTERM', () => this.shutdown());
    process.on('SIGINT', () => this.shutdown());

    // 未捕获异常
    process.on('uncaughtException', error => {
      this.emit('uncaught-exception', error);
    });

    // 未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      this.emit('unhandled-rejection', { reason, promise });
    });
  }

  /**
   * 获取内存统计
   * @return {Object} 内存统计信息
   */
  getMemoryStats() {
    const memoryUsage = process.memoryUsage();

    return {
      current: memoryUsage,
      peak: this.stats.memory.peak,
      gcCount: this.stats.memory.gcCount,
      lastGC: this.stats.memory.lastGC,
      history: this.memoryHistory.slice(-10), // 最近10次记录
      leaks: this.stats.leaks.slice(-5), // 最近5次泄漏
    };
  }

  /**
   * 获取资源池统计
   * @return {Object} 资源池统计信息
   */
  getPoolStats() {
    const poolStats = {};

    for (const [ name, pool ] of this.resourcePools.entries()) {
      poolStats[name] = {
        size: pool.size,
        available: pool.available,
        pending: pool.pending,
        stats: this.stats.pools.get(name),
      };
    }

    return poolStats;
  }

  /**
   * 获取完整统计信息
   * @return {Object} 统计信息
   */
  getStats() {
    return {
      memory: this.getMemoryStats(),
      pools: this.getPoolStats(),
      objects: {
        registered: this.objectRegistry.size,
        cleanup: this.stats.cleanup,
      },
      uptime: process.uptime(),
      timestamp: new Date(),
    };
  }

  /**
   * 关闭资源管理器
   */
  shutdown() {
    console.log('Shutting down resource manager...');

    // 清理定时器
    if (this.memoryTimer) { clearInterval(this.memoryTimer); }
    if (this.leakTimer) { clearInterval(this.leakTimer); }
    if (this.cleanupTimer) { clearInterval(this.cleanupTimer); }

    // 清理资源池
    this.cleanupAllPools();

    // 最后清理
    this.performCleanup();

    this.emit('shutdown');
  }
}

/**
 * 资源池类
 */
class ResourcePool {
  constructor(name, factory, destroyer, config) {
    this.name = name;
    this.factory = factory;
    this.destroyer = destroyer;
    this.config = config;

    this.resources = [];
    this.pending = [];
    this.size = 0;
    this.available = 0;
  }

  /**
   * 获取资源
   * @return {Promise} 资源
   */
  async acquire() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Resource acquisition timeout for pool: ${this.name}`));
      }, this.config.acquireTimeout);

      if (this.available > 0) {
        clearTimeout(timeout);
        const resource = this.resources.pop();
        this.available--;
        resolve(resource);
      } else if (this.size < this.config.maxSize) {
        this.factory().then(resource => {
          clearTimeout(timeout);
          this.size++;
          resolve(resource);
        }).catch(reject);
      } else {
        this.pending.push({ resolve, reject, timeout });
      }
    });
  }

  /**
   * 释放资源
   * @param {*} resource - 资源
   */
  release(resource) {
    if (this.pending.length > 0) {
      const { resolve, timeout } = this.pending.shift();
      clearTimeout(timeout);
      resolve(resource);
    } else {
      this.resources.push(resource);
      this.available++;
    }
  }

  /**
   * 销毁资源
   * @param {*} resource - 资源
   */
  destroy(resource) {
    if (this.destroyer) {
      this.destroyer(resource);
    }
    this.size--;
  }

  /**
   * 清空池
   */
  drain() {
    this.resources.forEach(resource => this.destroy(resource));
    this.resources = [];
    this.available = 0;
    this.size = 0;
  }
}

module.exports = ResourceManager;
