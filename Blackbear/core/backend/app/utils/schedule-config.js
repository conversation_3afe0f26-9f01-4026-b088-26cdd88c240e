'use strict';

/**
 * 定时任务配置管理工具
 * 提供定时任务的配置管理、验证、导入导出等功能
 */
class ScheduleConfig {
  constructor(app) {
    this.app = app;
    this.defaultConfig = this.getDefaultConfig();
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      // 全局配置
      global: {
        disabled: false,
        maxConcurrent: 10,
        defaultTimeout: 300000, // 5分钟
        retryDelay: 1000,
        maxRetries: 3,
      },

      // 任务特定配置
      tasks: {
        // 聚合数据创建
        scheduleAggregation: {
          disabled: false,
          batchSize: 50,
          notifyOnError: false,
        },

        // 百度SEO推送
        scheduleBaiduSeo: {
          disabled: false,
          notifyOnError: true,
          maxRetries: 3,
        },

        // Bing索引推送
        scheduleBingIndex: {
          disabled: false,
          notifyOnError: true,
          maxUrls: 10,
        },

        // 标签锁定清理
        scheduleClearTagLocker: {
          disabled: false,
          notifyOnError: false,
          cleanupThreshold: 1000,
        },

        // 量化数据同步
        scheduleQuant: {
          disabled: false,
          notifyOnError: true,
          platforms: [ 'xueqiu', 'weibo' ],
        },

        // 新闻更新
        scheduleNewsUpdate: {
          disabled: false,
          batchSize: 100,
          notifyOnError: true,
        },

        // 日历消息同步
        scheduleCalendar: {
          disabled: false,
          notifyOnError: false,
        },

        // 收盘统计
        scheduleCloseStatistics: {
          disabled: false,
          notifyOnError: true,
        },

        // 验证码清理
        scheduleCleanVerification: {
          disabled: false,
          retentionHours: 24,
          notifyOnError: false,
        },

        // 用户积分同步
        scheduleSyncUserPoint: {
          disabled: false,
          batchSize: 50,
          notifyOnError: true,
          inconsistencyThreshold: 100,
        },

        // 权限缓存更新
        scheduleUpdatePermissionsCache: {
          disabled: false,
          notifyOnError: true,
          validateIntegrity: true,
        },

        // 微博发布
        scheduleWB: {
          disabled: false,
          notifyOnError: true,
          contentMaxLength: 130,
        },
      },

      // 环境特定配置
      environments: {
        local: {
          disabledTasks: [
            'scheduleBaiduSeo',
            'scheduleBingIndex',
            'scheduleQuant',
            'scheduleWB',
          ],
        },
        development: {
          disabledTasks: [
            'scheduleBaiduSeo',
            'scheduleBingIndex',
          ],
        },
        production: {
          disabledTasks: [],
        },
      },

      // 通知配置
      notifications: {
        enabled: true,
        channels: [ 'system', 'email' ],
        errorThreshold: 3, // 连续失败3次后发送通知
        successNotification: false,
      },

      // 监控配置
      monitoring: {
        enabled: true,
        metricsRetention: 30, // 天
        performanceThreshold: {
          duration: 300000, // 5分钟
          memoryUsage: 100 * 1024 * 1024, // 100MB
        },
      },
    };
  }

  /**
   * 获取任务配置
   * @param {string} taskName - 任务名称
   * @param {Object} ctx - 上下文
   */
  async getTaskConfig(taskName, ctx) {
    try {
      // 从数据库获取配置
      const dbConfig = await this.getConfigFromDB(ctx);

      // 合并默认配置和数据库配置
      const mergedConfig = this.mergeConfig(this.defaultConfig, dbConfig);

      // 应用环境特定配置
      const envConfig = this.applyEnvironmentConfig(mergedConfig, ctx.app.config.env);

      // 返回特定任务配置
      return {
        ...envConfig.global,
        ...envConfig.tasks[taskName],
        notifications: envConfig.notifications,
        monitoring: envConfig.monitoring,
      };

    } catch (error) {
      ctx.logger.error(`获取任务 ${taskName} 配置失败:`, error);
      return this.defaultConfig.tasks[taskName] || {};
    }
  }

  /**
   * 从数据库获取配置
   * @param ctx
   */
  async getConfigFromDB(ctx) {
    try {
      if (ctx.service.configurations) {
        return await ctx.service.configurations.getConfig('schedule') || {};
      }
      return {};
    } catch (error) {
      return {};
    }
  }

  /**
   * 合并配置
   * @param defaultConfig
   * @param userConfig
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = JSON.parse(JSON.stringify(defaultConfig));

    // 深度合并配置
    this.deepMerge(merged, userConfig);

    return merged;
  }

  /**
   * 深度合并对象
   * @param target
   * @param source
   */
  deepMerge(target, source) {
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (!target[key]) { target[key] = {}; }
        this.deepMerge(target[key], source[key]);
      } else {
        target[key] = source[key];
      }
    }
  }

  /**
   * 应用环境特定配置
   * @param config
   * @param env
   */
  applyEnvironmentConfig(config, env) {
    const envConfig = config.environments[env];
    if (!envConfig) { return config; }

    // 应用环境禁用的任务
    if (envConfig.disabledTasks) {
      envConfig.disabledTasks.forEach(taskName => {
        if (config.tasks[taskName]) {
          config.tasks[taskName].disabled = true;
        }
      });
    }

    return config;
  }

  /**
   * 验证配置
   * @param config
   */
  validateConfig(config) {
    const errors = [];

    // 验证全局配置
    if (config.global) {
      if (typeof config.global.maxConcurrent !== 'number' || config.global.maxConcurrent < 1) {
        errors.push('global.maxConcurrent 必须是大于0的数字');
      }
      if (typeof config.global.defaultTimeout !== 'number' || config.global.defaultTimeout < 1000) {
        errors.push('global.defaultTimeout 必须是大于1000的数字');
      }
    }

    // 验证任务配置
    if (config.tasks) {
      Object.keys(config.tasks).forEach(taskName => {
        const taskConfig = config.tasks[taskName];
        if (typeof taskConfig.disabled !== 'boolean') {
          errors.push(`tasks.${taskName}.disabled 必须是布尔值`);
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 保存配置到数据库
   * @param config
   * @param ctx
   */
  async saveConfig(config, ctx) {
    try {
      // 验证配置
      const validation = this.validateConfig(config);
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存到数据库
      if (ctx.service.configurations) {
        await ctx.service.configurations.updateConfig('schedule', config);
      }

      ctx.logger.info('定时任务配置保存成功');
      return true;

    } catch (error) {
      ctx.logger.error('保存定时任务配置失败:', error);
      throw error;
    }
  }

  /**
   * 导出配置
   * @param ctx
   */
  async exportConfig(ctx) {
    try {
      const config = await this.getConfigFromDB(ctx);

      return {
        exportTime: new Date(),
        version: '1.0',
        config: this.mergeConfig(this.defaultConfig, config),
      };

    } catch (error) {
      ctx.logger.error('导出配置失败:', error);
      throw error;
    }
  }

  /**
   * 导入配置
   * @param configData
   * @param ctx
   */
  async importConfig(configData, ctx) {
    try {
      if (!configData.config) {
        throw new Error('无效的配置数据格式');
      }

      // 验证配置
      const validation = this.validateConfig(configData.config);
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存配置
      await this.saveConfig(configData.config, ctx);

      ctx.logger.info('定时任务配置导入成功');
      return true;

    } catch (error) {
      ctx.logger.error('导入配置失败:', error);
      throw error;
    }
  }

  /**
   * 重置为默认配置
   * @param ctx
   */
  async resetToDefault(ctx) {
    try {
      await this.saveConfig(this.defaultConfig, ctx);
      ctx.logger.info('定时任务配置已重置为默认值');
      return true;

    } catch (error) {
      ctx.logger.error('重置配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取配置模板
   */
  getConfigTemplate() {
    return {
      description: '定时任务配置模板',
      structure: {
        global: '全局配置',
        tasks: '任务特定配置',
        environments: '环境特定配置',
        notifications: '通知配置',
        monitoring: '监控配置',
      },
      example: this.defaultConfig,
    };
  }
}

module.exports = ScheduleConfig;
