'use strict';

/**
 * 统一响应系统
 * 整合所有Response Codes定义，提供统一的响应处理方案
 *
 * 本文件整合了以下文件中的响应代码定义：
 * - backup/backend/app/extend/helper.js
 * - Blackbear/core/backend/app/utils/response-formatter.js
 * - Blackbear/core/backend/app/utils/custom-errors.js
 * - Blackbear/core/backend/app/extend/helper.js
 */

const ResponseFormatter = require('./response-formatter');
const CustomErrors = require('./custom-errors');

/**
 * 统一响应系统类
 */
class UnifiedResponseSystem {
  constructor() {
    this.formatter = ResponseFormatter;
    this.errors = CustomErrors;
  }

  /**
   * 获取所有响应代码
   */
  get codes() {
    return this.formatter.ResponseCodes;
  }

  /**
   * 获取所有响应消息
   */
  get messages() {
    return this.formatter.ResponseMessages;
  }

  /**
   * 创建成功响应
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 响应代码
   * @return {Object} 格式化后的成功响应
   */
  success(data = null, message = '', code = 200) {
    return this.formatter.formatSuccessResponse(data, message, code);
  }

  /**
   * 创建错误响应
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   * @param {*} data - 错误数据
   * @return {Object} 格式化后的错误响应
   */
  error(message = '', code = 500, data = null) {
    return this.formatter.formatErrorResponse(message, code, data);
  }

  /**
   * 创建分页响应
   * @param {Array} rows - 数据列表
   * @param {number} total - 总数量
   * @param {number} pageIndex - 当前页码
   * @param {number} pageSize - 每页数量
   * @param {string} message - 响应消息
   * @return {Object} 格式化后的分页响应
   */
  pagination(rows = [], total = 0, pageIndex = 1, pageSize = 10, message = '查询成功') {
    return this.formatter.formatPaginationResponse(rows, total, pageIndex, pageSize, message);
  }

  /**
   * 创建列表响应
   * @param {Array} list - 数据列表
   * @param {string} message - 响应消息
   * @return {Object} 格式化后的列表响应
   */
  list(list = [], message = '查询成功') {
    return this.formatter.formatListResponse(list, message);
  }

  /**
   * 创建详情响应
   * @param {*} item - 详情数据
   * @param {string} message - 响应消息
   * @return {Object} 格式化后的详情响应
   */
  detail(item = null, message = '查询成功') {
    return this.formatter.formatDetailResponse(item, message);
  }

  /**
   * 创建操作结果响应
   * @param {boolean} success - 操作是否成功
   * @param {string} message - 响应消息
   * @param {*} data - 附加数据
   * @return {Object} 格式化后的操作结果响应
   */
  operation(success = true, message = '操作成功', data = null) {
    return this.formatter.formatOperationResponse(success, message, data);
  }

  /**
   * 设置Koa上下文响应
   * @param {Object} ctx - Koa上下文
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 响应代码
   */
  setResponse(ctx, data = null, message = '', code = 200) {
    return this.formatter.createResponse(data, code, message);
  }

  /**
   * 抛出自定义错误
   * @param {string} type - 错误类型
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   * @throws {Error} 自定义错误
   */
  throwError(type, message, code) {
    switch (type) {
      case 'validation':
        throw this.errors.ErrorFactory.validation(message);
      case 'unauthorized':
        throw this.errors.ErrorFactory.unauthorized(message);
      case 'forbidden':
        throw this.errors.ErrorFactory.forbidden(message);
      case 'notFound':
        throw this.errors.ErrorFactory.notFound(message);
      case 'business':
        throw this.errors.ErrorFactory.business(message, code);
      case 'badRequest':
        throw this.errors.ErrorFactory.badRequest(message);
      case 'internal':
        throw this.errors.ErrorFactory.internal(message);
      case 'timeout':
        throw this.errors.ErrorFactory.timeout(message);
      case 'conflict':
        throw this.errors.ErrorFactory.conflict(message);
      case 'rateLimit':
        throw this.errors.ErrorFactory.rateLimit(message);
      default:
        throw this.errors.ErrorFactory.fromStatus(code || 500, message);
    }
  }

  /**
   * 兼容方法：wrapJson (兼容原有helper.js)
   * @param data
   * @param status
   * @param code
   * @param message
   */
  wrapJson(data, status = 200, code = 200, message) {
    return this.formatter.wrapJson(data, status, code, message);
  }

  /**
   * 兼容方法：body方法集合 (兼容原有helper.js)
   */
  get body() {
    return this.formatter.body;
  }

  /**
   * 兼容方法：errorCode映射 (兼容原有helper.js)
   */
  get errorCode() {
    return this.formatter.ErrorCodeMap;
  }
}

/**
 * 创建统一响应系统实例
 */
const unifiedResponse = new UnifiedResponseSystem();

/**
 * 快捷方法导出
 */
module.exports = {
  // 主要实例
  UnifiedResponseSystem,
  unifiedResponse,

  // 快捷方法
  success: unifiedResponse.success.bind(unifiedResponse),
  error: unifiedResponse.error.bind(unifiedResponse),
  pagination: unifiedResponse.pagination.bind(unifiedResponse),
  list: unifiedResponse.list.bind(unifiedResponse),
  detail: unifiedResponse.detail.bind(unifiedResponse),
  operation: unifiedResponse.operation.bind(unifiedResponse),
  setResponse: unifiedResponse.setResponse.bind(unifiedResponse),
  throwError: unifiedResponse.throwError.bind(unifiedResponse),

  // 兼容方法
  wrapJson: unifiedResponse.wrapJson.bind(unifiedResponse),
  body: unifiedResponse.body,
  errorCode: unifiedResponse.errorCode,

  // 响应代码和消息
  codes: unifiedResponse.codes,
  messages: unifiedResponse.messages,

  // 原始模块引用
  ResponseFormatter,
  CustomErrors,
};
