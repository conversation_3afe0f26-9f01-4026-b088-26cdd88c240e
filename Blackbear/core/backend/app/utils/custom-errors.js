'use strict';

/**
 * 自定义错误类型定义
 * 提供统一的错误类型，用于应用程序的错误处理
 */

/**
 * 验证错误
 */
class ValidationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ValidationError';
    this.status = 422;
  }
}

/**
 * 未授权错误
 */
class UnauthorizedError extends Error {
  constructor(message = '未授权访问') {
    super(message);
    this.name = 'UnauthorizedError';
    this.status = 401;
  }
}

/**
 * 权限不足错误
 */
class ForbiddenError extends Error {
  constructor(message = '权限不足') {
    super(message);
    this.name = 'ForbiddenError';
    this.status = 403;
  }
}

/**
 * 资源不存在错误
 */
class NotFoundError extends Error {
  constructor(message = '资源不存在') {
    super(message);
    this.name = 'NotFoundError';
    this.status = 404;
  }
}

/**
 * 业务逻辑错误
 */
class BusinessError extends Error {
  constructor(message, code = 400) {
    super(message);
    this.name = 'BusinessError';
    this.code = code;
    this.status = code;
  }
}

/**
 * 请求参数错误
 */
class BadRequestError extends Error {
  constructor(message = '请求参数错误') {
    super(message);
    this.name = 'BadRequestError';
    this.status = 400;
  }
}

/**
 * 服务器内部错误
 */
class InternalServerError extends Error {
  constructor(message = '服务器内部错误') {
    super(message);
    this.name = 'InternalServerError';
    this.status = 500;
  }
}

/**
 * 服务不可用错误
 */
class ServiceUnavailableError extends Error {
  constructor(message = '服务暂时不可用') {
    super(message);
    this.name = 'ServiceUnavailableError';
    this.status = 503;
  }
}

/**
 * 请求超时错误
 */
class TimeoutError extends Error {
  constructor(message = '请求超时') {
    super(message);
    this.name = 'TimeoutError';
    this.status = 408;
  }
}

/**
 * 冲突错误
 */
class ConflictError extends Error {
  constructor(message = '资源冲突') {
    super(message);
    this.name = 'ConflictError';
    this.status = 409;
  }
}

/**
 * 限流错误
 */
class RateLimitError extends Error {
  constructor(message = '请求过于频繁') {
    super(message);
    this.name = 'RateLimitError';
    this.status = 429;
  }
}

/**
 * 创建错误实例的工厂函数
 */
class ErrorFactory {
  /**
   * 创建验证错误
   * @param message
   */
  static validation(message) {
    return new ValidationError(message);
  }

  /**
   * 创建未授权错误
   * @param message
   */
  static unauthorized(message) {
    return new UnauthorizedError(message);
  }

  /**
   * 创建权限不足错误
   * @param message
   */
  static forbidden(message) {
    return new ForbiddenError(message);
  }

  /**
   * 创建资源不存在错误
   * @param message
   */
  static notFound(message) {
    return new NotFoundError(message);
  }

  /**
   * 创建业务逻辑错误
   * @param message
   * @param code
   */
  static business(message, code) {
    return new BusinessError(message, code);
  }

  /**
   * 创建请求参数错误
   * @param message
   */
  static badRequest(message) {
    return new BadRequestError(message);
  }

  /**
   * 创建服务器内部错误
   * @param message
   */
  static internal(message) {
    return new InternalServerError(message);
  }

  /**
   * 创建服务不可用错误
   * @param message
   */
  static serviceUnavailable(message) {
    return new ServiceUnavailableError(message);
  }

  /**
   * 创建请求超时错误
   * @param message
   */
  static timeout(message) {
    return new TimeoutError(message);
  }

  /**
   * 创建冲突错误
   * @param message
   */
  static conflict(message) {
    return new ConflictError(message);
  }

  /**
   * 创建限流错误
   * @param message
   */
  static rateLimit(message) {
    return new RateLimitError(message);
  }

  /**
   * 根据状态码创建错误
   * @param status
   * @param message
   */
  static fromStatus(status, message) {
    switch (status) {
      case 400:
        return new BadRequestError(message);
      case 401:
        return new UnauthorizedError(message);
      case 403:
        return new ForbiddenError(message);
      case 404:
        return new NotFoundError(message);
      case 408:
        return new TimeoutError(message);
      case 409:
        return new ConflictError(message);
      case 422:
        return new ValidationError(message);
      case 429:
        return new RateLimitError(message);
      case 500:
        return new InternalServerError(message);
      case 503:
        return new ServiceUnavailableError(message);
      default:
        return new Error(message);
    }
  }
}

module.exports = {
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  BusinessError,
  BadRequestError,
  InternalServerError,
  ServiceUnavailableError,
  TimeoutError,
  ConflictError,
  RateLimitError,
  ErrorFactory,
};
