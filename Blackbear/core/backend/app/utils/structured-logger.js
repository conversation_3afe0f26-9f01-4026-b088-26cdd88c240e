'use strict';

const SecurityValidator = require('./security-validator');

/**
 * 结构化日志工具类
 * 提供统一的日志格式、敏感信息脱敏、性能日志等功能
 */
class StructuredLogger {
  constructor(ctx) {
    this.ctx = ctx;
    this.baseLogger = ctx.logger;
    this.startTime = Date.now();
    this.logBuffer = [];
    this.config = {
      // 敏感字段列表
      sensitiveFields: [
        'password', 'token', 'secret', 'key', 'authorization',
        'cookie', 'session', 'credit_card', 'ssn', 'phone',
      ],
      // 性能阈值
      performanceThresholds: {
        slow: 1000, // 1秒
        critical: 5000, // 5秒
      },
      // 日志级别
      levels: {
        TRACE: 0,
        DEBUG: 1,
        INFO: 2,
        WARN: 3,
        ERROR: 4,
        FATAL: 5,
      },
      // 缓冲区配置
      buffer: {
        enabled: true,
        maxSize: 100,
        flushInterval: 5000, // 5秒
      },
    };
  }

  /**
   * 创建基础日志对象
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {Object} meta - 元数据
   * @return {Object} 日志对象
   */
  createLogEntry(level, message, meta = {}) {
    const entry = {
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      message,
      traceId: this.ctx.traceId || this.ctx.get('x-trace-id'),
      spanId: this.ctx.spanId || this.ctx.get('x-span-id'),
      requestId: this.ctx.requestId || this.generateRequestId(),
      service: 'blackbear-backend',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',

      // 请求上下文
      request: {
        method: this.ctx.method,
        url: this.ctx.url,
        path: this.ctx.path,
        userAgent: this.ctx.get('user-agent'),
        ip: this.ctx.ip,
        headers: this.sanitizeHeaders(this.ctx.headers),
      },

      // 用户信息
      user: this.ctx.userInfo ? {
        id: this.ctx.userInfo.id,
        username: this.ctx.userInfo.username,
        role: this.ctx.userInfo.role,
      } : null,

      // 性能信息
      performance: {
        duration: Date.now() - this.startTime,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
      },

      // 自定义元数据
      meta: this.sanitizeMetadata(meta),
    };

    return entry;
  }

  /**
   * 记录信息日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 元数据
   */
  info(message, meta = {}) {
    const entry = this.createLogEntry('info', message, meta);
    this.writeLog(entry);
  }

  /**
   * 记录调试日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 元数据
   */
  debug(message, meta = {}) {
    const entry = this.createLogEntry('debug', message, meta);
    this.writeLog(entry);
  }

  /**
   * 记录警告日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 元数据
   */
  warn(message, meta = {}) {
    const entry = this.createLogEntry('warn', message, meta);
    this.writeLog(entry);
  }

  /**
   * 记录错误日志
   * @param {string} message - 日志消息
   * @param {Error|Object} error - 错误对象
   * @param {Object} meta - 元数据
   */
  error(message, error, meta = {}) {
    const errorMeta = {
      ...meta,
      error: this.serializeError(error),
    };

    const entry = this.createLogEntry('error', message, errorMeta);
    this.writeLog(entry);

    // 错误日志立即刷新
    this.flush();
  }

  /**
   * 记录致命错误日志
   * @param {string} message - 日志消息
   * @param {Error|Object} error - 错误对象
   * @param {Object} meta - 元数据
   */
  fatal(message, error, meta = {}) {
    const errorMeta = {
      ...meta,
      error: this.serializeError(error),
    };

    const entry = this.createLogEntry('fatal', message, errorMeta);
    this.writeLog(entry);

    // 致命错误立即刷新并发送告警
    this.flush();
    this.sendAlert('fatal_error', entry);
  }

  /**
   * 记录性能日志
   * @param {string} operation - 操作名称
   * @param {number} duration - 持续时间
   * @param {Object} meta - 元数据
   */
  performance(operation, duration, meta = {}) {
    const level = this.getPerformanceLevel(duration);
    const message = `Performance: ${operation} took ${duration}ms`;

    const perfMeta = {
      ...meta,
      performance: {
        operation,
        duration,
        threshold: this.config.performanceThresholds,
        level,
      },
    };

    this[level](message, perfMeta);
  }

  /**
   * 记录业务日志
   * @param {string} action - 业务动作
   * @param {Object} data - 业务数据
   * @param {Object} meta - 元数据
   */
  business(action, data = {}, meta = {}) {
    const message = `Business Action: ${action}`;
    const businessMeta = {
      ...meta,
      business: {
        action,
        data: this.sanitizeMetadata(data),
        timestamp: new Date().toISOString(),
      },
    };

    this.info(message, businessMeta);
  }

  /**
   * 记录安全日志
   * @param {string} event - 安全事件
   * @param {Object} details - 事件详情
   * @param {string} severity - 严重程度
   */
  security(event, details = {}, severity = 'info') {
    const message = `Security Event: ${event}`;
    const securityMeta = {
      security: {
        event,
        details: this.sanitizeMetadata(details),
        severity,
        ip: this.ctx.ip,
        userAgent: this.ctx.get('user-agent'),
        timestamp: new Date().toISOString(),
      },
    };

    this[severity](message, securityMeta);

    // 高危安全事件发送告警
    if ([ 'warn', 'error', 'fatal' ].includes(severity)) {
      this.sendAlert('security_event', { event, severity, details });
    }
  }

  /**
   * 记录数据库操作日志
   * @param {string} operation - 操作类型
   * @param {string} table - 表名
   * @param {number} duration - 执行时间
   * @param {Object} meta - 元数据
   */
  database(operation, table, duration, meta = {}) {
    const message = `Database: ${operation} on ${table} (${duration}ms)`;
    const dbMeta = {
      ...meta,
      database: {
        operation,
        table,
        duration,
        slow: duration > 1000,
      },
    };

    const level = duration > 1000 ? 'warn' : 'debug';
    this[level](message, dbMeta);
  }

  /**
   * 记录缓存操作日志
   * @param {string} operation - 操作类型
   * @param {string} key - 缓存键
   * @param {boolean} hit - 是否命中
   * @param {Object} meta - 元数据
   */
  cache(operation, key, hit = null, meta = {}) {
    const message = `Cache: ${operation} ${key}${hit !== null ? (hit ? ' (HIT)' : ' (MISS)') : ''}`;
    const cacheMeta = {
      ...meta,
      cache: {
        operation,
        key: this.sanitizeCacheKey(key),
        hit,
      },
    };

    this.debug(message, cacheMeta);
  }

  /**
   * 写入日志
   * @param {Object} entry - 日志条目
   */
  writeLog(entry) {
    if (this.config.buffer.enabled) {
      this.logBuffer.push(entry);

      if (this.logBuffer.length >= this.config.buffer.maxSize) {
        this.flush();
      }
    } else {
      this.outputLog(entry);
    }
  }

  /**
   * 刷新日志缓冲区
   */
  flush() {
    if (this.logBuffer.length === 0) { return; }

    const logs = [ ...this.logBuffer ];
    this.logBuffer = [];

    logs.forEach(log => this.outputLog(log));
  }

  /**
   * 输出日志
   * @param {Object} entry - 日志条目
   */
  outputLog(entry) {
    const level = entry.level.toLowerCase();

    if (this.baseLogger[level]) {
      this.baseLogger[level](entry);
    } else {
      this.baseLogger.info(entry);
    }

    // 发送到外部日志系统
    this.sendToExternalLogger(entry);
  }

  /**
   * 发送到外部日志系统
   * @param {Object} entry - 日志条目
   */
  sendToExternalLogger(entry) {
    // 这里可以集成ELK、Fluentd等日志系统
    this.ctx.app.emit('structured-log', entry);
  }

  /**
   * 序列化错误对象
   * @param {Error|Object} error - 错误对象
   * @return {Object} 序列化后的错误
   */
  serializeError(error) {
    if (!error) { return null; }

    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code,
        status: error.status,
      };
    }

    return this.sanitizeMetadata(error);
  }

  /**
   * 清理敏感信息
   * @param {Object} data - 原始数据
   * @return {Object} 清理后的数据
   */
  sanitizeMetadata(data) {
    if (!data || typeof data !== 'object') { return data; }

    return SecurityValidator.maskSensitiveData(data, this.config.sensitiveFields);
  }

  /**
   * 清理请求头
   * @param {Object} headers - 请求头
   * @return {Object} 清理后的请求头
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };

    this.config.sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    });

    return sanitized;
  }

  /**
   * 清理缓存键
   * @param {string} key - 缓存键
   * @return {string} 清理后的缓存键
   */
  sanitizeCacheKey(key) {
    // 移除可能包含敏感信息的部分
    return key.replace(/user:\d+/g, 'user:***').replace(/token:[a-f0-9]+/g, 'token:***');
  }

  /**
   * 获取性能日志级别
   * @param {number} duration - 持续时间
   * @return {string} 日志级别
   */
  getPerformanceLevel(duration) {
    if (duration > this.config.performanceThresholds.critical) {
      return 'error';
    } else if (duration > this.config.performanceThresholds.slow) {
      return 'warn';
    }
    return 'debug';

  }

  /**
   * 生成请求ID
   * @return {string} 请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 发送告警
   * @param {string} type - 告警类型
   * @param {Object} data - 告警数据
   */
  sendAlert(type, data) {
    this.ctx.app.emit('alert', {
      type,
      data,
      timestamp: new Date().toISOString(),
      service: 'blackbear-backend',
    });
  }

  /**
   * 启动定期刷新
   */
  startPeriodicFlush() {
    if (this.flushTimer) { return; }

    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.buffer.flushInterval);
  }

  /**
   * 停止定期刷新
   */
  stopPeriodicFlush() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    // 最后刷新一次
    this.flush();
  }
}

module.exports = StructuredLogger;
