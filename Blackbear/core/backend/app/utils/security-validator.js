'use strict';

const xss = require('xss');
const validator = require('validator');

/**
 * 安全验证工具类
 * 提供输入验证、XSS防护、SQL注入防护等安全功能
 */
class SecurityValidator {

  /**
   * SQL注入防护 - 清理SQL输入
   * @param {string} input - 输入字符串
   * @return {string} 清理后的字符串
   */
  static sanitizeSqlInput(input) {
    if (typeof input !== 'string') { return input; }

    // 移除危险字符
    return input.replace(/['"\\;]/g, '').trim();
  }

  /**
   * XSS防护 - 清理HTML输入
   * @param {string} input - 输入字符串
   * @param {Object} options - XSS过滤选项
   * @return {string} 清理后的字符串
   */
  static sanitizeHtmlInput(input, options = {}) {
    if (typeof input !== 'string') { return input; }

    const defaultOptions = {
      whiteList: {
        p: [],
        br: [],
        strong: [],
        em: [],
        u: [],
        b: [],
        i: [],
      },
      stripIgnoreTag: true,
      stripIgnoreTagBody: [ 'script' ],
    };

    return xss(input, { ...defaultOptions, ...options });
  }

  /**
   * 敏感信息脱敏
   * @param {Object} data - 数据对象
   * @param {Array} fields - 需要脱敏的字段
   * @return {Object} 脱敏后的数据
   */
  static maskSensitiveData(data, fields = [ 'password', 'token', 'secret', 'key' ]) {
    if (!data || typeof data !== 'object') { return data; }

    const masked = { ...data };
    fields.forEach(field => {
      if (masked[field]) {
        const value = masked[field];
        if (typeof value === 'string') {
          // 保留前2位和后2位，中间用*替换
          if (value.length > 4) {
            masked[field] = value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2);
          } else {
            masked[field] = '*'.repeat(value.length);
          }
        } else {
          masked[field] = '***';
        }
      }
    });

    return masked;
  }

  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱地址
   * @return {boolean} 是否有效
   */
  static isValidEmail(email) {
    return validator.isEmail(email);
  }

  /**
   * 验证手机号格式（中国大陆）
   * @param {string} phone - 手机号
   * @return {boolean} 是否有效
   */
  static isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证URL格式
   * @param {string} url - URL地址
   * @return {boolean} 是否有效
   */
  static isValidUrl(url) {
    return validator.isURL(url, {
      protocols: [ 'http', 'https' ],
      require_protocol: true,
    });
  }

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @return {Object} 验证结果
   */
  static validatePasswordStrength(password) {
    const result = {
      isValid: false,
      score: 0,
      issues: [],
    };

    if (!password || typeof password !== 'string') {
      result.issues.push('密码不能为空');
      return result;
    }

    // 长度检查
    if (password.length < 8) {
      result.issues.push('密码长度至少8位');
    } else {
      result.score += 1;
    }

    // 包含数字
    if (/\d/.test(password)) {
      result.score += 1;
    } else {
      result.issues.push('密码应包含数字');
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      result.score += 1;
    } else {
      result.issues.push('密码应包含小写字母');
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      result.score += 1;
    } else {
      result.issues.push('密码应包含大写字母');
    }

    // 包含特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.score += 1;
    } else {
      result.issues.push('密码应包含特殊字符');
    }

    result.isValid = result.score >= 3 && result.issues.length === 0;

    return result;
  }

  /**
   * 验证JWT Token格式
   * @param {string} token - JWT Token
   * @return {boolean} 是否有效格式
   */
  static isValidJwtFormat(token) {
    if (!token || typeof token !== 'string') { return false; }

    const parts = token.split('.');
    return parts.length === 3;
  }

  /**
   * 清理文件名
   * @param {string} filename - 文件名
   * @return {string} 清理后的文件名
   */
  static sanitizeFilename(filename) {
    if (!filename || typeof filename !== 'string') { return ''; }

    // 移除危险字符
    return filename.replace(/[<>:"/\\|?*\x00-\x1f]/g, '').trim();
  }

  /**
   * 验证IP地址
   * @param {string} ip - IP地址
   * @return {boolean} 是否有效
   */
  static isValidIP(ip) {
    return validator.isIP(ip);
  }

  /**
   * 检查是否为安全的重定向URL
   * @param {string} url - 重定向URL
   * @param {Array} allowedDomains - 允许的域名列表
   * @return {boolean} 是否安全
   */
  static isSafeRedirectUrl(url, allowedDomains = []) {
    if (!url || typeof url !== 'string') { return false; }

    try {
      const urlObj = new URL(url);

      // 只允许http和https协议
      if (![ 'http:', 'https:' ].includes(urlObj.protocol)) {
        return false;
      }

      // 检查域名白名单
      if (allowedDomains.length > 0) {
        return allowedDomains.includes(urlObj.hostname);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 生成安全的随机字符串
   * @param {number} length - 字符串长度
   * @return {string} 随机字符串
   */
  static generateSecureRandomString(length = 32) {
    const crypto = require('crypto');
    return crypto.randomBytes(length).toString('hex');
  }
}

module.exports = SecurityValidator;
