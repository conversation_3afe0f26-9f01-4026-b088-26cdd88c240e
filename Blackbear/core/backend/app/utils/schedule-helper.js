'use strict';

/**
 * 定时任务辅助工具类
 * 提供通用的错误处理、日志记录、数据验证等功能
 */
class ScheduleHelper {
  /**
   * 安全执行定时任务
   * @param {Object} ctx - Egg上下文
   * @param {string} taskName - 任务名称
   * @param {Function} taskFunction - 任务执行函数
   * @param {Object} options - 配置选项
   */
  static async safeExecute(ctx, taskName, taskFunction, options = {}) {
    const startTime = Date.now();

    try {
      ctx.logger.info(`开始执行定时任务: ${taskName}`);

      // 检查任务是否被禁用
      if (options.configKey) {
        const config = await ctx.service.configurations.getConfig('schedule');
        if (config && config[options.configKey] === false) {
          ctx.logger.info(`任务 ${taskName} 已被配置禁用，跳过执行`);
          return;
        }
      }

      // 检查环境
      if (options.skipLocal && ctx.app.config.env === 'local') {
        ctx.logger.info(`任务 ${taskName} 在本地环境跳过执行`);
        return;
      }

      // 执行任务
      const result = await taskFunction();

      const duration = Date.now() - startTime;
      ctx.logger.info(`任务 ${taskName} 执行成功，耗时: ${duration}ms`);

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      ctx.logger.error(`任务 ${taskName} 执行失败，耗时: ${duration}ms`, error);

      // 可选的错误通知
      if (options.notifyOnError) {
        await this.notifyError(ctx, taskName, error);
      }

      throw error;
    }
  }

  /**
   * 安全获取字符串字段并分割
   * @param {Object} obj - 数据对象
   * @param {string} field - 字段名
   * @param {string} separator - 分隔符
   * @param {*} defaultValue - 默认值
   */
  static safeSplit(obj, field, separator = ',', defaultValue = []) {
    try {
      const value = obj && obj.get ? obj.get(field) : obj[field];

      if (!value || typeof value !== 'string') {
        return defaultValue;
      }

      return value.split(separator)
        .map(item => item.trim())
        .filter(item => item.length > 0);

    } catch (error) {
      return defaultValue;
    }
  }

  /**
   * 安全遍历数组并提取字段
   * @param {Array} array - 数组
   * @param {string|Function} fieldOrExtractor - 字段名或提取函数
   * @param {*} defaultValue - 默认值
   */
  static safeMap(array, fieldOrExtractor, defaultValue = []) {
    if (!Array.isArray(array)) {
      return defaultValue;
    }

    const result = [];

    array.forEach((item, index) => {
      try {
        if (!item) { return; }

        let value;
        if (typeof fieldOrExtractor === 'function') {
          value = fieldOrExtractor(item);
        } else if (typeof fieldOrExtractor === 'string') {
          // 支持嵌套字段访问，如 'tag.name'
          const fields = fieldOrExtractor.split('.');
          value = item;
          for (const field of fields) {
            if (value && typeof value === 'object') {
              value = value[field];
            } else {
              value = undefined;
              break;
            }
          }
        } else {
          value = item;
        }

        if (value !== undefined && value !== null) {
          result.push(value);
        }

      } catch (error) {
        // 记录错误但继续处理其他项
        console.warn(`处理数组项 ${index} 时发生错误:`, error);
      }
    });

    return result;
  }

  /**
   * 安全的数据库事务执行
   * @param {Object} ctx - Egg上下文
   * @param {Function} transactionFunction - 事务函数
   * @param {string} operationName - 操作名称
   */
  static async safeTransaction(ctx, transactionFunction, operationName = '数据库操作') {
    try {
      return await ctx.model.transaction(async transaction => {
        return await transactionFunction(transaction);
      });
    } catch (error) {
      ctx.logger.error(`${operationName}事务执行失败:`, error);
      throw error;
    }
  }

  /**
   * 批量处理数据，支持错误容错
   * @param {Array} items - 待处理的数据项
   * @param {Function} processor - 处理函数
   * @param {Object} options - 选项
   */
  static async batchProcess(items, processor, options = {}) {
    const {
      batchSize = 10,
      continueOnError = true,
      logger = console,
    } = options;

    const results = [];
    const errors = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);

      for (const item of batch) {
        try {
          const result = await processor(item);
          results.push(result);
        } catch (error) {
          errors.push({ item, error });
          logger.error('处理项目失败:', error);

          if (!continueOnError) {
            throw error;
          }
        }
      }
    }

    return { results, errors };
  }

  /**
   * 错误通知
   * @param {Object} ctx - Egg上下文
   * @param {string} taskName - 任务名称
   * @param {Error} error - 错误对象
   */
  static async notifyError(ctx, taskName, error) {
    try {
      // 发送系统消息
      if (ctx.service.messages && ctx.service.messages.sendMessage) {
        await ctx.service.messages.sendMessage({
          type: 'system',
          title: '定时任务执行失败',
          content: `任务: ${taskName}\n错误: ${error.message}`,
          userId: ctx.app.config.systemUserId,
        });
      }
    } catch (notifyError) {
      ctx.logger.error('发送错误通知失败:', notifyError);
    }
  }

  /**
   * 验证必要的配置和服务
   * @param {Object} ctx - Egg上下文
   * @param {Array} requiredServices - 必需的服务列表
   */
  static validateRequirements(ctx, requiredServices = []) {
    const missing = [];

    requiredServices.forEach(service => {
      const servicePath = service.split('.');
      let current = ctx;

      for (const part of servicePath) {
        if (current && current[part]) {
          current = current[part];
        } else {
          missing.push(service);
          break;
        }
      }
    });

    if (missing.length > 0) {
      throw new Error(`缺少必要的服务: ${missing.join(', ')}`);
    }
  }

  /**
   * 格式化时间
   * @param {Date|string} date - 日期
   * @param {string} format - 格式
   */
  static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const moment = require('moment');
    return moment(date).format(format);
  }

  /**
   * 安全的数值计算
   * @param {*} value - 值
   * @param {number} defaultValue - 默认值
   * @param {number} precision - 精度
   */
  static safeNumber(value, defaultValue = 0, precision = 2) {
    const num = parseFloat(value);
    if (isNaN(num)) {
      return defaultValue;
    }
    return Number(num.toFixed(precision));
  }
}

module.exports = ScheduleHelper;
