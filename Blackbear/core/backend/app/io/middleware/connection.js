'use strict';

/**
 * Socket.IO 连接中间件
 * 处理用户连接认证、房间管理和在线状态同步
 * @param app
 */
module.exports = app => {
  return async (ctx, next) => {
    const { socket, logger } = ctx;
    const { socketOnlineUserRoomName } = app.config;
    const nsp = app.io.of('/');

    // 连接建立处理
    try {
      const { token } = socket.handshake.query;

      // Token验证
      if (!token) {
        console.warn('Socket connection without token, disconnecting');
        socket.disconnect();
        return;
      }

      // JWT验证
      try {
        await app.jwt.verify(token, app.config.jwt.secret);
      } catch (jwtError) {
        console.warn('Socket JWT verification failed:', jwtError.message);
        socket.emit('auth_error', 'Token验证失败');
        socket.disconnect();
        return;
      }

      // 加入在线用户房间
      await new Promise((resolve, reject) => {
        socket.join(socketOnlineUserRoomName, error => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      // 延迟处理在线状态通知（避免阻塞连接）
      setImmediate(() => {
        handleOnlineStatusUpdate(ctx, socket, nsp, socketOnlineUserRoomName, logger);
      });

    } catch (error) {
      console.error('Socket connection error:', error);
      logger.error('Socket connection failed:', error);

      try {
        socket.emit('connection_error', '连接失败');
        socket.disconnect();
      } catch (emitError) {
        console.error('Error emitting connection error:', emitError);
      }
      return;
    }

    // 执行下一个中间件
    await next();

    // 连接断开处理
    try {
      await handleDisconnection(ctx, socket, socketOnlineUserRoomName);
    } catch (error) {
      console.error('Socket disconnection error:', error);
    }
  };
};

/**
 * 处理在线状态更新
 * @param ctx
 * @param socket
 * @param nsp
 * @param roomName
 * @param logger
 */
function handleOnlineStatusUpdate(ctx, socket, nsp, roomName, logger) {
  try {
    // 通知其他用户有新用户加入
    ctx.helper.sendSocketToClientOfRoom({ socketId: socket.id }, 'join');

    // 获取当前在线用户列表
    nsp.adapter.clients([ roomName ], (err, clients) => {
      if (err) {
        logger.error('Failed to get online clients:', err);
        return;
      }

      try {
        // 构建在线用户ID集合
        const onlineIds = new Set(clients);
        onlineIds.add(socket.id);

        // 发送在线用户列表给新连接的用户
        const message = ctx.helper.parseSocketMsg(Array.from(onlineIds), socket.id, 'online');
        const emitData = [ 'sync', message ];

        socket.emit(...emitData);

        // 可选：存储到Redis用于消息重发机制
        // app.redis.setex(
        //   ctx.helper.redisKeys.socketBaseSocketId(message.id),
        //   app.config.socketRedisExp,
        //   JSON.stringify(emitData)
        // );

      } catch (error) {
        logger.error('Failed to send online status:', error);
      }
    });
  } catch (error) {
    logger.error('Handle online status update error:', error);
  }
}

/**
 * 处理连接断开
 * @param ctx
 * @param socket
 * @param roomName
 */
async function handleDisconnection(ctx, socket, roomName) {
  try {
    // 重新加入房间以便发送离开通知
    await new Promise(resolve => {
      socket.join(roomName, () => {
        // 延迟发送离开通知，确保其他操作完成
        setImmediate(() => {
          try {
            ctx.helper.sendSocketToClientOfRoom({ socketId: socket.id }, 'leave');
          } catch (error) {
            console.error('Failed to send leave notification:', error);
          }
          resolve();
        });
      });
    });
  } catch (error) {
    console.error('Disconnection handling error:', error);
  }
}
