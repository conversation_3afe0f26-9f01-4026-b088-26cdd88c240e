'use strict';

/**
 * Socket.IO 数据包处理中间件
 * 处理所有传入的Socket.IO数据包
 * @param app
 */
module.exports = app => {
  return async (ctx, next) => {
    try {
      // 发送确认响应
      await ctx.socket.emit('res', 'packet received!');

      // 记录数据包信息（仅在开发环境）
      if (app.config.env !== 'prod') {
        console.log('Socket packet received:', {
          socketId: ctx.socket.id,
          event: ctx.packet?.[0],
          timestamp: new Date().toISOString(),
        });
      }

      // 继续处理
      await next();

    } catch (error) {
      console.error('Packet middleware error:', error);
      app.logger.error('Socket packet processing error:', error);

      // 尝试发送错误响应
      try {
        await ctx.socket.emit('error', '数据包处理失败');
      } catch (emitError) {
        console.error('Error emit failed:', emitError);
      }
    }
  };
};
