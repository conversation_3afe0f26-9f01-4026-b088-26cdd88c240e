'use strict';

const { Controller } = require('egg');

/**
 * Socket.IO 基础控制器
 * 提供统一的性能优化和错误处理
 */
/**
 * BaseSocketController
 * Handles HTTP requests for specific resource
 */
class BaseSocketController extends Controller {
  constructor(ctx) {
    super(ctx);
    this.performanceUtils = this.app.socketPerformanceUtils;
  }

  /**
   * 安全的消息发送
   * @param event
   * @param data
   * @param options
   */
  async safeEmit(event, data, options = {}) {
    const { ctx } = this;
    const { timeout = 5000, retry = false } = options;

    try {
      // 更新活动时间
      if (this.performanceUtils) {
        this.performanceUtils.updateActivity(ctx.socket.id);
      }

      // 限流检查
      if (this.performanceUtils && !this.performanceUtils.checkRateLimit(ctx.socket.id)) {
        console.warn(`Rate limit exceeded for socket ${ctx.socket.id}`);
        return false;
      }

      // 发送消息
      const promise = ctx.socket.emit(event, data);

      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Emit timeout')), timeout);
      });

      await Promise.race([ promise, timeoutPromise ]);
      return true;

    } catch (error) {
      console.error(`Safe emit error for event ${event}:`, error);

      // 重试机制
      if (retry && options.retryCount < 3) {
        options.retryCount = (options.retryCount || 0) + 1;
        setTimeout(() => {
          this.safeEmit(event, data, options);
        }, 1000 * options.retryCount);
      }

      return false;
    }
  }

  /**
   * 批量发送消息
   * @param events
   */
  async batchEmit(events) {
    const { ctx } = this;
    const nsp = this.app.io.of('/');

    if (this.performanceUtils) {
      await this.performanceUtils.batchEmit(nsp, events);
    } else {
      // 降级处理
      for (const event of events) {
        try {
          await ctx.socket.emit(event.target, event.data);
        } catch (error) {
          console.error('Batch emit fallback error:', error);
        }
      }
    }
  }

  /**
   * 验证消息格式
   * @param message
   * @param schema
   */
  validateMessage(message, schema = {}) {
    if (!message || typeof message !== 'object') {
      return { valid: false, error: 'Invalid message format' };
    }

    // 基础验证
    const { required = [], optional = [] } = schema;
    const messageKeys = Object.keys(message);

    // 检查必需字段
    for (const field of required) {
      if (!messageKeys.includes(field)) {
        return { valid: false, error: `Missing required field: ${field}` };
      }
    }

    // 检查字段类型
    for (const [ field, type ] of Object.entries(schema.types || {})) {
      if (message[field] !== undefined && typeof message[field] !== type) {
        return { valid: false, error: `Invalid type for field ${field}, expected ${type}` };
      }
    }

    return { valid: true };
  }

  /**
   * 获取客户端信息
   */
  getClientInfo() {
    const { ctx } = this;
    const { socket } = ctx;

    return {
      id: socket.id,
      ip: socket.handshake.address,
      userAgent: socket.handshake.headers['user-agent'],
      connectTime: socket.handshake.time,
      rooms: Object.keys(socket.rooms),
    };
  }

  /**
   * 记录操作日志
   * @param operation
   * @param data
   * @param level
   */
  logOperation(operation, data = {}, level = 'info') {
    const { ctx, app } = this;
    const clientInfo = this.getClientInfo();

    const logData = {
      operation,
      socketId: clientInfo.id,
      timestamp: new Date().toISOString(),
      ...data,
    };

    if (level === 'error') {
      app.logger.error('Socket operation error:', logData);
    } else if (app.config.env !== 'prod') {
      console.log('Socket operation:', logData);
    }
  }

  /**
   * 错误处理
   * @param error
   * @param context
   */
  async handleError(error, context = '') {
    const { ctx } = this;

    // 记录错误
    this.logOperation('error', {
      context,
      error: error.message,
      stack: error.stack,
    }, 'error');

    // 发送错误响应给客户端
    try {
      await this.safeEmit('error', {
        message: '操作失败',
        context,
        timestamp: new Date().toISOString(),
      });
    } catch (emitError) {
      console.error('Error emit failed:', emitError);
    }
  }

  /**
   * 房间管理
   * @param roomName
   * @param callback
   */
  async joinRoom(roomName, callback) {
    const { ctx } = this;

    return new Promise((resolve, reject) => {
      ctx.socket.join(roomName, error => {
        if (error) {
          this.logOperation('join_room_failed', { roomName, error: error.message }, 'error');
          reject(error);
        } else {
          this.logOperation('join_room_success', { roomName });
          if (callback) { callback(); }
          resolve();
        }
      });
    });
  }

  /**
   * 离开房间
   * @param roomName
   * @param callback
   */
  async leaveRoom(roomName, callback) {
    const { ctx } = this;

    return new Promise((resolve, reject) => {
      ctx.socket.leave(roomName, error => {
        if (error) {
          this.logOperation('leave_room_failed', { roomName, error: error.message }, 'error');
          reject(error);
        } else {
          this.logOperation('leave_room_success', { roomName });
          if (callback) { callback(); }
          resolve();
        }
      });
    });
  }

  /**
   * 广播到房间
   * @param roomName
   * @param event
   * @param data
   */
  async broadcastToRoom(roomName, event, data) {
    const { app } = this;

    try {
      const nsp = app.io.of('/');
      nsp.to(roomName).emit(event, data);

      this.logOperation('broadcast_success', { roomName, event });
      return true;
    } catch (error) {
      this.logOperation('broadcast_failed', {
        roomName,
        event,
        error: error.message,
      }, 'error');
      return false;
    }
  }

  /**
   * 获取房间客户端列表
   * @param roomName
   */
  async getRoomClients(roomName) {
    const { app } = this;

    return new Promise((resolve, reject) => {
      const nsp = app.io.of('/');
      nsp.adapter.clients([ roomName ], (error, clients) => {
        if (error) {
          reject(error);
        } else {
          resolve(clients || []);
        }
      });
    });
  }

  /**
   * 性能监控
   */
  getPerformanceMetrics() {
    if (this.performanceUtils) {
      return this.performanceUtils.getPerformanceMetrics();
    }
    return null;
  }
}

module.exports = BaseSocketController;
