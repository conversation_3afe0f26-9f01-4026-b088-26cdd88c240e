'use strict';

/**
 * Socket.IO 性能优化工具类
 * 提供连接管理、消息缓存、性能监控等功能
 */
class SocketPerformanceUtils {
  constructor(app) {
    this.app = app;
    this.connectionStats = new Map(); // 连接统计
    this.messageQueue = new Map(); // 消息队列
    this.rateLimiters = new Map(); // 限流器
  }

  /**
   * 记录连接统计信息
   * @param socketId
   * @param userInfo
   */
  recordConnection(socketId, userInfo = {}) {
    const stats = {
      socketId,
      userId: userInfo.id,
      connectTime: Date.now(),
      messageCount: 0,
      lastActivity: Date.now(),
    };

    this.connectionStats.set(socketId, stats);

    // 定期清理过期连接统计
    this.scheduleStatsCleanup();
  }

  /**
   * 更新连接活动时间
   * @param socketId
   */
  updateActivity(socketId) {
    const stats = this.connectionStats.get(socketId);
    if (stats) {
      stats.lastActivity = Date.now();
      stats.messageCount += 1;
    }
  }

  /**
   * 移除连接统计
   * @param socketId
   */
  removeConnection(socketId) {
    this.connectionStats.delete(socketId);
    this.messageQueue.delete(socketId);
    this.rateLimiters.delete(socketId);
  }

  /**
   * 获取连接统计信息
   * @param socketId
   */
  getConnectionStats(socketId) {
    return this.connectionStats.get(socketId);
  }

  /**
   * 获取所有连接统计
   */
  getAllStats() {
    const stats = {
      totalConnections: this.connectionStats.size,
      connections: Array.from(this.connectionStats.values()),
      timestamp: Date.now(),
    };

    return stats;
  }

  /**
   * 消息限流检查
   * @param socketId
   * @param limit
   * @param window
   */
  checkRateLimit(socketId, limit = 100, window = 60000) {
    const now = Date.now();
    const key = `${socketId}_${Math.floor(now / window)}`;

    if (!this.rateLimiters.has(key)) {
      this.rateLimiters.set(key, { count: 0, window: now + window });

      // 清理过期的限流记录
      setTimeout(() => {
        this.rateLimiters.delete(key);
      }, window);
    }

    const limiter = this.rateLimiters.get(key);
    limiter.count += 1;

    return limiter.count <= limit;
  }

  /**
   * 批量发送消息（性能优化）
   * @param nsp
   * @param events
   */
  async batchEmit(nsp, events) {
    if (!Array.isArray(events) || events.length === 0) {
      return;
    }

    try {
      // 按目标分组消息
      const groupedEvents = this.groupEventsByTarget(events);

      // 批量发送
      const promises = Object.entries(groupedEvents).map(([ target, targetEvents ]) => {
        return this.emitToTarget(nsp, target, targetEvents);
      });

      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Batch emit error:', error);
    }
  }

  /**
   * 按目标分组事件
   * @param events
   */
  groupEventsByTarget(events) {
    const grouped = {};

    events.forEach(event => {
      const { target, data } = event;
      if (!grouped[target]) {
        grouped[target] = [];
      }
      grouped[target].push(data);
    });

    return grouped;
  }

  /**
   * 发送到指定目标
   * @param nsp
   * @param target
   * @param events
   */
  async emitToTarget(nsp, target, events) {
    try {
      if (events.length === 1) {
        // 单个事件直接发送
        nsp.emit(target, events[0]);
      } else {
        // 多个事件批量发送
        nsp.emit(target, { type: 'batch', events });
      }
    } catch (error) {
      console.error(`Emit to target ${target} error:`, error);
    }
  }

  /**
   * 消息队列管理
   * @param socketId
   * @param message
   */
  queueMessage(socketId, message) {
    if (!this.messageQueue.has(socketId)) {
      this.messageQueue.set(socketId, []);
    }

    const queue = this.messageQueue.get(socketId);
    queue.push({
      ...message,
      timestamp: Date.now(),
    });

    // 限制队列大小
    if (queue.length > 100) {
      queue.shift(); // 移除最旧的消息
    }
  }

  /**
   * 获取队列中的消息
   * @param socketId
   */
  getQueuedMessages(socketId) {
    return this.messageQueue.get(socketId) || [];
  }

  /**
   * 清空消息队列
   * @param socketId
   */
  clearQueue(socketId) {
    this.messageQueue.delete(socketId);
  }

  /**
   * 定期清理过期统计数据
   */
  scheduleStatsCleanup() {
    if (this.cleanupScheduled) {
      return;
    }

    this.cleanupScheduled = true;

    setTimeout(() => {
      this.cleanupExpiredStats();
      this.cleanupScheduled = false;
    }, 5 * 60 * 1000); // 5分钟清理一次
  }

  /**
   * 清理过期的统计数据
   */
  cleanupExpiredStats() {
    const now = Date.now();
    const expireTime = 30 * 60 * 1000; // 30分钟过期

    for (const [ socketId, stats ] of this.connectionStats.entries()) {
      if (now - stats.lastActivity > expireTime) {
        this.removeConnection(socketId);
      }
    }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    const now = Date.now();
    const connections = Array.from(this.connectionStats.values());

    const metrics = {
      totalConnections: connections.length,
      activeConnections: connections.filter(c => now - c.lastActivity < 5 * 60 * 1000).length,
      totalMessages: connections.reduce((sum, c) => sum + c.messageCount, 0),
      averageConnectionTime: connections.length > 0
        ? connections.reduce((sum, c) => sum + (now - c.connectTime), 0) / connections.length
        : 0,
      queuedMessages: Array.from(this.messageQueue.values()).reduce((sum, q) => sum + q.length, 0),
      rateLimiters: this.rateLimiters.size,
      timestamp: now,
    };

    return metrics;
  }

  /**
   * 内存使用优化
   */
  optimizeMemory() {
    // 清理过期数据
    this.cleanupExpiredStats();

    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }
  }
}

module.exports = SocketPerformanceUtils;
