'use strict';

const SocketPerformanceUtils = require('./utils/socket-performance');

/**
 * Socket.IO 模块初始化
 * 设置性能监控和优化工具
 * @param app
 */
module.exports = app => {
  // 初始化性能工具
  app.socketPerformanceUtils = new SocketPerformanceUtils(app);

  // 监听连接事件
  app.io.on('connection', socket => {
    // 记录连接
    app.socketPerformanceUtils.recordConnection(socket.id, socket.handshake.query);

    // 监听断开连接
    socket.on('disconnect', () => {
      app.socketPerformanceUtils.removeConnection(socket.id);
    });
  });

  // 定期性能监控
  if (app.config.env !== 'prod') {
    setInterval(() => {
      const metrics = app.socketPerformanceUtils.getPerformanceMetrics();
      console.log('Socket.IO Performance Metrics:', metrics);
    }, 60000); // 每分钟输出一次
  }

  // 定期内存优化
  setInterval(() => {
    app.socketPerformanceUtils.optimizeMemory();
  }, 10 * 60 * 1000); // 每10分钟优化一次

  console.log('Socket.IO performance optimization initialized');
};
