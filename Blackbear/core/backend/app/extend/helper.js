'use strict';

const childProcess = require('child_process');
const _ = require('lodash');
const FormData = require('form-data');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const uuid = require('uuid');
const crypto = require('crypto');
const moment = require('moment');
const md5 = require('md5');

module.exports = {
  toInt(str) {
    if (typeof str === 'number') { return str; }
    if (!str) { return str; }
    return parseInt(str, 10) || 0;
  },
  md5(str) {
    return md5(str);
  },
  isAdmin() {
    return this.ctx.state && this.ctx.state.user && this.ctx.state.user.roleId === 'admin.top';
  },
  isPartner() {
    return this.ctx.state && this.ctx.state.user && this.ctx.state.user.roleId === 'partner';
  },
  hasRole(roles) {
    if (!roles || roles.length == 0) {
      return false;
    }
    const role = this.ctx.state && this.ctx.state.user && this.ctx.state.user.roleId;

    return roles.indexOf(role) > -1;
  },
  parseMsg(action, payload = {}, metadata = {}) {
    const meta = Object.assign(
      {},
      {
        timestamp: Date.now(),
      },
      metadata
    );

    return {
      data: {
        action,
        payload,
      },
      meta,
    };
  },
  async runScript(cmd, opts) {
    return new Promise((resolve, reject) => {
      childProcess.exec(cmd, _.merge({
        maxBuffer: 1024 * 512 * 100,
        wrapArgs: false,
      }, opts || {}), (err, stdout) => {
        if (err) {
          return reject(_.trim(err));
        }
        resolve(_.trim(stdout));
      });
    });
  },

  async downloadIMGSave(url) {
    // const filename = url.match(/\/(\w+\.(?:png|jpg|jpeg|gif|bmp))$/i)
    const s = url.split('/');
    const filename = s[s.length - 1];
    const { public_uploads_path } = app.config.static;
    const filepath = path.join(public_uploads_path, filename);
    const writer = fs.createWriteStream(filepath);

    const response = await axios({
      url,
      method: 'GET',
      responseType: 'stream',
    });

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve(filepath));
      writer.on('error', reject);
    });
  },

  async downloadIMGWithUrl(url) {
    const result = await axios({
      method: 'GET',
      url,
      responseType: 'arraybuffer',
    });

    if (result.data) {
      const param = new FormData();
      param.append('file', result.data);
      return param;
    }

    return null;
  },
  getTokenInfo(jwt, auth, secret) {
    // 判断请求头是否包含token
    if (
      auth.authorization &&
      auth.authorization.split(' ')[0] === 'Bearer'
    ) {
      const token = auth.authorization.split(' ')[1];
      let decode = '';
      if (token) {
        decode = jwt.verify(token, secret);
      }
      return decode;
    }
    return null;
  },
  /**
   * socket消息规则解析
   * @param params
   * @param clientId
   * @param action
   * @param method
   */
  parseSocketMsg(params, clientId, action, method = 'publish') {
    const data = {
      id: uuid.v4(),
      clientId,
      action,
      method,
      params,
    };
    return data;
  },
  /**
   * 发送socket消息给room里的每个连接,并录入redis
   * @param params
   * @param action
   * @param messageType
   * @param method
   */
  async sendSocketToClientOfRoom(params, action, messageType = 'sync', method = 'publish') {
    const { ctx, app, redisKeys } = this;
    const nsp = app.io.of('/');
    const roomName = app.config.socketOnlineUserRoomName;
    try {
      nsp.adapter.clients([ roomName ], (_err, clients) => {
        clients.forEach(clientId => {
          const data = ctx.helper.parseSocketMsg(params, clientId, action, method);
          const socket = nsp.to(clientId);
          const emitData = [ messageType, data ];
          socket.emit(...emitData);
          // 存入redis，接收到ACK则删除，否则在 this.app.config.socketRedisExp 时间内多次重发
          app.redis.setex(redisKeys.socketBaseSocketId(data.id), app.config.socketRedisExp, JSON.stringify(emitData));
        });
      });
    } catch (e) {
      app.logger.errorAndSentry(e);
    }
  },
  /**
   * 给单个socket发送消息,并录入redis
   * @param userId
   * @param params
   * @param action
   * @param messageType
   * @param method
   */
  sendMessageToSocket(userId, params, action, messageType = 'sync', method = 'publish') {
    const { ctx, app, redisKeys } = this;
    const nsp = app.io.of('/');
    nsp.adapter.clients((err, clients) => {
      if (err) {
        app.logger.errorAndSentry(err);
        return;
      }
      clients.forEach(clientId => {
        // 正则userID_uuid，给同一个用户多个socket分别发送消息
        const rex = new RegExp(`^${userId}`);
        if (rex.test(clientId)) {
          try {
            const socket = nsp.to(clientId);
            // 当此用户在线，则发送消息
            if (socket) {
              const _message = ctx.helper.parseSocketMsg(params, clientId, action, method);
              const emitData = [ messageType, _message ];
              socket.emit(...emitData);
              // 存入redis，接收到ACK则删除，否则在 this.app.config.socketRedisExp 时间内多次重发
              app.redis.setex(redisKeys.socketBaseSocketId(_message.id), app.config.socketRedisExp, JSON.stringify(emitData));
            }
          } catch (e) {
            app.logger.errorAndSentry(e);
          }
        }
      });
    });
  },


  // 如果今天是交易日9点30之后取今天的日期，其它时间都取前一个交易日的日期
  async getTradeDate() {
    const daliy = await this.ctx.service.quants.getToadyDaliy();
    if (daliy && daliy.isOpen) {
      const now = new Date();
      const hour = now.getHours();
      const minute = now.getMinutes();

      // 大于9点半
      if (hour > 9 || (hour === 9 && minute >= 30)) {
        return moment().format('YYYY-MM-DD');
      }
    }

    const lastdaliy = await this.ctx.service.quants.getLastDaliy();
    return moment(lastdaliy.date).format('YYYY-MM-DD');
  },

};

module.exports.tools = {
  // 密码“加盐”
  async saltPassword(password, salt = crypto.createHash('md5').update(Math.random().toString()).digest('hex')) {
    const password_finally = crypto
      .createHash('md5')
      .update(`${password}:${salt}`)
      .digest('hex');
    return {
      salt,
      password: password_finally,
    };
  },
};

module.exports.redisKeys = {
  // 资源基于action和url存储到redis中的key
  permissionsBaseActionUrl(action = '', url = '') {
    return `permissions:action:${action}:url:${url}`;
  },
  // 角色资源基于roleId存储到redis中的key
  rolePermissionsBaseRoleId(id = '') {
    return `rolePermissions:roleId:${id}`;
  },
  // 用户拥有的所有角色id，基于userId存储到redis中的key
  userRoleIdsBaseUserId(id = '') {
    return `userRoleIds:userId:${id}`;
  },
  // socket发送后基于ID存储到redis中的key
  socketBaseSocketId(id = '') {
    return `socket:Id:${id}`;
  },
};

module.exports.waitTime = n => new Promise(r => setTimeout(r, n));

