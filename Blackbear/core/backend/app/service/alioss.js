'use strict';

const { Service } = require('egg');
const path = require('path');
const md5 = require('md5');
const { CustomErrors } = require('../utils/unified-response-system');

/**
 * AliossService
 * Business logic service
 */
class AliossService extends Service {
  async uploadFiles(params) {
    const { ctx } = this;
    try {
      const size = ctx.request.header['content-length'];
      const stream = await ctx.getFileStream();
      const { fileSize } = ctx.app.config.multipart;
      const extension = path.extname(stream.filename);
      // 基础参数校验
      if (!stream.filename || !stream.mimeType || !size) {
        throw CustomErrors.ErrorFactory.badRequest('文件参数不完整');
      }
      if (size > fileSize) {
        throw CustomErrors.ErrorFactory.badRequest(`文件容量不可超过${fileSize / 1024 / 1024}Mb`);
      }
      if (/^image\/.*$/i.test(stream.mimeType) && size > 30 * 1024 * 1024) {
        throw CustomErrors.ErrorFactory.badRequest('图片容量不可超过30Mb');
      }
      if (/^video\/.*$/i.test(stream.mimeType) && size > 100 * 1024 * 1024) {
        throw CustomErrors.ErrorFactory.badRequest('媒体文件容量不可超过100Mb');
      }
      const filename = md5(stream.filename) + path.extname(stream.filename).toLocaleLowerCase();
      try {
        const {
          url,
          name,
          res: { status, statusMessage },
        } = await ctx.oss.putStream(`${ctx.app.config.ossBucketName}/${filename}`, stream);
        if (status === 200 && statusMessage === 'OK') {
          await ctx.service.material.create({
            category: params.category ? params.category : 'postimage',
            name: stream.filename,
            text: filename,
            filetype: stream.mimeType,
            size,
            extension,
          });
          return {
            url: `${ctx.app.config.ossHost}/${name}`,
            filename,
            path: url,
            filetype: stream.mimeType,
            size,
            extension,
          };
        }
        throw CustomErrors.ErrorFactory.internal('文件上传失败');
      } catch (err) {
        ctx.logger.error('OSS文件上传异常:', err);
        ctx.throw(500, err);
        throw err;
      }
    } catch (error) {
      ctx.logger.error('上传接口异常:', error);
      return { success: false, message: '文件上传失败', error: error.message };
    }
  }
}

module.exports = AliossService;
