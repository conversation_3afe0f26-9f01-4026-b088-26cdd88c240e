'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class ResearchsService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'research:minute:',
        description: '研报分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'research:hourly:',
        description: '研报小时级数据',
        strategy: 'conservative',
      },
      daily: {
        ttl: 1800,
        prefix: 'research:daily:',
        description: '研报日级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'research:statistics:',
        description: '研报统计数据',
        strategy: 'conservative',
      },
      search: {
        ttl: 90,
        prefix: 'research:search:',
        description: '研报搜索数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'research:detail:',
        description: '研报详情数据',
        strategy: 'balanced',
      },
    };
  }

  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      return await this.ctx.model.Research.findOne({ where: { id } });
    });
  }

  async findAllResearch(params) {
    return await this.withCache('minute', 'findAllResearch', params, async () => {
      const { pageIndex, pageSize, tag, type, unread, important, star } = params;
      const { Stock, Source } = this.ctx.model;
      const where = {};
      if (unread === 'true') { where.read = false; }
      if (important === 'true') { where.important = true; }
      if (star === 'true') { where.star = true; }
      if (tag) { where.title = { [Op.like]: `%${tag}%` }; }
      if (type && type.length > 0) { where.type = type; }
      return await this.ctx.model.Research.findAndCountAll({
        order: [[ 'read', 'ASC' ], [ 'publishdate', 'DESC' ]],
        where,
        include: [{
          model: Stock,
          attributes: [ 'code', 'score' ],
          include: [{
            model: Source,
            attributes: [ 'code', 'displayName', 'f3' ],
          }],
        }],
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  async getRandom(num, ex) {
    return await this.withCache('minute', 'getRandom', { num, ex }, async () => {
      return await this.ctx.model.Research.findAll({
        where: {
          read: false,
          star: true,
          id: { [Op.notIn]: ex.split(',') },
          type: { [Op.in]: [ 'industry', 'stock' ] },
        },
        order: [[ 'publishTimeStm', 'ASC' ]],
        include: [{
          model: this.ctx.model.Stock,
          attributes: [ 'code' ],
          include: [{
            model: this.ctx.model.Source,
            attributes: [ 'code', 'displayName' ],
          }],
        }],
        limit: num,
      });
    });
  }

  async get_count() {
    return await this.withCache('statistics', 'get_count', {}, async () => {
      return await this.ctx.model.Research.count({
        where: {
          read: false,
          star: true,
          type: { [Op.in]: [ 'industry', 'stock' ] },
        },
      });
    });
  }

  async update(id, params) {
    await this.ctx.model.Research.update({ ...params }, { where: { id } });
    await this.clearRelatedCache('research', { method: 'update', id });
    return true;
  }

  async read(id, params) {
    await this.ctx.model.Research.update({ read: true }, { where: { id } });
    await this.clearRelatedCache('research', { method: 'read', id });
    return true;
  }

  async allread() {
    await this.ctx.model.Research.update({ read: true }, { where: { read: false } });
    await this.clearRelatedCache('research', { method: 'allread' });
    return true;
  }

  async deleteTag(id, params) {
    const researchtag = await this.ctx.model.ResearchTag.destroy({ where: { researchId: id, tagId: params.tagId } });
    await this.clearRelatedCache('research', { method: 'deleteTag', id });
    return researchtag;
  }

  async createTag(id, params) {
    const researchtag = await this.ctx.model.ResearchTag.findOrCreate({ where: { researchId: id, tagId: params.tagId } });
    await this.clearRelatedCache('research', { method: 'createTag', id });
    return researchtag;
  }
}

module.exports = ResearchsService;
