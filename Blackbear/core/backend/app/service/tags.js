'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const pinyin = require('pinyin');
const moment = require('moment');

class TagsService extends BaseCacheService {
  // 统一分层缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      tagsRealtime: {
        ttl: 30,
        prefix: 'tags:realtime:',
        description: '标签实时数据',
        strategy: 'aggressive',
      },
      tagsMinute: {
        ttl: 60,
        prefix: 'tags:minute:',
        description: '标签分钟数据',
        strategy: 'balanced',
      },
      tagsHourly: {
        ttl: 300,
        prefix: 'tags:hourly:',
        description: '标签小时数据',
        strategy: 'balanced',
      },
      tagsDaily: {
        ttl: 1800,
        prefix: 'tags:daily:',
        description: '标签日级数据',
        strategy: 'conservative',
      },
      tagsStatistics: {
        ttl: 600,
        prefix: 'tags:statistics:',
        description: '标签统计数据',
        strategy: 'conservative',
      },
      tagsRanking: {
        ttl: 120,
        prefix: 'tags:ranking:',
        description: '标签排行榜数据',
        strategy: 'balanced',
      },
      tagsSearch: {
        ttl: 90,
        prefix: 'tags:search:',
        description: '标签搜索数据',
        strategy: 'balanced',
      },
      tagsDetail: {
        ttl: 180,
        prefix: 'tags:detail:',
        description: '标签详情数据',
        strategy: 'conservative',
      },
    };
  }

  // ========== 业务方法全部用withCache包装 ==========

  async show(id) {
    return this.withCache('tagsDetail', 'show', { id }, async () => {
      const tag = await this.ctx.model.Tag.findOne({
        where: { id },
        include: [
          { model: this.ctx.model.Subtag },
          { model: this.ctx.model.TagStock, include: [{ model: this.ctx.model.Stock, include: [{ model: this.ctx.model.Source }] }] },
          { model: this.ctx.model.TagUser, include: [{ model: this.ctx.model.User }] },
          { model: this.ctx.model.ResearchTag, include: [{ model: this.ctx.model.Research }] },
          { model: this.ctx.model.EventTag, limit: 10, include: [{ model: this.ctx.model.Event, where: { frequency: 'day', category: 'BREAKING' }, include: [{ model: this.ctx.model.CalendarEvent, include: [{ model: this.ctx.model.Calendar }] }] }] },
          { model: this.ctx.model.PostTag, limit: 10, include: [{ order: [[ 'id', 'ASC' ]], where: { status: 'published', category: { [Op.in]: [ 'meetingminutes', 'weekly', 'original', 'report', 'collection' ] } }, model: this.ctx.model.Post }] },
        ],
        distinct: true,
      });
      return tag;
    });
  }

  async getAllTagsName() {
    return this.withCache('tagsStatistics', 'getAllTagsName', {}, async () => {
      const tags = await this.ctx.model.Tag.findAndCountAll();
      return tags;
    });
  }

  async mytags(params) {
    return this.withCache('tagsDetail', 'mytags', params, async () => {
      const { userId } = params;
      const tags = await this.ctx.model.TagUser.findAll({
        where: { userId },
        include: [{ model: this.ctx.model.Tag }],
      });
      return tags;
    });
  }

  async findAllTags(params) {
    return this.withCache('tagsSearch', 'findAllTags', params, async () => {
      const {
        Stock,
        Tag,
        TagStock,
        Source,
        EventTag,
        Event,
        CalendarEvent,
        Calendar,
        Subtag,
      } = this.ctx.model;
      const {
        pageIndex, pageSize, text, type, topic, isTop,
      } = params;
      let where = {};
      let include = [];
      let having = {};
      if (text) {
        where = Object.assign(where, {
          [Op.or]: [
            { code: { [Op.like]: `%${text}%` } },
            { name: { [Op.like]: `%${text}%` } },
          ],
        });
      }
      if (topic) {
        where = Object.assign(where, { isTopic: topic });
      }
      if (isTop) {
        where = Object.assign(where, { isTop });
      }
      if (type === 'ranking' || type === 'hot') {
        include = [{
          model: Tag,
          attributes: { exclude: [ 'desc' ] },
          require: true,
          where: { ...where, id: { [Op.gt]: 0 } },
          attributes: [ 'id', 'name', 'isTopic', 'isTop', 'parentName', 'parentId' ],
        }, {
          model: Stock,
          attributes: [],
          include: { model: Source, attributes: [ 'f3', 'code' ] },
        }];
        if (type === 'hot') {
          having = Object.assign(having, { total: { [Op.gt]: 2 }, avg: { [Op.gt]: 3 } });
        }
        const tags = await TagStock.findAll({
          attributes: [
            [ Sequelize.fn('AVG', Sequelize.col('f3')), 'avg' ],
            'tagId',
            Sequelize.col('f3'),
            [ Sequelize.fn('COUNT', Sequelize.col('displayName')), 'total' ]],
          order: [[ Sequelize.literal('avg DESC') ]],
          distinct: true,
          having,
          group: 'tagId',
          include,
          offset: parseInt(pageIndex - 1) * parseInt(pageSize),
          limit: parseInt(pageSize),
        });
        return tags;
      }
      const tags = await Tag.findAndCountAll({
        where,
        order: [[ 'isTop', 'DESC' ]],
        include: [{
          model: TagStock,
          include: {
            model: Stock,
            attributes: [ 'code', 'rate', 'keyword', 'ret20' ],
            include: { model: Source, attributes: [ 'code', 'displayName', 'f3' ] },
          },
        }, { model: this.ctx.model.Subtag }, { model: this.ctx.model.TagUser, include: [{ model: this.ctx.model.User }] }],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
      return tags;
    });
  }

  async getAll(num) {
    return this.withCache('tagsStatistics', 'getAll', { num }, async () => {
      let query = { order: [[ 'id', 'DESC' ]], attributes: [ 'id', 'updatedAt' ] };
      if (num) {
        query = Object.assign(query, { offset: 0, limit: num });
      }
      const tags = await this.ctx.model.Tag.findAll(query);
      return tags;
    });
  }

  async getTopTopic() {
    return this.withCache('tagsRanking', 'getTopTopic', {}, async () => {
      const topic = await this.ctx.model.Tag.findAll({
        where: { isTopic: true, isTop: true },
        include: [{ model: this.ctx.model.EventTag, limit: 1, order: [[ 'id', 'DESC' ]], include: [{ model: this.ctx.model.Event }] }],
      });
      return topic;
    });
  }

  async getTopicById(id) {
    return this.withCache('tagsDetail', 'getTopicById', { id }, async () => {
      const { Tag, TagStock, Stock, Source, EventTag, Event, CalendarEvent, Calendar } = this.ctx.model;
      const topic = await this.ctx.service.tags.show(id);
      const list = await Tag.findAll({
        where: { parentId: id },
        order: [[ 'sortId', 'ASC' ]],
        include: [{
          model: TagStock,
          include: [{ model: Stock, include: [{ model: Source }] }],
        }, {
          model: EventTag,
          limit: 10,
          include: [{ model: Event, where: { frequency: 'day', category: 'BREAKING' }, include: [{ model: CalendarEvent, include: [{ model: Calendar }] }] }],
        }],
        distinct: true,
      });
      return { topic, list };
    });
  }

  async getAllTopic() {
    return this.withCache('tagsStatistics', 'getAllTopic', {}, async () => {
      const { Tag } = this.ctx.model;
      const dict = {};
      const parentTag = await Tag.findAll({ where: { isTopic: 1 } });
      const unassignedTag = await Tag.findAll({ where: { [Op.or]: [{ isTopic: 0 }, { isTopic: { [Op.eq]: null } }], parentId: { [Op.eq]: null } } });
      dict.unassignedTag = { id: 'unassignedTag', list: unassignedTag };
      for (const element of parentTag) {
        const tags = await Tag.findAll({ where: { [Op.or]: [{ isTopic: 0 }, { isTopic: { [Op.eq]: null } }], parentId: element.get('id') }, order: [[ 'sortId', 'ASC' ]] });
        const obj = { id: element.get('id'), name: element.get('name'), list: tags };
        dict[element.get('id')] = obj;
      }
      return dict;
    });
  }


  // 创建标签 - 清除相关缓存
  async create(name) {
    return await this.withCache('tagsDetail', 'create', { name }, async () => {
      const tag = await this.ctx.model.Tag.findOrCreate({
        where: {
          name,
          code: pinyin(name, {
            style: pinyin.STYLE_FIRST_LETTER, // 设置拼音风格
          }).join('').toLowerCase(),
        },
      });

      return tag;
    });
  }


  // 锁定标签 - 清除相关缓存
  async lock(id, params) {
    return await this.withCache('tagsDetail', 'lock', { id, params }, async () => {
      let res = false;
      await this.ctx.model.transaction(async transaction => {
        const tag = await this.ctx.model.Tag.findOne({
          where: { id },
        }, { transaction });

        const lockTime = moment().add(10, 'minute').format('YYYY-MM-DD HH:mm:ss');
        if (!tag.lockUser || tag.lockUser === this.ctx.state.user.id) {
          await tag.update({
            lockUser: this.ctx.state.user.id,
            lockTime,
          }, { transaction });
          res = true;
        }
      });
      return res;
    });
  }

  // 解锁标签 - 清除相关缓存
  async unlock(id, params) {
    return await this.withCache('tagsDetail', 'unlock', { id, params }, async () => {
      let res = false;
      await this.ctx.model.transaction(async transaction => {
        const tag = await this.ctx.model.Tag.findOne({
          where: { id },
        }, { transaction });

        if (tag.lockUser === this.ctx.state.user.id) {
          await tag.update({
            lockUser: null,
            lockTime: null,
          }, { transaction });
          res = true;
        }
      });
      return res;
    });
  }

  // 使用JSON更新标签 - 清除相关缓存
  async updateWithJson(id, params) {
    return await this.withCache('tagsDetail', 'updateWithJson', { id, params }, async () => {
      const tag = await this.ctx.model.Tag.findByPk(id);
      const parsedData = JSON.parse(params.json);

      try {
        // 先删除所有题材关联个股，再重新添加
        await this.ctx.model.transaction(async transaction => {
          await this.ctx.model.TagStock.destroy({
            where: { tagId: id },
          }, { transaction });

          if (parsedData.desc) {
            await tag.update({
              desc: parsedData.desc,
            }, { transaction });
          }

          for (const element of parsedData.data) {
            await this.ctx.model.TagStock.create({
              tagId: id,
              code: element.code,
              rate: parseFloat(element.rate, 10),
              reason: element.reason,
            }, { transaction });
          }
        });
      } catch (error) {
        this.ctx.logger.error('JSON更新标签失败:', error);
        throw error;
      }

      return tag;
    });
  }

  // 更新标签 - 清除相关缓存
  async update(id, params) {
    return await this.withCache('tagsDetail', 'update', { id, params }, async () => {
      await this.ctx.model.transaction(async transaction => {
        const tag = await this.ctx.model.Tag.findOne({
          where: { id },
          lock: Sequelize.Transaction.LOCK.SHARE,
        }, { transaction });

        await tag.update({
          ...params,
          code: pinyin(params.name || tag.name, {
            style: pinyin.STYLE_FIRST_LETTER, // 设置拼音风格
          }).join('').toLowerCase(),
        }, { transaction });

        return tag;
      });
    });
  }

  // 删除标签股票关联 - 清除相关缓存
  async deleteStock(id, params) {
    return await this.withCache('tagsDetail', 'deleteStock', { id, params }, async () => {
      const tagstock = await this.ctx.model.TagStock.destroy({
        where: {
          tagId: id,
          code: params.code,
        },
      });
      return tagstock;
    });
  }

  // 更新标签股票原因 - 清除相关缓存
  async tagReason(id, params) {
    return await this.withCache('tagsDetail', 'tagReason', { id, params }, async () => {
      const tagstock = await this.ctx.model.TagStock.update({
        reason: params.reason,
        rate: params.rate,
      }, {
        where: {
          tagId: id,
          code: params.code,
        },
      });
      return tagstock;
    });
  }

  // 创建标签股票关联 - 清除相关缓存
  async createStock(id, params) {
    return await this.withCache('tagsDetail', 'createStock', { id, params }, async () => {
      const where = {
        tagId: id,
        code: params.code,
      };

      if (params.subTagId) {
        where.subTagId = params.subTagId;
      }

      const tagstock = await this.ctx.model.TagStock.findOrCreate({
        where,
      });

      return tagstock;
    });
  }

  // 添加标签用户关联 - 清除相关缓存
  async addUser(id, params) {
    return await this.withCache('tagsDetail', 'addUser', { id, params }, async () => {
      const taguser = await this.ctx.model.TagUser.findOrCreate({
        where: {
          tagId: id,
          userId: params.userId,
        },
      });

      return taguser;
    });
  }

  // 删除标签用户关联 - 清除相关缓存
  async removeUser(id, params) {
    return await this.withCache('tagsDetail', 'removeUser', { id, params }, async () => {
      const taguser = await this.ctx.model.TagUser.destroy({
        where: {
          tagId: id,
          userId: params.userId,
        },
      });

      return taguser;
    });
  }

  // 添加子标签 - 清除相关缓存
  async addSubtag(id, params) {
    return await this.withCache('tagsDetail', 'addSubtag', { id, params }, async () => {
      let subtag = null;
      await this.ctx.model.transaction(async transaction => {
        subtag = await this.ctx.model.Subtag.findByPk(params.subTagId, { transaction });

        if (subtag) {
          subtag = await subtag.update({
            name: params.name,
          }, { transaction });
        } else {
          subtag = await this.ctx.model.Subtag.create({
            parentId: id,
            name: params.name,
          }, { transaction });
        }

        const codes = params.codes.split(',');

        for (const element of codes) {
          await this.ctx.model.TagStock.update({
            subTagId: subtag.dataValues.id,
          }, {
            where: {
              tagId: id,
              code: element,
            },
          }, { transaction });
        }
      });

      return subtag;
    });
  }

  // 删除子标签 - 清除相关缓存
  async removeSubtag(id, params) {
    return await this.withCache('tagsDetail', 'removeSubtag', { id, params }, async () => {
      await this.ctx.model.transaction(async transaction => {
        await this.ctx.model.Subtag.destroy({
          where: {
            parentId: id,
            id: params.id,
          },
        }, { transaction });

        await this.ctx.model.TagStock.destroy({
          where: {
            subTagId: params.id,
          },
        }, { transaction });
      });

      return {};
    });
  }


  // 删除标签 - 清除相关缓存
  async destroy(id) {
    return await this.withCache('tagsDetail', 'destroy', { id }, async () => {
      await this.ctx.model.transaction(async transaction => {
        await this.ctx.model.Tag.update({
          parentId: null,
          sortId: null,
        }, {
          where: { parentId: id },
        }, { transaction });

        await this.ctx.model.Tag.destroy({
          where: { id },
        }, { transaction });

        await this.ctx.model.TagStock.destroy({
          where: { tagId: id },
        }, { transaction });

        await this.ctx.model.ResearchTag.destroy({
          where: { tagId: id },
        }, { transaction });

        await this.ctx.model.EventTag.destroy({
          where: { tagId: id },
        }, { transaction });

        await this.ctx.model.PostTag.destroy({
          where: { tagId: id },
        }, { transaction });
      });

      return {};
    });
  }


  // 专题变更 - 清除相关缓存
  async topicChange(params) {
    return await this.withCache('tagsDetail', 'topicChange', { params }, async () => {
      const { parentId, list } = params;
      const { Tag, TagStock } = this.ctx.model;

      await this.ctx.model.transaction(async transaction => {
        const parentTag = await Tag.findOne({
          where: { id: parentId },
        }, { transaction });

        // 去除专题关联
        await Tag.update({
          parentId: null,
          parentName: null,
          sortId: null,
        }, {
          where: { parentId },
        }, { transaction });

        await TagStock.destroy({
          force: true,
          where: { tagId: parentId },
        }, { transaction });

        // 重建关联
        for (let index = 0; index < list.length; index++) {
          const element = list[index];

          const stocks = await TagStock.findAll({
            where: { tagId: element.id },
          }, { transaction });

          for (let j = 0; j < stocks.length; j++) {
            const stock = stocks[j];
            const tagStock = await TagStock.findOrCreate({
              where: {
                tagId: parentId,
                code: stock.get('code'),
              },
              transaction,
            });

            await TagStock.update({
              reason: stock.get('reason'),
            }, {
              where: {
                tagId: parentId,
                code: stock.get('code'),
              },
            }, { transaction });
          }

          await Tag.update({
            parentId,
            parentName: parentTag.name,
            sortId: index,
          }, {
            where: { id: element.id },
          }, { transaction });
        }
      });

      return true;
    });
  }
}

module.exports = TagsService;
