'use strict';

const { Service } = require('egg');

/**
 * QueryMonitorService
 * Business logic service
 */
class QueryMonitorService extends Service {

  // 查询统计存储
  constructor(ctx) {
    super(ctx);
    this.queryStats = new Map();
    this.slowQueries = [];
    this.maxSlowQueries = 100; // 最多保存100条慢查询记录
  }

  // 记录查询
  recordQuery(sql, params, executionTime, success = true) {
    const queryKey = this.generateQueryKey(sql, params);

    if (!this.queryStats.has(queryKey)) {
      this.queryStats.set(queryKey, {
        sql,
        params,
        count: 0,
        totalTime: 0,
        avgTime: 0,
        minTime: Infinity,
        maxTime: 0,
        successCount: 0,
        errorCount: 0,
        lastExecuted: null,
      });
    }

    const stats = this.queryStats.get(queryKey);
    stats.count++;
    stats.totalTime += executionTime;
    stats.avgTime = stats.totalTime / stats.count;
    stats.minTime = Math.min(stats.minTime, executionTime);
    stats.maxTime = Math.max(stats.maxTime, executionTime);
    stats.lastExecuted = new Date();

    if (success) {
      stats.successCount++;
    } else {
      stats.errorCount++;
    }

    // 记录慢查询
    if (executionTime > 1000) { // 超过1秒的查询
      this.recordSlowQuery(sql, params, executionTime, success);
    }

    // 限制统计数据大小
    if (this.queryStats.size > 1000) {
      this.cleanupStats();
    }
  }

  // 生成查询键
  generateQueryKey(sql, params) {
    // 简单的查询键生成，实际项目中可能需要更复杂的逻辑
    return `${sql}:${JSON.stringify(params)}`;
  }

  // 记录慢查询
  recordSlowQuery(sql, params, executionTime, success) {
    const slowQuery = {
      sql,
      params,
      executionTime,
      success,
      timestamp: new Date(),
      stack: new Error().stack,
    };

    this.slowQueries.push(slowQuery);

    // 限制慢查询记录数量
    if (this.slowQueries.length > this.maxSlowQueries) {
      this.slowQueries.shift();
    }
  }

  // 清理统计数据
  cleanupStats() {
    const entries = Array.from(this.queryStats.entries());
    // 按执行次数排序，保留执行次数最多的500个查询
    entries.sort((a, b) => b[1].count - a[1].count);
    this.queryStats.clear();

    entries.slice(0, 500).forEach(([ key, value ]) => {
      this.queryStats.set(key, value);
    });
  }

  // 获取查询统计
  getQueryStats() {
    const stats = Array.from(this.queryStats.values());

    // 按平均执行时间排序
    stats.sort((a, b) => b.avgTime - a.avgTime);

    return {
      totalQueries: stats.reduce((sum, stat) => sum + stat.count, 0),
      totalTime: stats.reduce((sum, stat) => sum + stat.totalTime, 0),
      avgTime: stats.reduce((sum, stat) => sum + stat.avgTime, 0) / stats.length,
      slowestQueries: stats.slice(0, 10),
      mostFrequentQueries: stats.sort((a, b) => b.count - a.count).slice(0, 10),
      errorQueries: stats.filter(stat => stat.errorCount > 0).slice(0, 10),
    };
  }

  // 获取慢查询记录
  getSlowQueries(limit = 20) {
    return this.slowQueries
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  // 获取性能报告
  getPerformanceReport() {
    const stats = this.getQueryStats();
    const slowQueries = this.getSlowQueries();

    return {
      summary: {
        totalQueries: stats.totalQueries,
        totalTime: `${Math.round(stats.totalTime)}ms`,
        avgTime: `${Math.round(stats.avgTime)}ms`,
        slowQueryCount: slowQueries.length,
        uniqueQueries: this.queryStats.size,
      },
      topSlowQueries: stats.slowestQueries.map(stat => ({
        sql: `${stat.sql.substring(0, 100)}...`,
        avgTime: `${Math.round(stat.avgTime)}ms`,
        count: stat.count,
        successRate: `${Math.round((stat.successCount / stat.count) * 100)}%`,
      })),
      topFrequentQueries: stats.mostFrequentQueries.map(stat => ({
        sql: `${stat.sql.substring(0, 100)}...`,
        count: stat.count,
        avgTime: `${Math.round(stat.avgTime)}ms`,
        totalTime: `${Math.round(stat.totalTime)}ms`,
      })),
      recentSlowQueries: slowQueries.map(query => ({
        sql: `${query.sql.substring(0, 100)}...`,
        executionTime: `${Math.round(query.executionTime)}ms`,
        success: query.success,
        timestamp: query.timestamp,
      })),
    };
  }

  // 重置统计数据
  resetStats() {
    this.queryStats.clear();
    this.slowQueries.length = 0;
  }

  // 导出统计数据
  exportStats() {
    return {
      queryStats: Array.from(this.queryStats.entries()),
      slowQueries: this.slowQueries,
      exportTime: new Date(),
    };
  }

  // 监控数据库连接池状态
  async getConnectionPoolStatus() {
    const sequelize = this.app.model;
    const { pool } = sequelize.connectionManager;

    if (!pool) {
      return { error: 'Connection pool not available' };
    }

    return {
      total: pool.size,
      idle: pool.idle,
      waiting: pool.waiting,
      using: pool.using,
      pending: pool.pending,
    };
  }

  // 监控Redis状态
  async getRedisStatus() {
    try {
      const { redis } = this.app;
      const info = await redis.info();

      // 解析Redis INFO命令的输出
      const lines = info.split('\r\n');
      const status = {};

      lines.forEach(line => {
        const [ key, value ] = line.split(':');
        if (key && value) {
          status[key] = value;
        }
      });

      return {
        connected_clients: status.connected_clients,
        used_memory_human: status.used_memory_human,
        used_memory_peak_human: status.used_memory_peak_human,
        keyspace_hits: status.keyspace_hits,
        keyspace_misses: status.keyspace_misses,
        hit_rate: status.keyspace_hits && status.keyspace_misses
          ? `${Math.round((parseInt(status.keyspace_hits) / (parseInt(status.keyspace_hits) + parseInt(status.keyspace_misses))) * 100)}%`
          : 'N/A',
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  // 获取系统资源使用情况
  getSystemResources() {
    const usage = process.memoryUsage();

    return {
      memory: {
        rss: `${Math.round(usage.rss / 1024 / 1024 * 100) / 100}MB`,
        heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100}MB`,
        heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100}MB`,
        external: `${Math.round(usage.external / 1024 / 1024 * 100) / 100}MB`,
      },
      cpu: process.cpuUsage(),
      uptime: `${Math.round(process.uptime())}s`,
      pid: process.pid,
    };
  }

  // 健康检查
  async healthCheck() {
    const [ dbStatus, redisStatus, systemResources ] = await Promise.all([
      this.getConnectionPoolStatus(),
      this.getRedisStatus(),
      Promise.resolve(this.getSystemResources()),
    ]);

    const performanceReport = this.getPerformanceReport();

    return {
      timestamp: new Date(),
      database: dbStatus,
      redis: redisStatus,
      system: systemResources,
      performance: performanceReport.summary,
      status: 'healthy', // 可以根据实际情况判断
    };
  }
}

module.exports = QueryMonitorService;
