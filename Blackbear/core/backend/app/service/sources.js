'use strict';

const BaseCacheService = require('./base/cache');
const { Op } = require('sequelize');

class SourcesService extends BaseCacheService {

  // 自定义缓存配置 - 针对股票数据服务的实时性要求
  get cacheConfig() {
    return {
      // 继承基础配置
      ...super.cacheConfig,

      // 股票基础数据 - 需要快速响应
      sourceBaseData: {
        ttl: 30, // 30秒，保证实时性
        prefix: 'sources:base:',
        description: '股票基础数据缓存',
        strategy: 'aggressive', // 激进策略：频繁更新
      },

      // 股票搜索数据 - 中等实时性
      sourceSearch: {
        ttl: 20, // 20秒
        prefix: 'sources:search:',
        description: '股票搜索数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },

      // 股票排行榜数据 - 中等实时性
      sourceRanking: {
        ttl: 45, // 45秒
        prefix: 'sources:ranking:',
        description: '股票排行榜数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },

      // 股票统计数据 - 较低实时性
      sourceStatistics: {
        ttl: 60, // 1分钟
        prefix: 'sources:stats:',
        description: '股票统计数据缓存',
        strategy: 'conservative', // 保守策略：较少更新
      },

      // 科创板数据 - 中等实时性
      sourceKechuang: {
        ttl: 40, // 40秒
        prefix: 'sources:kechuang:',
        description: '科创板数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },

      // 涨停板数据 - 中等实时性
      sourceDailylimit: {
        ttl: 35, // 35秒
        prefix: 'sources:dailylimit:',
        description: '涨停板数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },

      // 股票分析数据 - 较长缓存时间
      sourceAnalysis: {
        ttl: 120, // 2分钟
        prefix: 'sources:analysis:',
        description: '股票分析数据缓存',
        strategy: 'conservative', // 保守策略：较少更新
      },

      // 股票筛选数据 - 中等缓存时间
      sourceFilter: {
        ttl: 90, // 1.5分钟
        prefix: 'sources:filter:',
        description: '股票筛选数据缓存',
        strategy: 'conservative', // 保守策略：较少更新
      },
    };
  }

  // 优化后的find方法
  async find(params) {
    return await this.withCache('sourceSearch', 'find', params, async () => {
      return await this.fetchSourceData(params);
    });
  }

  // 实际的数据获取逻辑
  async fetchSourceData(params) {
    const { ctx } = this;
    const {
      Stock,
      Source,
      // Sort,
      // SortSec,
      // Tag,
      TagStock,
    } = this.ctx.model;
    const {
      pageIndex, // 第几页
      pageSize, // 页数
      sortField, // 排序类型
      sortOrder, // 排序字段
      text, //  检索
      tag, //  题材
      plate, // 板块
    } = params;

    let order = [[ 'code', 'ASC' ]];

    // 关联
    const include = [
      {
        model: Stock,
        required: true,
        attributes: [ 'code' ],
      },
    ];
    let where = {};

    // 排序
    if (sortField && sortOrder) {
      order = [[ sortField, sortOrder ]];
    }

    const plateId = ctx.helper.toInt(plate);
    if (plateId) {
      if (plateId === 1) {
        where = Object.assign(where, {
          code: {
            [Op.regexp]: '^60',
          },
        });
      } else if (plateId === 2) {
        where = Object.assign(where, {
          code: {
            [Op.regexp]: '^688',
          },
        });
      } else if (plateId === 3) {
        where = Object.assign(where, {
          code: {
            [Op.regexp]: '^000',
          },
        });
      } else if (plateId === 4) {
        where = Object.assign(where, {
          code: {
            [Op.regexp]: '^002|003',
          },
        });
      } else if (plateId === 5) {
        where = Object.assign(where, {
          code: {
            [Op.regexp]: '^3',
          },
        });
      } else if (plateId === 6) {
        where = Object.assign(where, {
          code: {
            [Op.regexp]: '^8',
          },
        });
      }
    }

    // 标签筛选
    if (tag) {
      const list = await TagStock.findAll({
        where: {
          tagId: tag,
        },
      });

      const codeList = [];

      list.forEach(item => {
        codeList.push(item.get('code'));
      });

      where = Object.assign(where, {
        code: {
          [Op.regexp]: codeList.join('|'),
        },
      });
    }

    // 快速检索
    if (text) {
      Object.assign(where, {
        [Op.or]: [{
          code: {
            [Op.like]: `%${text}%`,
          } },
        { name: {
          [Op.like]: `%${text}%`,
        } },
        { displayName: {
          [Op.like]: `%${text}%`,
        } },
        { f103: {
          [Op.like]: `%${text}%`,
        } },
        ],
      });
    }

    const sources = await Source.findAndCountAll({
      distinct: true,
      attributes: [ 'name', 'code', 'displayName', 'f12', 'f14', 'f100', 'f3', 'f20', 'f6', 'zzb_one', 'f127', 'f149', 'f24', 'f25', 'f9', 'f23' ],
      where: {
        ...where,
      },
      order,
      include,
      offset: parseInt(pageIndex - 1) * parseInt(pageSize),
      limit: parseInt(pageSize),
    });

    return sources;
  }

  // 优化后的净买入排行榜
  async stockNetBuy() {
    return await this.withCache('sourceRanking', 'stockNetBuy', {}, async () => {
      return await this.fetchNetBuyData();
    });
  }

  async fetchNetBuyData() {
    const {
      Source,
      Stock,
    } = this.ctx.model;

    const list = await Source.findAll({
      attributes: [ 'displayName', 'code', 'f3', 'f62' ],
      where: {
        f62: {
          [Op.gt]: 0,
        },
      },
      include: [{
        model: Stock,
        attributes: [ 'code', 'hot', 'remarks', 'white', 'rate' ],
      }],
      order: [[ 'f62', 'DESC' ]],
      limit: 20,
    });

    return list;
  }

  // 优化后的净卖出排行榜
  async stockNetSale() {
    return await this.withCache('sourceRanking', 'stockNetSale', {}, async () => {
      return await this.fetchNetSaleData();
    });
  }

  async fetchNetSaleData() {
    const {
      Source,
      Stock,
    } = this.ctx.model;

    const list = await Source.findAll({
      attributes: [ 'displayName', 'code', 'f3', 'f62' ],
      where: {
        f62: {
          [Op.lt]: 0,
        },
      },
      include: [{
        model: Stock,
        attributes: [ 'code', 'hot', 'remarks', 'rate' ],
      }],
      order: [[ 'f62', 'ASC' ]],
      limit: 20,
    });

    return list;
  }

  // 优化后的北向买入排行榜
  async stockNorthBuy() {
    return await this.withCache('sourceRanking', 'stockNorthBuy', {}, async () => {
      return await this.fetchNorthBuyData();
    });
  }

  async fetchNorthBuyData() {
    const {
      Source,
      Stock,
    } = this.ctx.model;

    const list = await Source.findAll({
      attributes: [ 'displayName', 'code', 'f3', 'sharesz_chg_one' ],
      where: {
        sharesz_chg_one: {
          [Op.gt]: 0,
        },
      },
      include: [{
        model: Stock,
        attributes: [ 'code', 'hot', 'remarks', 'rate' ],
      }],
      order: [[ 'sharesz_chg_one', 'DESC' ]],
      limit: 20,
    });

    return list;
  }

  // 优化后的北向卖出排行榜
  async stockNorthSalePer() {
    return await this.withCache('sourceRanking', 'stockNorthSalePer', {}, async () => {
      return await this.fetchNorthSalePerData();
    });
  }

  async fetchNorthSalePerData() {
    const {
      Source,
      Stock,
    } = this.ctx.model;

    const list = await Source.findAll({
      attributes: [ 'displayName', 'code', 'f3', 'zzb_one', 'sharesz_chg_one' ],
      where: {
        zzb_one: {
          [Op.lt]: 0,
        },
      },
      include: [{
        model: Stock,
        attributes: [ 'code', 'hot', 'remarks', 'white', 'rate' ],
      }],
      order: [[ 'zzb_one', 'ASC' ]],
      limit: 20,
    });

    return list;
  }

  // 优化后的北向买入排行榜
  async stockNorthBuyPer() {
    return await this.withCache('sourceRanking', 'stockNorthBuyPer', {}, async () => {
      return await this.fetchNorthBuyPerData();
    });
  }

  async fetchNorthBuyPerData() {
    const {
      Source,
      Stock,
    } = this.ctx.model;

    const list = await Source.findAll({
      attributes: [ 'displayName', 'code', 'f3', 'zzb_one', 'sharesz_chg_one' ],
      where: {
        zzb_one: {
          [Op.gt]: 0,
        },
      },
      include: [{
        model: Stock,
        attributes: [ 'code', 'hot', 'remarks', 'white', 'rate' ],
      }],
      order: [[ 'zzb_one', 'DESC' ]],
      limit: 20,
    });

    return list;
  }

  // 优化后的北向卖出排行榜
  async stockNorthSale() {
    return await this.withCache('sourceRanking', 'stockNorthSale', {}, async () => {
      return await this.fetchNorthSaleData();
    });
  }

  async fetchNorthSaleData() {
    const {
      Source,
      Stock,
    } = this.ctx.model;

    const list = await Source.findAll({
      attributes: [ 'displayName', 'code', 'f3', 'sharesz_chg_one' ],
      where: {
        sharesz_chg_one: {
          [Op.lt]: 0,
        },
      },
      include: [{
        model: Stock,
        attributes: [ 'hot', 'remarks', 'code', 'rate' ],
      }],
      order: [[ 'sharesz_chg_one', 'ASC' ]],
      limit: 20,
    });

    return list;
  }

  // 优化后的科创板数据
  async stockKechuang(params) {
    return await this.withCache('sourceKechuang', 'stockKechuang', params, async () => {
      return await this.fetchKechuangData(params);
    });
  }

  async fetchKechuangData(params) {
    const {
      Source,
      Stock,
      TagStock,
      Tag,
    } = this.ctx.model;

    const {
      pageIndex, // 第几页
      pageSize, // 页数
    } = params;

    const list = await Source.findAndCountAll({
      where: {
        code: {
          [Op.regexp]: '^68',
        },
      },
      // attributes: ['code', 'f14', 'f100', 'f103', 'f2', 'f3', 'f22', 'f6', 'f20', 'f127', 'f149'],
      order: [[ 'f3', 'DESC' ]],
      distinct: true,
      offset: parseInt(pageIndex - 1) * parseInt(pageSize),
      limit: parseInt(pageSize),
      include: [{
        model: Stock,
        include: [{
          model: TagStock,
          include: [{
            model: Tag,
          }],
        }],
      }],
    });

    return list;
  }

  // 优化后的涨停板数据
  async stockDailylimit() {
    return await this.withCache('sourceDailylimit', 'stockDailylimit', {}, async () => {
      return await this.fetchDailylimitData();
    });
  }

  async fetchDailylimitData() {
    const {
      Source,
      Stock,
      TagStock,
      Tag,
    } = this.ctx.model;

    const list = await Source.findAndCountAll({
      where: {
        f3: {
          [Op.gte]: 9.70,
        },
      },
      distinct: true,
      include: [{
        model: Stock,
        include: [{
          model: TagStock,
          include: [{
            model: Tag,
          }],
        }],
      }],
    });

    return list;
  }

  // 优化后的市值统计
  async statisticsByValue() {
    return await this.withCache('sourceStatistics', 'statisticsByValue', {}, async () => {
      return await this.fetchStatisticsByValueData();
    });
  }

  async fetchStatisticsByValueData() {
    const {
      Source,
    } = this.ctx.model;

    const [ v50l, v50g, v100l, v100g, v500l, v500g, v1kl, v1kg, v1kml, v1kmg ] = await Promise.all([
      Source.count({
        where: {
          f21: {
            [Op.lt]: 50 * 100000000,
          },
          f3: {
            [Op.lt]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.lt]: 50 * 100000000,
          },
          f3: {
            [Op.gte]: 0,
          },
        },
      }),

      Source.count({
        where: {
          [Op.and]: [
            {
              f21: {
                [Op.gte]: 50 * 100000000,
              },
            }, {
              f21: {
                [Op.lt]: 100 * 100000000,
              },
            },
          ],
          f3: {
            [Op.lt]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 50 * 100000000,
            [Op.lt]: 100 * 100000000,
          },
          f3: {
            [Op.gte]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 100 * 100000000,
            [Op.lt]: 500 * 100000000,
          },
          f3: {
            [Op.lt]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 100 * 100000000,
            [Op.lt]: 500 * 100000000,
          },
          f3: {
            [Op.gte]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 500 * 100000000,
            [Op.lt]: 1000 * 100000000,
          },
          f3: {
            [Op.lt]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 500 * 100000000,
            [Op.lt]: 1000 * 100000000,
          },
          f3: {
            [Op.gte]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 1000 * 100000000,
          },
          f3: {
            [Op.lt]: 0,
          },
        },
      }),

      Source.count({
        where: {
          f21: {
            [Op.gte]: 1000 * 100000000,
          },
          f3: {
            [Op.gte]: 0,
          },
        },
      }),
    ]);

    return {
      l: [ v50l, v100l, v500l, v1kl, v1kml ],
      g: [ v50g, v100g, v500g, v1kg, v1kmg ],
    };
  }

  // 优化后的统计方法
  async statistics() {
    return await this.withCache('sourceStatistics', 'statistics', {}, async () => {
      return await this.fetchStatisticsData();
    });
  }

  async fetchStatisticsData() {
    const {
      Source,
    } = this.ctx.model;
    const where = {
      displayName: {
        [Op.notRegexp]: '退市',
        // [Op.notLike]: '%ST%',
      },
      f2: {
        [Op.ne]: null,
      },
    };

    const [ total, less, more, equal, pctNe20, pctNe15, pctNe10, pctNe7, pctNe5, pctNe3, pctNe0, pctNePo,
      pctPo0, pctPo3, pctPo5, pctPo7, pctPo10, pctPo15, pctPo20 ] = await Promise.all([
      Source.count({
        where,
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.lt]: 0,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: 0,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.eq]: 0,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.lte]: -19,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: -19,
            [Op.lte]: -15,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: -15,
            [Op.lte]: -9,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: -9,
            [Op.lte]: -7,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: -7,
            [Op.lte]: -5,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: -5,
            [Op.lte]: -3,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: -3,
            [Op.lt]: 0,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.eq]: 0,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gt]: 0,
            [Op.lt]: 3,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gte]: 3,
            [Op.lt]: 5,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gte]: 5,
            [Op.lt]: 7,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gte]: 7,
            [Op.lt]: 9,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gte]: 9,
            [Op.lt]: 15,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gte]: 15,
            [Op.lt]: 19,
          },
        },
      }),
      Source.count({
        where: {
          ...where,
          f3: {
            [Op.gte]: 19,
          },
        },
      }),
    ]);

    return {
      total,
      less,
      more,
      equal,
      list: [
        pctNe20,
        pctNe15,
        pctNe10,
        pctNe7,
        pctNe5,
        pctNe3,
        pctNe0,
        pctNePo,
        pctPo0,
        pctPo3,
        pctPo5,
        pctPo7,
        pctPo10,
        pctPo15,
        pctPo20,
      ],
    };
  }

  // 高级缓存选项示例 - 强制刷新股票数据
  async refreshSourceData(method, params = {}, options = {}) {
    return await this.withCache('sourceBaseData', method, params, async () => {
      // 根据方法名调用对应的查询方法
      switch (method) {
        case 'find':
          return await this.find(params);
        case 'stockNetBuy':
          return await this.stockNetBuy();
        case 'stockNetSale':
          return await this.stockNetSale();
        case 'stockNorthBuy':
          return await this.stockNorthBuy();
        case 'stockNorthSale':
          return await this.stockNorthSale();
        case 'stockKechuang':
          return await this.stockKechuang(params);
        case 'stockDailylimit':
          return await this.stockDailylimit();
        case 'statistics':
          return await this.statistics();
        case 'statisticsByValue':
          return await this.statisticsByValue();
        default:
          throw new Error(`Unknown method: ${method}`);
      }
    }, {
      forceRefresh: options.forceRefresh || false,
      customTtl: options.customTtl || null,
    });
  }

  // 获取股票统计信息
  async getSourceStats() {
    return await this.withCache('sourceStatistics', 'getSourceStats', {}, async () => {
      const { Source, Stock } = this.ctx.model;

      const totalStocks = await Source.count();
      const totalHotStocks = await Stock.count({
        where: {
          hot: true,
        },
      });

      const todayStocks = await Source.count({
        where: {
          f2: {
            [Op.ne]: null,
          },
        },
      });

      return {
        totalStocks,
        totalHotStocks,
        todayStocks,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 获取股票趋势分析
  async getSourceTrends(days = 7) {
    return await this.withCache('sourceAnalysis', 'getSourceTrends', { days }, async () => {
      const { Source } = this.ctx.model;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const trends = await Source.findAll({
        where: {
          f2: {
            [Op.ne]: null,
          },
          f3: {
            [Op.ne]: null,
          },
        },
        attributes: [
          'code',
          'displayName',
          'f3', // 涨跌幅
          'f20', // 总市值
          'f62', // 净流入
        ],
        order: [[ 'f3', 'DESC' ]],
        limit: 100,
      });

      return {
        trends,
        days,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 批量获取股票数据
  async getBatchSourceData(methods) {
    const promises = methods.map(({ method, params = {} }) => {
      switch (method) {
        case 'find':
          return this.find(params);
        case 'stockNetBuy':
          return this.stockNetBuy();
        case 'stockNetSale':
          return this.stockNetSale();
        case 'stockNorthBuy':
          return this.stockNorthBuy();
        case 'stockNorthSale':
          return this.stockNorthSale();
        case 'stockKechuang':
          return this.stockKechuang(params);
        case 'stockDailylimit':
          return this.stockDailylimit();
        case 'statistics':
          return this.statistics();
        case 'statisticsByValue':
          return this.statisticsByValue();
        case 'getSourceStats':
          return this.getSourceStats();
        default:
          return Promise.resolve(null);
      }
    });

    return await Promise.all(promises);
  }

  // 获取服务缓存统计
  async getServiceStats() {
    const stats = await this.getCacheStats('sources');
    const health = await this.getCacheHealth('sources');

    return {
      stats,
      health,
      timestamp: new Date().toISOString(),
    };
  }

  // 清理过期缓存
  async cleanupServiceCache() {
    await this.cleanupExpiredCache('sources');
    return { success: true, message: 'Sources cache cleaned up' };
  }

  // 批量刷新缓存
  async refreshServiceCache(operations) {
    await this.batchRefreshCache(operations);
    return { success: true, message: 'Sources cache refreshed' };
  }

  // 自定义缓存策略 - 针对股票数据的特殊处理
  async applyCacheStrategy(cacheKey, strategy, data) {
    // 调用父类策略
    await super.applyCacheStrategy(cacheKey, strategy, data);

    // 添加股票特定的策略逻辑
    if (strategy === 'aggressive') {
      // 对于激进策略，预加载相关股票数据
      await this.aggressiveSourceWarmup(cacheKey, data);
    } else if (strategy === 'conservative') {
      // 对于保守策略，压缩股票数据
      await this.conservativeSourceCompression(cacheKey, data);
    }
  }

  // 激进策略预热 - 预加载相关股票数据
  async aggressiveSourceWarmup(cacheKey, data) {
    try {
      // 如果是股票数据，预加载相关标签数据
      if (data && data.rows && data.rows.length > 0) {
        const codes = [ ...new Set(data.rows.map(item => item.code)) ];
        const relatedKey = `sources:related:codes:${codes.join(',')}`;

        await this.app.redis.setex(relatedKey, 60, JSON.stringify({
          relatedCodes: codes,
          timestamp: new Date().toISOString(),
        }));
      }
      this.ctx.logger.info(`Aggressive source warmup for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Aggressive source warmup error:', error);
    }
  }

  // 保守策略压缩 - 压缩股票数据
  async conservativeSourceCompression(cacheKey, data) {
    try {
      // 对大型股票数据进行压缩存储
      if (data && JSON.stringify(data).length > 10000) {
        const compressedKey = `${cacheKey}:compressed`;
        const compressedData = {
          compressed: true,
          originalSize: JSON.stringify(data).length,
          compressedSize: Math.floor(JSON.stringify(data).length * 0.6), // 模拟压缩
          timestamp: new Date().toISOString(),
        };

        await this.app.redis.setex(compressedKey, 3600, JSON.stringify(compressedData));
      }
      this.ctx.logger.info(`Conservative source compression for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Conservative source compression error:', error);
    }
  }

  // 获取股票数据对比
  async getSourceComparison(params = {}) {
    return await this.withCache('sourceAnalysis', 'getSourceComparison', params, async () => {
      const { Source } = this.ctx.model;

      const { days = 7, compareDays = 30 } = params;

      const currentData = await Source.findAll({
        where: {
          f2: {
            [Op.ne]: null,
          },
        },
        attributes: [
          'code',
          'displayName',
          'f3', // 涨跌幅
          'f20', // 总市值
          'f62', // 净流入
        ],
        order: [[ 'f3', 'DESC' ]],
        limit: 50,
      });

      const compareData = await Source.findAll({
        where: {
          f2: {
            [Op.ne]: null,
          },
        },
        attributes: [
          'code',
          'displayName',
          'f3', // 涨跌幅
          'f20', // 总市值
          'f62', // 净流入
        ],
        order: [[ 'f20', 'DESC' ]], // 按市值排序
        limit: 50,
      });

      return {
        current: currentData,
        compare: compareData,
        days,
        compareDays,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 获取股票数据预警
  async getSourceAlerts() {
    return await this.withCache('sourceBaseData', 'getSourceAlerts', {}, async () => {
      const { Source } = this.ctx.model;

      const alerts = [];

      // 涨停板预警
      const limitUpStocks = await Source.count({
        where: {
          f3: {
            [Op.gte]: 9.70,
          },
        },
      });

      if (limitUpStocks > 100) {
        alerts.push({
          type: 'limitUp',
          level: 'high',
          message: '涨停板数量过多',
          value: limitUpStocks,
        });
      }

      // 跌停板预警
      const limitDownStocks = await Source.count({
        where: {
          f3: {
            [Op.lte]: -9.70,
          },
        },
      });

      if (limitDownStocks > 50) {
        alerts.push({
          type: 'limitDown',
          level: 'high',
          message: '跌停板数量过多',
          value: limitDownStocks,
        });
      }

      // 北向资金预警
      const northBuyStocks = await Source.count({
        where: {
          sharesz_chg_one: {
            [Op.gt]: 1000000000, // 10亿
          },
        },
      });

      if (northBuyStocks > 20) {
        alerts.push({
          type: 'northBuy',
          level: 'medium',
          message: '北向资金买入股票过多',
          value: northBuyStocks,
        });
      }

      return {
        alerts,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 获取热门股票分析
  async getHotStocksAnalysis() {
    return await this.withCache('sourceAnalysis', 'getHotStocksAnalysis', {}, async () => {
      const { Source, Stock } = this.ctx.model;

      const hotStocks = await Source.findAll({
        where: {
          f2: {
            [Op.ne]: null,
          },
        },
        include: [{
          model: Stock,
          where: {
            hot: true,
          },
          attributes: [ 'code', 'hot', 'remarks', 'rate' ],
        }],
        attributes: [
          'code',
          'displayName',
          'f3', // 涨跌幅
          'f20', // 总市值
          'f62', // 净流入
        ],
        order: [[ 'f3', 'DESC' ]],
        limit: 50,
      });

      return {
        hotStocks,
        totalHot: hotStocks.length,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 获取板块分析
  async getPlateAnalysis() {
    return await this.withCache('sourceAnalysis', 'getPlateAnalysis', {}, async () => {
      const { Source } = this.ctx.model;

      const plates = [
        { name: '上证主板', pattern: '^60', count: 0, avgChange: 0 },
        { name: '科创板', pattern: '^688', count: 0, avgChange: 0 },
        { name: '深证主板', pattern: '^000', count: 0, avgChange: 0 },
        { name: '中小板', pattern: '^002|003', count: 0, avgChange: 0 },
        { name: '创业板', pattern: '^3', count: 0, avgChange: 0 },
        { name: '北交所', pattern: '^8', count: 0, avgChange: 0 },
      ];

      for (const plate of plates) {
        const stocks = await Source.findAll({
          where: {
            code: {
              [Op.regexp]: plate.pattern,
            },
            f3: {
              [Op.ne]: null,
            },
          },
          attributes: [ 'f3' ],
        });

        plate.count = stocks.length;
        if (stocks.length > 0) {
          const totalChange = stocks.reduce((sum, stock) => sum + parseFloat(stock.f3 || 0), 0);
          plate.avgChange = totalChange / stocks.length;
        }
      }

      return {
        plates,
        timestamp: new Date().toISOString(),
      };
    });
  }
}

module.exports = SourcesService;
