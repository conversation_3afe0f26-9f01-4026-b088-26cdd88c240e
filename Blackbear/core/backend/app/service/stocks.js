'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const { QueryTypes } = Sequelize;
const moment = require('moment');

class StocksService extends BaseCacheService {
  // 统一分层缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      // 实时数据
      stocksRealtime: {
        ttl: 30,
        prefix: 'stocks:realtime:',
        description: '股票实时数据',
        strategy: 'aggressive',
      },
      // 分钟级数据
      stocksMinute: {
        ttl: 60,
        prefix: 'stocks:minute:',
        description: '股票分钟数据',
        strategy: 'balanced',
      },
      // 小时级数据
      stocksHourly: {
        ttl: 300,
        prefix: 'stocks:hourly:',
        description: '股票小时数据',
        strategy: 'balanced',
      },
      // 日级数据
      stocksDaily: {
        ttl: 1800,
        prefix: 'stocks:daily:',
        description: '股票日级数据',
        strategy: 'conservative',
      },
      // 统计数据
      stocksStatistics: {
        ttl: 600,
        prefix: 'stocks:statistics:',
        description: '股票统计数据',
        strategy: 'conservative',
      },
      // 排行榜数据
      stocksRanking: {
        ttl: 120,
        prefix: 'stocks:ranking:',
        description: '股票排行榜数据',
        strategy: 'balanced',
      },
      // 搜索数据
      stocksSearch: {
        ttl: 90,
        prefix: 'stocks:search:',
        description: '股票搜索数据',
        strategy: 'balanced',
      },
      // 详情数据
      stocksDetail: {
        ttl: 180,
        prefix: 'stocks:detail:',
        description: '股票详情数据',
        strategy: 'conservative',
      },
    };
  }

  // ========== 业务方法全部用withCache包装 ==========

  async getStockMin(code) {
    return this.withCache('stocksRealtime', 'getStockMin', { code }, async () => {
      const stocks = await this.ctx.model.StockMin.findAll({
        where: { code },
        order: [[ 'date', 'ASC' ]],
        attributes: [ 'id', 'code', 'date', 'tradedate', 'preclose', 'open', 'close', 'high', 'low', 'now', 'turnover', 'volume', 'pctchg' ],
      });
      return stocks;
    });
  }

  async getDecisionRent() {
    return this.withCache('stocksRanking', 'getDecisionRent', {}, async () => {
      const {
        Stock,
        Source,
        Group,
        GroupStock,
      } = this.ctx.model;

      const list = await Stock.findAll({
        order: [[ 'decisionPercent', 'DESC' ]],
        attributes: [ 'macd', 'code', 'white', 'decisionPercent', 'rate' ],
        include: [{
          model: Source,
          required: true,
          where: {
            displayName: {
              [Op.notRegexp]: 'ST|退市',
            },
            f20: {
              [Op.gt]: 100 * 100000000,
            },
            f6: {
              [Op.gt]: 1 * 100000000,
            },
          },
          attributes: [ 'displayName', 'code', 'f3' ],
        }, {
          model: GroupStock,
          include: {
            model: Group,
            attributes: [ 'id', 'name' ],
          },
        }],
        limit: 100,
      });

      return list;
    });
  }

  async getSelection() {
    return this.withCache('stocksSearch', 'getSelection', {}, async () => {
      const {
        Stock,
        Source,
        Report,
        Research,
        NewsStock,
        News,
        Group,
        GroupStock,
      } = this.ctx.model;

      const list = await Stock.findAll({
        where: {
          white: true,
          [Op.or]: [
            { momentum: true },
            { isBottomInversion: true },
            { limit: { [Op.gt]: 2 } },
          ],
        },
        attributes: [ 'macd', 'code', 'momentum', 'isBottomInversion', 'limit', 'ret20', 'tags' ],
        include: [{
          model: Source,
          required: true,
          where: {
            f3: { [Op.gt]: 0 },
            displayName: { [Op.notRegexp]: 'ST|退市' },
          },
          attributes: [ 'displayName', 'code', 'f3' ],
        }, {
          model: Report,
          limit: 10,
          attributes: [ 'announcementId' ],
          order: [[ 'announcementId', 'DESC' ]],
        }, {
          model: Research,
          limit: 10,
          attributes: [ 'id' ],
          order: [[ 'id', 'DESC' ]],
        }, {
          model: NewsStock,
          limit: 10,
          order: [[ 'newsId', 'DESC' ]],
          include: {
            model: News,
            attributes: [ 'newsID', 'content', 'time', 'level', 'type' ],
          },
        }, {
          model: GroupStock,
          include: {
            model: Group,
            attributes: [ 'id', 'name' ],
          },
        }],
        limit: 1000,
      });

      return list;
    });
  }

  async getFunds() {
    return this.withCache('stocksRealtime', 'getFunds', {}, async () => {
      const [ netBuy, netSale, northBuyPer, northSalePer ] = await Promise.all([
        this.ctx.service.sources.stockNetBuy(),
        this.ctx.service.sources.stockNetSale(),
        this.ctx.service.sources.stockNorthBuyPer(),
        this.ctx.service.sources.stockNorthSalePer(),
      ]).catch(error => {
        this.ctx.logger.error('Funds query error:', error);
        return [ null, null, null, null ];
      });

      return {
        netBuy,
        netSale,
        northBuyPer,
        northSalePer,
      };
    });
  }

  async getStarStock(params) {
    return this.withCache('stocksRanking', 'getStarStock', params, async () => {
      const { pageSize, pageIndex } = params;
      const offset = (pageIndex - 1) * pageSize;

      const query = `
        SELECT s.*, src.displayName, src.f3 
        FROM stock s 
        LEFT JOIN source src ON src.code = s.code  
        WHERE s.rate > 0 
        ORDER BY s.rate DESC
        LIMIT ? OFFSET ?
      `;

      const stocks = await this.app.model.query(query, {
        type: QueryTypes.SELECT,
        replacements: [ pageSize, offset ],
      });

      return stocks;
    });
  }

  async getIndustry() {
    return this.withCache('stocksStatistics', 'getIndustry', {}, async () => {
      const query = `
        SELECT COUNT(a.code) as num, b.f100 as name 
        FROM stock a 
        LEFT JOIN source b ON b.code = a.code 
        WHERE a.star = true 
        GROUP BY b.f100 
        ORDER BY COUNT(a.code) DESC
      `;

      const stocks = await this.app.model.query(query, { type: QueryTypes.SELECT });
      return stocks;
    });
  }

  async getIndustryUp() {
    return this.withCache('stocksRealtime', 'getIndustryUp', {}, async () => {
      const query = `
        SELECT AVG(f3) as avg, f100 as name 
        FROM source 
        WHERE LENGTH(f100) > 1 
        GROUP BY f100 
        HAVING AVG(f3) > 0  
        ORDER BY AVG(f3) DESC 
        LIMIT 5
      `;

      const stocks = await this.app.model.query(query, { type: QueryTypes.SELECT });
      return stocks;
    });
  }

  async getIndustryDown() {
    return this.withCache('stocksRealtime', 'getIndustryDown', {}, async () => {
      const query = `
        SELECT AVG(f3) as avg, f100 as name 
        FROM source 
        GROUP BY f100 
        HAVING AVG(f3) < 0  
        ORDER BY AVG(f3) ASC 
        LIMIT 5
      `;

      const stocks = await this.app.model.query(query, { type: QueryTypes.SELECT });
      return stocks;
    });
  }

  async getOrgStock() {
    return this.withCache('stocksHourly', 'getOrgStock', {}, async () => {
      const {
        Stock,
        Source,
        OrgStock,
      } = this.ctx.model;

      const stocks = await OrgStock.findAll({
        include: [{
          model: Source,
          required: true,
          attributes: [ 'displayName', 'code', 'f3' ],
        }, {
          model: Stock,
          attributes: [ 'macd', 'code', 'hot', 'remarks', 'white', 'rate' ],
        }],
        order: [[ 'pBuy', 'DESC' ]],
        attributes: {
          exclude: [ 'createdAt', 'updatedAt', 'deletedAt' ],
        },
      });

      return stocks;
    });
  }

  async getMostReport(num = 20) {
    return this.withCache('stocksDaily', 'getMostReport', { num }, async () => {
      const {
        Stock,
        Source,
      } = this.ctx.model;

      const stocks = await Stock.findAll({
        attributes: [
          'macd', 'code', 'recentReportCount', 'white', 'hot', 'rate',
          'remarks', 'tags', 'isTup', 'isHeavyVolume', 'isBottomInversion',
          'momentum', 'limit', 'score',
        ],
        include: [{
          model: Source,
          required: true,
          attributes: [ 'displayName', 'code', 'f3' ],
        }],
        order: [[ 'recentReportCount', 'DESC' ]],
        limit: num,
      });

      return stocks;
    });
  }

  async getMostComments(num = 20) {
    return this.withCache('stocksDaily', 'getMostComments', { num }, async () => {
      const {
        Stock,
        Source,
        Report,
        Research,
        NewsStock,
        News,
      } = this.ctx.model;

      const stocks = await Stock.findAll({
        attributes: [
          'macd', 'code', 'lastComments', 'diffComments', 'white', 'hot',
          'remarks', 'tags', 'isTup', 'isHeavyVolume', 'isBottomInversion',
          'momentum', 'limit', 'score',
        ],
        include: [{
          model: Source,
          required: true,
          attributes: [ 'displayName', 'code', 'f3' ],
        }, {
          model: Report,
          limit: 10,
          order: [[ 'announcementId', 'DESC' ]],
        }, {
          model: Research,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
        }, {
          model: NewsStock,
          limit: 10,
          order: [[ 'newsId', 'DESC' ]],
          include: {
            model: News,
            attributes: [ 'newsID', 'content', 'time', 'level' ],
          },
        }],
        order: [[ 'diffComments', 'DESC' ]],
        limit: num,
      });

      return stocks;
    });
  }

  async getStar() {
    return this.withCache('stocksRanking', 'getStar', {}, async () => {
      const {
        Stock,
        Source,
        Report,
        Research,
        NewsStock,
        News,
        PostStock,
        Post,
        Group,
        GroupStock,
      } = this.ctx.model;

      const stocks = await Stock.findAll({
        attributes: [
          'code', 'white', 'star', 'tags', 'rate', 'ret20', 'decisionPercent',
          'remarks', 'keyword', 'emv', 'macd', 'isBottomInversion', 'isTup',
          'isHeavyVolume', 'momentum', 'limit', 'score',
        ],
        order: [[ 'decisionPercent', 'DESC' ]],
        where: { star: true },
        include: [{
          model: Source,
          required: true,
          attributes: [ 'displayName', 'code', 'f3', 'f6', 'f100' ],
        }, {
          model: Report,
          limit: 10,
          order: [[ 'announcementId', 'DESC' ]],
        }, {
          model: Research,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
        }, {
          model: NewsStock,
          limit: 10,
          order: [[ 'newsId', 'DESC' ]],
          include: {
            model: News,
            attributes: [ 'newsID', 'content', 'time', 'level' ],
          },
        }, {
          model: PostStock,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
          include: {
            model: Post,
            attributes: [ 'id', 'title', 'intro', 'time' ],
          },
        }, {
          model: GroupStock,
          include: {
            model: Group,
            attributes: [ 'id', 'name' ],
          },
        }],
        limit: 1000,
      });

      return stocks;
    });
  }

  async getScore(params) {
    return this.withCache('stocksSearch', 'getScore', params, async () => {
      const {
        Stock,
        Source,
        Report,
        Research,
        NewsStock,
        News,
        PostStock,
        Post,
        Group,
        GroupStock,
      } = this.ctx.model;

      const stocks = await Stock.findAndCountAll({
        order: [[ 'score', 'DESC' ]],
        where: {
          score: { [Op.gt]: 0 },
        },
        include: [{
          model: Source,
          required: true,
        }, {
          model: Report,
          limit: 10,
          order: [[ 'announcementId', 'DESC' ]],
        }, {
          model: Research,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
        }, {
          model: NewsStock,
          limit: 10,
          order: [[ 'newsId', 'DESC' ]],
          include: {
            model: News,
            attributes: [ 'newsID', 'content', 'time', 'level' ],
          },
        }, {
          model: PostStock,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
          include: {
            model: Post,
            attributes: [ 'id', 'title', 'intro', 'time' ],
          },
        }, {
          model: GroupStock,
          include: {
            model: Group,
            attributes: [ 'id', 'name' ],
          },
        }],
        offset: parseInt(params.pageIndex - 1) * parseInt(params.pageSize),
        limit: parseInt(params.pageSize),
      });

      return stocks;
    });
  }

  async getTrend(num = 20) {
    return this.withCache('stocksMinute', 'getTrend', { num }, async () => {
      const {
        Stock,
        Source,
        StockTrend,
        Report,
        Research,
        NewsStock,
        News,
        GroupStock,
        Group,
      } = this.ctx.model;

      const stocks = await StockTrend.findAll({
        attributes: [
          'code',
          [ Sequelize.fn('SUM', Sequelize.col('score')), 'score' ],
        ],
        order: [[ Sequelize.literal('score DESC') ]],
        group: 'code',
        include: [{
          model: Source,
          required: true,
          attributes: [ 'displayName', 'code', 'f3' ],
        }, {
          model: Stock,
          attributes: [
            'macd', 'star', 'code', 'hot', 'remarks', 'keyword', 'rate',
            'white', 'tags', 'isTup', 'isHeavyVolume', 'isBottomInversion',
            'momentum', 'limit', 'emv', 'score',
          ],
          include: [{
            model: GroupStock,
            include: {
              model: Group,
              attributes: [ 'id', 'name' ],
            },
          }, {
            model: Report,
            limit: 10,
            order: [[ 'announcementId', 'DESC' ]],
          }, {
            model: Research,
            limit: 10,
            order: [[ 'id', 'DESC' ]],
          }, {
            model: NewsStock,
            limit: 10,
            order: [[ 'newsId', 'DESC' ]],
            include: {
              model: News,
              attributes: [ 'newsID', 'content', 'time', 'level' ],
            },
          }],
        }],
        limit: num,
      });

      return stocks;
    });
  }

  // 优化后的二级分类排名查询 - 统计数据
  async findSortSecRankById(firstLevelId) {
    return this.withCache('stocksStatistics', 'findSortSecRankById', { firstLevelId }, async () => {
      const {
        Stock,
        Source,
        Sort,
        SortSec,
      } = this.ctx.model;

      const where = firstLevelId ? { firstLevelId } : {};

      const secondLevel = await Stock.findAll({
        attributes: [
          'secondLevelId',
          [ Sequelize.fn('COUNT', Sequelize.col('f1')), 'total' ],
          [ Sequelize.fn('SUM', Sequelize.col('f6')), 'amount' ],
          [ Sequelize.fn('AVG', Sequelize.col('f3')), 'avg' ],
        ],
        where,
        order: [[ Sequelize.literal('avg DESC') ]],
        group: 'secondLevelId',
        include: [{
          model: Sort,
          where: { id: { [Op.gt]: 0 } },
          attributes: [ 'id', 'name' ],
        }, {
          model: SortSec,
          where: { id: { [Op.gt]: 0 } },
          attributes: [ 'id', 'name' ],
        }, {
          model: Source,
          required: true,
          attributes: [ 'code', 'displayName' ],
        }],
      });

      return secondLevel;
    });
  }

  // 优化后的分类金额查询 - 日级数据
  async findSortAmount(firstLevelId) {
    return this.withCache('stocksDaily', 'findSortAmount', { firstLevelId }, async () => {
      const sort = await this.ctx.model.Amount.findAll({
        where: {
          firstLevelId,
          secondLevelId: { [Op.is]: null },
        },
        order: [[ 'date', 'DESC' ]],
        limit: 10,
        include: [{
          model: this.ctx.model.Sort,
          where: { id: { [Op.gt]: 0 } },
        }],
      });

      return sort;
    });
  }

  // 优化后的二级分类金额查询 - 日级数据
  async findSecondSortAmount(secondLevelId) {
    return this.withCache('stocksDaily', 'findSecondSortAmount', { secondLevelId }, async () => {
      const sort = await this.ctx.model.Amount.findAll({
        where: { secondLevelId },
        order: [[ 'date', 'DESC' ]],
        limit: 10,
        include: [{
          model: this.ctx.model.SortSec,
          where: { id: { [Op.gt]: 0 } },
        }],
      });

      return sort;
    });
  }

  // 优化后的所有二级分类金额查询 - 日级数据
  async findAllSecondSortAmount() {
    return this.withCache('stocksDaily', 'findAllSecondSortAmount', {}, async () => {
      const sort = await this.ctx.model.Amount.findAll({
        where: {
          secondLevelId: { [Op.gt]: 0 },
          date: {
            [Op.gt]: moment().subtract(30, 'days').format('YYYYMMDD'),
          },
        },
        order: [[ 'date', 'ASC' ], [ 'firstLevelId', 'ASC' ]],
        include: [{
          model: this.ctx.model.SortSec,
          where: { id: { [Op.gt]: 0 } },
        }, {
          model: this.ctx.model.Sort,
          where: { id: { [Op.gt]: 0 } },
        }],
      });

      return sort;
    });
  }

  // 优化后的一级分类排名查询 - 统计数据
  async findSortFirRank() {
    return this.withCache('stocksStatistics', 'findSortFirRank', {}, async () => {
      const {
        Stock,
        Source,
        Sort,
      } = this.ctx.model;

      const firstLevel = await Stock.findAll({
        attributes: [
          'firstLevelId',
          [ Sequelize.fn('COUNT', Sequelize.col('f1')), 'total' ],
          [ Sequelize.fn('AVG', Sequelize.col('f3')), 'avg' ],
          [ Sequelize.fn('SUM', Sequelize.col('f6')), 'amount' ],
        ],
        group: 'firstLevelId',
        order: [[ Sequelize.literal('avg DESC') ]],
        having: {
          total: { [Op.gt]: 3 },
        },
        include: [{
          model: Sort,
          where: { id: { [Op.gt]: 0 } },
        }, {
          model: Source,
          required: true,
          attributes: [ 'code', 'displayName' ],
        }],
      });

      return firstLevel;
    });
  }

  // 优化后的二级分类排名查询 - 统计数据
  async findSortSecRank() {
    return this.withCache('stocksStatistics', 'findSortSecRank', {}, async () => {
      const {
        Stock,
        Source,
        SortSec,
      } = this.ctx.model;

      const firstLevel = await Stock.findAll({
        attributes: [
          'firstLevelId',
          'secondLevelId',
          [ Sequelize.fn('COUNT', Sequelize.col('f1')), 'total' ],
          [ Sequelize.fn('AVG', Sequelize.col('f3')), 'avg' ],
          [ Sequelize.fn('SUM', Sequelize.col('f6')), 'amount' ],
        ],
        group: 'secondLevelId',
        order: [[ Sequelize.literal('avg DESC') ]],
        having: {
          total: { [Op.gt]: 3 },
        },
        include: [{
          model: SortSec,
          where: { id: { [Op.gt]: 0 } },
        }, {
          model: Source,
          required: true,
          attributes: [ 'code', 'displayName' ],
        }],
      });

      return firstLevel;
    });
  }

  // 优化后的股票查找查询 - 搜索数据
  async find(params) {
    // 添加调试参数支持
    const { debug, forceRefresh, ...queryParams } = params;

    return this.withCache('stocksSearch', 'find', queryParams, async () => {
      const { type, st, first_level, second_level, star, white, pageSize, pageIndex } = queryParams;

      // 添加调试日志
      if (debug) {
        this.ctx.logger.info('Stocks find query params:', queryParams);
      }

      const {
        Stock,
        StockQuant,
        Source,
        TagStock,
        Tag,
        SortSec,
        Report,
        ReportTime,
        News,
        Research,
        NewsStock,
        Configuration,
        GroupStock,
        Group,
        PostStock,
        Post,
        Fund,
        FundStock,
        Stockkline,
      } = this.ctx.model;

      // 处理二级分类查询
      if (first_level) {
        let where = { parentId: first_level };

        if (second_level) {
          where = Object.assign(where, { id: second_level });
        }

        const secondLevel = await SortSec.findAll({
          where,
          order: [[ 'sortId', 'ASC' ]],
          include: [{
            model: Stock,
            attributes: { exclude: [ 'businessAnalysis' ] },
            include: [{
              model: Source,
              attributes: [ 'code', 'displayName', 'f102', 'f100', 'f3', 'f127', 'f149', 'f25', 'f40', 'f41', 'f45', 'f46', 'f20' ],
            }, {
              model: TagStock,
              include: {
                model: Tag,
                attributes: [ 'id', 'code', 'name' ],
              },
            }],
          }],
        });

        if (debug) {
          this.ctx.logger.info('Second level query result count:', secondLevel.length);
        }

        return { stocks: secondLevel, strategy: null };
      }

      // 处理量化查询
      if (type === 'quant') {
        const strategy = await Configuration.findByPk(1);
        const stocks = await StockQuant.findAndCountAll({
          order: [[ 'rate', 'DESC' ]],
          include: [{
            model: Source,
            attributes: [ 'code', 'displayName', 'f102', 'f100', 'f3', 'f127', 'f149', 'f25', 'f40', 'f41', 'f45', 'f46', 'f20' ],
          }],
          offset: parseInt(pageIndex - 1) * parseInt(pageSize),
          limit: parseInt(pageSize),
        });

        if (debug) {
          this.ctx.logger.info('Quant query result count:', stocks.count);
        }

        return { stocks, strategy };
      }

      // 构建查询条件
      let where = {};
      let order = [];
      let sourceWhere = {};

      // 根据类型设置查询条件
      const whereConditions = await this.buildWhereConditions(type, Stockkline);
      where = whereConditions.where;
      order = whereConditions.order;
      sourceWhere = whereConditions.sourceWhere;

      // 添加筛选条件
      if (star === 'true') {
        where = Sequelize.and(where, { rate: { [Op.gt]: 0 } });
      }

      if (white === 'true') {
        where = Sequelize.and(where, { white: 1 });
      }

      if (this.ctx.helper.toInt(st) === 1) {
        sourceWhere = Object.assign(sourceWhere, {
          displayName: { [Op.notRegexp]: 'ST|退市' },
        });
      }

      if (debug) {
        this.ctx.logger.info('Stock where conditions:', JSON.stringify(where));
        this.ctx.logger.info('Source where conditions:', JSON.stringify(sourceWhere));
      }

      const strategy = await Configuration.findByPk(1);

      const stocks = await Stock.findAndCountAll({
        where,
        order,
        distinct: true,
        include: [{
          model: Source,
          where: sourceWhere,
          attributes: [ 'code', 'displayName', 'f100', 'f3', 'f20', 'f6', 'zzb_one', 'f127', 'f149', 'f24', 'f25', 'f9', 'f23', 'f14', 'f103', 'f40', 'f41', 'f45', 'f46' ],
        }, {
          model: TagStock,
          include: {
            model: Tag,
          },
        }, {
          model: Report,
          limit: 10,
          order: [[ 'announcementId', 'DESC' ]],
        }, {
          model: Research,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
        }, {
          model: ReportTime,
          limit: 1,
          order: [[ 'date', 'DESC' ]],
          attributes: [ 'id', 'code', 'name', 'date' ],
        }, {
          model: NewsStock,
          limit: 10,
          order: [[ 'newsId', 'DESC' ]],
          include: {
            model: News,
            attributes: [ 'newsID', 'content', 'time', 'level' ],
          },
        }, {
          model: FundStock,
          limit: 10,
          order: [[ 'ratio', 'DESC' ]],
          include: {
            model: Fund,
            attributes: [ 'code', 'name', 'shortName' ],
          },
        }, {
          model: GroupStock,
          include: {
            model: Group,
            attributes: [ 'id', 'name' ],
          },
        }, {
          model: PostStock,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
          include: {
            model: Post,
            attributes: [ 'id', 'title', 'intro', 'time' ],
          },
        }],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });

      if (debug) {
        this.ctx.logger.info('Main query result count:', stocks.count);
        this.ctx.logger.info('Main query rows count:', stocks.rows.length);
      }

      return { stocks, strategy };
    }, {
      forceRefresh: forceRefresh || false, // 支持强制刷新缓存
    });
  }

  // 构建查询条件的辅助方法
  async buildWhereConditions(type, Stockkline) {
    let where = {};
    let order = [];
    let sourceWhere = {};

    switch (type) {
      case 'limit':
        where = { limit: { [Op.gt]: 0 } };
        order = [[ 'limit', 'DESC' ]];
        break;
      case 'isHeavyVolume':
        where = { isHeavyVolume: true };
        break;
      case 'isBottomInversion':
        const klines = await Stockkline.findAll();
        const k_arr = klines
          .filter(element => element.type === 'bottom reverse')
          .map(element => ({ kline: { [Op.substring]: element.func } }));
        where = { [Op.or]: k_arr };
        break;
      case 'momentum':
        where = { momentum: true };
        break;
      case 'emv':
        where = { emv: true };
        break;
      case 'decisionPercent':
        order = [[ 'decisionPercent', 'DESC' ]];
        break;
      case 'beta':
        where = { beta: { [Op.gt]: 1.2 } };
        order = [[ 'beta', 'DESC' ]];
        break;
      case 'roe':
        where = { roe: { [Op.gt]: 1 } };
        order = [[ 'roe', 'DESC' ]];
        break;
      case 'peg':
        where = {
          [Op.and]: [
            { peg: { [Op.lt]: 1 } },
            { peg: { [Op.gt]: 0 } },
          ],
        };
        order = [[ 'peg', 'ASC' ]];
        break;
      case 'alpha':
        where = { alpha: { [Op.gt]: 0.5 } };
        order = [[ 'alpha', 'DESC' ]];
        break;
      case 'md':
        where = { md: { [Op.lt]: 0.15 } };
        order = [[ 'md', 'ASC' ]];
        break;
      case 'isTup':
        where = { kline: { [Op.substring]: 'CDL3WHITESOLDIERS' } };
        break;
      case 'up':
      case 'down':
        const klines2 = await Stockkline.findAll();
        const k_arr2 = klines2
          .filter(element => element.type === type)
          .map(element => ({ kline: { [Op.substring]: element.func } }));
        where = { [Op.or]: k_arr2 };
        break;
      case 'star':
        where = { rate: { [Op.gt]: 0 } };
        order = [[ 'rate', 'DESC' ]];
        break;
      case 'ganggu':
        sourceWhere = { zzb_one: { [Op.ne]: null } };
        break;
      case 'wave':
        sourceWhere = { f7: { [Op.gt]: 8 } };
        break;
      case 'yoypni':
        where = { yoypni: { [Op.ne]: null } };
        order = [[ 'yoypni', 'DESC' ]];
        break;
    }

    return { where, order, sourceWhere };
  }

  // 优化后的股票创建
  async create(params) {
    const stock = await this.ctx.model.Stock.findOrCreate({
      where: { ...params },
    });

    // 清除相关缓存
    await this.clearRelatedCache();

    return stock;
  }

  // 优化后的股票更新
  async update(id, params) {
    const {
      Sort,
      SortSec,
      Stock,
      Source,
    } = this.ctx.model;

    const stock = await Stock.findByPk(id);
    await stock.update({ ...params });

    const stock_full = await Stock.findOne({
      where: { code: id },
      distinct: true,
      include: [{
        model: Sort,
      }, {
        model: SortSec,
      }, {
        model: Source,
      }],
    });

    // 清除相关缓存
    await this.clearRelatedCache({ code: id });

    return stock_full;
  }

  // 优化后的股票详情查询 - 详情数据
  async show(params) {
    return this.withCache('stocksDetail', 'show', params, async () => {
      const {
        Stock,
        Sort,
        Tag,
        TagStock,
        Source,
        SortSec,
        Report,
        News,
        Research,
        NewsStock,
        GroupStock,
        Group,
        PostStock,
        Post,
        Fund,
        FundStock,
      } = this.ctx.model;

      const stock = await Stock.findOne({
        where: { code: params.id },
        distinct: true,
        include: [{
          model: Sort,
        }, {
          model: SortSec,
        }, {
          model: TagStock,
          include: {
            model: Tag,
            where: { isTopic: null },
            attributes: { exclude: [ 'desc' ] },
          },
        }, {
          model: Source,
        }, {
          model: Report,
          limit: 10,
          order: [[ 'announcementId', 'DESC' ]],
        }, {
          model: Research,
          limit: 10,
          order: [[ 'id', 'DESC' ]],
        }, {
          model: NewsStock,
          limit: 10,
          order: [[ 'newsId', 'DESC' ]],
          include: {
            model: News,
            attributes: [ 'newsID', 'content', 'time', 'level', 'type' ],
          },
        }, {
          model: FundStock,
          limit: 10,
          order: [[ 'ratio', 'DESC' ]],
          include: {
            model: Fund,
            attributes: [ 'code', 'name', 'shortName' ],
          },
        }, {
          model: GroupStock,
          include: {
            model: Group,
            attributes: [ 'id', 'name' ],
          },
        }, {
          model: PostStock,
          limit: 10,
          order: [[ 'postId', 'DESC' ]],
          include: {
            model: Post,
            attributes: { exclude: [ 'content' ] },
          },
        }],
      });

      return stock;
    });
  }

  // 优化后的概览查询 - 详情数据
  async overview(params) {
    return this.withCache('stocksDetail', 'overview', params, async () => {
      let { code, type, star } = params;
      let where = {};

      if (star === 'true') {
        where = Sequelize.and(where, { rate: { [Op.gt]: 0 } });
      }

      if (type && type.length > 0) {
        where = Sequelize.and(where, {
          tags: { [Op.substring]: `${type}` },
        });
      }

      if (!code) {
        const firstStock = await this.ctx.model.Stock.findOne({
          where,
          order: [[ 'code', 'ASC' ]],
        });
        code = firstStock.get('code');
      }

      const [ stock, total, num, next, previous ] = await Promise.all([
        this.ctx.service.stocks.show({ id: code }),
        this.ctx.model.Stock.count({ where }),
        this.ctx.model.Stock.count({
          where: Sequelize.and(where, { code: { [Op.lte]: code } }),
          order: [[ 'code', 'ASC' ]],
        }),
        this.ctx.model.Stock.findOne({
          where: Sequelize.and(where, { code: { [Op.gt]: code } }),
          order: [[ 'code', 'ASC' ]],
        }),
        this.ctx.model.Stock.findOne({
          where: Sequelize.and(where, { code: { [Op.lt]: code } }),
          order: [[ 'code', 'DESC' ]],
        }),
      ]);

      return {
        previous,
        current: stock,
        next,
        total,
        num,
      };
    });
  }

  // 优化后的历史数据查询 - 日级数据
  async showHistory(code, params) {
    return this.withCache('stocksDaily', 'showHistory', { code, ...params }, async () => {
      const { begin, end } = params;
      const { ctx } = this;

      const historys = await ctx.model.StockHistory.findAll({
        where: {
          code,
          tradedate: { [Op.between]: [ begin, end ] },
        },
        order: [[ 'tradedate', 'ASC' ]],
        attributes: [ 'id', 'code', 'date', 'tradedate', 'open', 'close', 'high', 'low', 'turn', 'volume', 'pctChg', 'amount' ],
      });

      return historys;
    });
  }

  // 优化后的股票删除
  async destroy(id) {
    const stock = await this.ctx.model.Stock.findByPk(id);
    await stock.destroy({ force: true });

    // 清除相关缓存
    await this.clearRelatedCache({ code: id });

    return stock;
  }

  // 批量更新股票数据
  async batchUpdate(stocks) {
    const { Stock } = this.ctx.model;

    const transaction = await this.ctx.model.transaction();

    try {
      for (const stockData of stocks) {
        await Stock.upsert(stockData, { transaction });
      }

      await transaction.commit();

      // 清除相关缓存
      await this.clearRelatedCache();

      return { success: true, count: stocks.length };
    } catch (error) {
      await transaction.rollback();
      this.ctx.logger.error('Batch update error:', error);
      throw error;
    }
  }

  // 获取股票统计信息 - 统计数据
  async getStatistics() {
    return this.withCache('stocksStatistics', 'getStatistics', {}, async () => {
      const { Stock, Source } = this.ctx.model;

      const [ totalStocks, whiteStocks, starStocks, upStocks, downStocks ] = await Promise.all([
        Stock.count(),
        Stock.count({ where: { white: true } }),
        Stock.count({ where: { star: true } }),
        Source.count({ where: { f3: { [Op.gt]: 0 } } }),
        Source.count({ where: { f3: { [Op.lt]: 0 } } }),
      ]);

      return {
        total: totalStocks,
        white: whiteStocks,
        star: starStocks,
        up: upStocks,
        down: downStocks,
      };
    });
  }

  // 搜索股票 - 搜索数据
  async searchStocks(keyword, limit = 20) {
    return this.withCache('stocksSearch', 'searchStocks', { keyword, limit }, async () => {
      const { Stock, Source } = this.ctx.model;

      const stocks = await Stock.findAll({
        include: [{
          model: Source,
          required: true,
          where: {
            [Op.or]: [
              { displayName: { [Op.like]: `%${keyword}%` } },
              { code: { [Op.like]: `%${keyword}%` } },
            ],
          },
          attributes: [ 'displayName', 'code', 'f3', 'f100' ],
        }],
        limit,
        attributes: [ 'code', 'white', 'star', 'rate' ],
      });

      return stocks;
    });
  }

  // 获取缓存统计信息
  async getCacheStats() {
    try {
      const stats = {};

      for (const [ type, config ] of Object.entries(this.cacheConfig)) {
        const keys = await this.app.redis.keys(`${config.prefix}*`);
        stats[type] = keys.length;
      }

      return stats;
    } catch (error) {
      this.ctx.logger.error('Failed to get stocks cache stats', error);
      return {};
    }
  }

  // 强制刷新缓存
  async refreshCache(type, method, params = {}) {
    try {
      const cacheKey = this.getCacheKey(type, method, params);
      await this.app.redis.del(cacheKey);
      this.ctx.logger.info(`Forced refresh cache for ${type}:${method}`);
    } catch (error) {
      this.ctx.logger.error(`Failed to refresh cache for ${type}:${method}`, error);
    }
  }

  // 数据库诊断方法
  async diagnoseDatabase() {
    const result = {
      success: true,
      issues: [],
      recommendations: [],
      stats: {},
    };

    try {
      const { Stock, Source, GroupStock, Group } = this.ctx.model;

      // 1. 检查基本表数据量
      const stockCount = await Stock.count();
      const sourceCount = await Source.count();
      const groupStockCount = await GroupStock.count();
      const groupCount = await Group.count();

      result.stats = {
        stockCount,
        sourceCount,
        groupStockCount,
        groupCount,
      };

      // 2. 检查关联关系
      const stockWithSource = await Stock.count({
        include: [{
          model: Source,
          required: true,
        }],
      });

      const stockWithGroup = await Stock.count({
        include: [{
          model: GroupStock,
          include: {
            model: Group,
          },
        }],
      });

      result.stats.stockWithSource = stockWithSource;
      result.stats.stockWithGroup = stockWithGroup;

      // 3. 检查是否有数据但查询不到的情况
      if (stockCount > 0 && stockWithSource === 0) {
        result.issues.push('Stock表有数据但无法关联到Source表');
        result.recommendations.push('检查Stock和Source表的code字段关联关系');
      }

      if (stockCount > 0 && stockWithGroup === 0) {
        result.issues.push('Stock表有数据但无法关联到Group表');
        result.recommendations.push('检查GroupStock关联表的数据');
      }

      // 4. 检查软删除问题
      const deletedStockCount = await Stock.count({
        paranoid: false,
        where: {
          deletedAt: { [Op.ne]: null },
        },
      });

      if (deletedStockCount > 0) {
        result.issues.push(`发现${deletedStockCount}条软删除的股票数据`);
        result.recommendations.push('检查是否需要恢复软删除的数据');
      }

      // 5. 检查缓存状态
      try {
        const cacheStats = await this.getCacheStats();
        result.stats.cacheStats = cacheStats;
      } catch (cacheError) {
        result.issues.push('缓存系统异常');
        result.recommendations.push('检查Redis连接和配置');
      }

      // 6. 测试基本查询
      const testStock = await Stock.findOne({
        include: [{
          model: Source,
          required: true,
          attributes: [ 'code', 'displayName', 'f3' ],
        }],
        limit: 1,
      });

      if (!testStock) {
        result.issues.push('无法查询到任何股票数据');
        result.recommendations.push('检查数据库连接和表结构');
      } else {
        result.stats.sampleStock = {
          code: testStock.code,
          displayName: testStock.Source?.displayName,
          f3: testStock.Source?.f3,
        };
      }

    } catch (error) {
      result.success = false;
      result.issues.push(`数据库诊断失败: ${error.message}`);
      result.recommendations.push('检查数据库连接和权限');
      this.ctx.logger.error('Database diagnosis error:', error);
    }

    return result;
  }

  // 清除所有股票相关缓存
  async clearAllStocksCache() {
    try {
      const config = this.cacheConfig;
      const clearedKeys = [];

      for (const [ type, typeConfig ] of Object.entries(config)) {
        const keys = await this.app.redis.keys(`${typeConfig.prefix}*`);
        if (keys.length > 0) {
          await this.app.redis.del(...keys);
          clearedKeys.push(...keys);
        }
      }

      this.ctx.logger.info(`Cleared ${clearedKeys.length} stocks cache keys`);
      return {
        success: true,
        clearedCount: clearedKeys.length,
        keys: clearedKeys,
      };
    } catch (error) {
      this.ctx.logger.error('Failed to clear stocks cache:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = StocksService;
