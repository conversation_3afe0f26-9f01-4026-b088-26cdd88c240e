'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { count } = require('yargs');
const { Op } = Sequelize;

class ChartService extends BaseCacheService {
  get cacheConfig() {
    return {
      realtime: {
        ttl: 30,
        prefix: 'chart:realtime:',
        description: '图表实时数据',
        strategy: 'aggressive',
      },
    };
  }

  async zhangtie() {
    return await this.withCache('realtime', 'zhangtie', {}, async () => {
      const { Source } = this.ctx.model;
      const where = {
        displayName: { [Op.notRegexp]: '退市' },
        f2: { [Op.ne]: null },
      };
      const [ total, less, more, equal ] = await Promise.all([
        Source.count({ where }),
        Source.count({ where: { ...where, f3: { [Op.lt]: 0 } } }),
        Source.count({ where: { ...where, f3: { [Op.gt]: 0 } } }),
        Source.count({ where: { ...where, f3: { [Op.eq]: 0 } } }),
      ]);
      return [
        { name: '上涨', percent: parseFloat((more / total).toFixed(4)), num: more, a: '1' },
        { name: '平盘', percent: parseFloat((equal / total).toFixed(4)), num: equal, a: '1' },
        { name: '下跌', percent: 1 - (more / total).toFixed(4) - (equal / total).toFixed(4), num: less, a: '1' },
      ];
    });
  }

  async zhanbi() {
    return await this.withCache('realtime', 'zhanbi', {}, async () => {
      const { Source } = this.ctx.model;
      const where = {
        displayName: { [Op.notRegexp]: '退市' },
        f2: { [Op.ne]: null },
      };
      const [ pctNe15, pctNe9, pctNe5, pctNe3, pctNe0, pctNePo,
        pctPo0, pctPo3, pctPo5, pctPo9, pctPo15 ] = await Promise.all([
        Source.count({ where: { ...where, f3: { [Op.lte]: -15 } } }),
        Source.count({ where: { ...where, f3: { [Op.gt]: -15, [Op.lte]: -9 } } }),
        Source.count({ where: { ...where, f3: { [Op.gt]: -9, [Op.lte]: -5 } } }),
        Source.count({ where: { ...where, f3: { [Op.gt]: -5, [Op.lte]: -3 } } }),
        Source.count({ where: { ...where, f3: { [Op.gt]: -3, [Op.lt]: 0 } } }),
        Source.count({ where: { ...where, f3: { [Op.eq]: 0 } } }),
        Source.count({ where: { ...where, f3: { [Op.gt]: 0, [Op.lt]: 3 } } }),
        Source.count({ where: { ...where, f3: { [Op.gte]: 3, [Op.lt]: 5 } } }),
        Source.count({ where: { ...where, f3: { [Op.gte]: 5, [Op.lt]: 9 } } }),
        Source.count({ where: { ...where, f3: { [Op.gte]: 9, [Op.lt]: 15 } } }),
        Source.count({ where: { ...where, f3: { [Op.gte]: 15 } } }),
      ]);
      return [
        { name: '-15%以上', count: pctNe15 },
        { name: '-9%~-15%', count: pctNe9 },
        { name: '-5%~-9%', count: pctNe5 },
        { name: '-3%~-5%', count: pctNe3 },
        { name: '0~-3%', count: pctNe0 },
        { name: '0', count: pctNePo },
        { name: '0~3%', count: pctPo0 },
        { name: '3%~5%', count: pctPo3 },
        { name: '5%~-9%', count: pctPo5 },
        { name: '9%~15%', count: pctPo9 },
        { name: '15%以上', count: pctPo15 },
      ];
    });
  }
}

module.exports = ChartService;
