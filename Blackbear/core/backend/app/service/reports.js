'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class ReportsService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'reports:minute:',
        description: '报告分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'reports:hourly:',
        description: '报告小时级数据',
        strategy: 'conservative',
      },
      daily: {
        ttl: 1800,
        prefix: 'reports:daily:',
        description: '报告日级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'reports:statistics:',
        description: '报告统计数据',
        strategy: 'conservative',
      },
      search: {
        ttl: 90,
        prefix: 'reports:search:',
        description: '报告搜索数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'reports:detail:',
        description: '报告详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 报告列表分页查询
  async findAllReport(params) {
    return await this.withCache('minute', 'findAllReport', params, async () => {
      const { pageIndex, pageSize, tag, reportType } = params;
      const where = {
        secCode: {
          [Op.regexp]: '^68|002|300|000|60',
        },
      };
      if (tag) {
        where.secName = { [Op.like]: `%${tag}%` };
      }
      if (reportType && reportType.length > 0) {
        where.type = reportType;
      }
      return await this.ctx.model.Report.findAll({
        order: [[ 'announcementTime', 'DESC' ]],
        where,
        include: [
          { model: this.ctx.model.Stock },
          { model: this.ctx.model.Source },
        ],
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }
}

module.exports = ReportsService;
