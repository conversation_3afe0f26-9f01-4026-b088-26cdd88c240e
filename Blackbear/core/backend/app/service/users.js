'use strict';

const { Service } = require('egg');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const uuid = require('uuid');
const moment = require('moment');
const NodeRSA = require('node-rsa');

/**
 * UsersService
 * Business logic service
 */
class UsersService extends Service {
  // 建议：对 create、login、loginWithPhone 等方法加限流防爆破
  async create(params) {
    const { ctx } = this;
    const { phone, email, captcha, username, password } = params;
    // 参数基础校验
    if (!username || !email || !phone || !password) {
      return { errorcode: 100, message: '参数不完整' };
    }
    try {
      const exitCode = await ctx.service.verifications.verificationCode({
        target: email,
        code: captcha,
        type: 1,
      });
      if (exitCode) {
        const resExistsUsername = await this.existsUserUniqueFields({ username });
        if (resExistsUsername) {
          return { errorcode: 1, message: '用户名已存在' };
        }
        const resExistsEmail = await this.existsUserUniqueFields({ email });
        if (resExistsEmail) {
          return { errorcode: 2, message: '邮箱已存在' };
        }
        const resExistsPhone = await this.existsUserUniqueFields({ phone });
        if (resExistsPhone) {
          return { errorcode: 3, message: '手机号已存在' };
        }
        params = Object.assign(params, await ctx.helper.tools.saltPassword(params.password));
        params.password += params.salt;
        const transaction = await ctx.model.transaction();
        try {
          // 创建用户
          const defaultRole = await ctx.model.Role.findOne({ where: { isDefault: 1 } });
          const res_user = await ctx.model.User.create({
            id: uuid.v4(),
            homeUrl: 'admin-welcome',
            from: 'web',
            roleId: username === ctx.app.config.adminUsername ? 'admin.top' : defaultRole.code,
            ...params,
          }, { transaction });
          const token = await ctx.service.users.generateToken(res_user.dataValues);
          await res_user.update({
            ip: ctx.request.ip,
            token,
            lastLogin: moment().format('YYYY-MM-DD HH:mm:ss'),
          }, { transaction });
          await exitCode.update({ available: 0 }, { transaction });
          await transaction.commit();
          return await ctx.model.User.findOne({
            where: { id: res_user.id },
            include: [{
              model: this.ctx.model.Role,
              attributes: [ 'code', 'name' ],
              include: [{
                model: this.ctx.model.RolePermission,
                include: [{ model: this.ctx.model.Permission }],
              }, {
                model: this.ctx.model.RoleMenu,
                include: [{ model: this.ctx.model.Menu }],
              }],
            }],
          });
        } catch (e) {
          await transaction.rollback();
          ctx.logger.error('用户注册事务回滚:', e);
        }
      } else {
        return { errorcode: 4, message: '验证码错误或已使用过或已过期' };
      }
    } catch (error) {
      ctx.logger.error('用户注册异常:', error);
      return { errorcode: 500, message: '注册失败', error: error.message };
    }
  }

  async generateToken(params) {
    const { ctx } = this;
    if (!params.id) {
      return null;
    }
    try {
      const token = ctx.app.jwt.sign({
        id: params.id,
        username: params.username,
        roleId: params.roleId,
        isBlock: params.isBlock,
        point: params.point,
      }, ctx.app.config.jwt.secret, {
        expiresIn: ctx.app.config.jwtExp,
      });
      await ctx.app.redis.set(`token_${params.id}`, token);
      ctx.session.userInfo = params;
      return token;
    } catch (error) {
      ctx.logger.error('生成token异常:', error);
      return null;
    }
  }

  async login(params) {
    const { ctx } = this;
    // 建议：加限流防爆破
    if (!params.username || !params.password) {
      return { errorcode: 100, message: '参数不完整' };
    }
    try {
      // 检查用户是否存在
      const user = await ctx.model.User.scope('withPassword').findOne({ where: { username: params.username } });
      if (!user) {
        return { errorcode: 1, message: '用户名不存在' };
      }
      if (user.isBlock == 1) {
        return { errorcode: 2, message: '用户已停用' };
      }
      // 验证码校验
      const exitCaptcha = await ctx.service.verifications.verificationCode({
        target: 'login',
        code: params.captcha,
        type: 2,
      });
      if (!exitCaptcha) {
        return { errorcode: 3, message: '请输入正确的验证码' };
      }
      params = Object.assign(params, await ctx.helper.tools.saltPassword(params.password, user.dataValues.password.substr(32)));
      params.password += params.salt;
      const result = await ctx.model.User.findOne({
        include: [{
          model: this.ctx.model.Role,
          attributes: [ 'code', 'name' ],
          include: [{
            model: this.ctx.model.RolePermission,
            include: [{ model: this.ctx.model.Permission }],
          }, {
            model: this.ctx.model.RoleMenu,
            include: [{ model: this.ctx.model.Menu }],
          }],
        }],
        where: { username: params.username, password: params.password },
      });
      if (!result) {
        return { errorcode: 4, message: '密码不正确' };
      }
      const token = await ctx.service.users.generateToken(result.dataValues);
      exitCaptcha.update({ available: 0 });
      return await result.update({
        ip: ctx.request.ip,
        lastLogin: moment().format('YYYY-MM-DD HH:mm:ss'),
        token,
      });
    } catch (error) {
      ctx.logger.error('用户登录异常:', error);
      return { errorcode: 500, message: '登录失败', error: error.message };
    }
  }

  async loginWithPhone(params) {
    const { ctx, app } = this;
    // 建议：加限流防爆破
    if (!params.phone || !params.smscode) {
      return { errorcode: 100, message: '参数不完整' };
    }
    try {
      // 验证码校验
      const exitCode = await ctx.service.verifications.verificationCode({
        target: params.phone,
        code: params.smscode,
        type: 3,
      });
      if (exitCode) {
        const exitUser = await ctx.model.User.findOne({ where: { phone: params.phone } });
        if (!exitUser) {
          const transaction = await ctx.model.transaction();
          try {
            // 创建用户
            const defaultRole = await ctx.model.Role.findOne({ where: { isDefault: 1 } });
            const res_user = await ctx.model.User.create({
              id: uuid.v4(),
              homeUrl: 'admin-welcome',
              from: 'web',
              phone: params.phone,
              username: params.phone,
              roleId: defaultRole.code,
              ...params,
            }, { transaction });
            const token = await ctx.service.users.generateToken(res_user.dataValues);
            await res_user.update({
              ip: ctx.request.ip,
              token,
              lastLogin: moment().format('YYYY-MM-DD HH:mm:ss'),
            }, { transaction });
            await exitCode.update({ available: 0 }, { transaction });
            await transaction.commit();
            return await ctx.model.User.findOne({
              where: { id: res_user.id },
              include: [{
                model: this.ctx.model.Role,
                attributes: [ 'code', 'name' ],
                include: [{
                  model: this.ctx.model.RolePermission,
                  include: [{ model: this.ctx.model.Permission }],
                }, {
                  model: this.ctx.model.RoleMenu,
                  include: [{ model: this.ctx.model.Menu }],
                }],
              }],
            });
          } catch (e) {
            await transaction.rollback();
            ctx.logger.error('手机号注册事务回滚:', e);
          }
        } else {
          if (exitUser.isBlock == 1) {
            return { errorcode: 2, message: '用户已停用' };
          }
          const token = await ctx.service.users.generateToken(exitUser.dataValues);
          await exitCode.update({ available: 0 });
          await exitUser.update({
            ip: ctx.request.ip,
            lastLogin: moment().format('YYYY-MM-DD HH:mm:ss'),
            token,
          });
          return await ctx.model.User.findOne({
            where: { id: exitUser.id },
            include: [{
              model: this.ctx.model.Role,
              attributes: [ 'code', 'name' ],
              include: [{
                model: this.ctx.model.RolePermission,
                include: [{ model: this.ctx.model.Permission }],
              }, {
                model: this.ctx.model.RoleMenu,
                include: [{ model: this.ctx.model.Menu }],
              }],
            }],
          });
        }
      } else {
        return { errorcode: 4, message: '验证码错误或已使用过或已过期' };
      }
    } catch (error) {
      ctx.logger.error('手机号登录异常:', error);
      return { errorcode: 500, message: '登录失败', error: error.message };
    }
  }

  async loginWithDevice(params) {
    const { ctx, app } = this;
    // todo: 手机端无法给出唯一的设备id
    if (!params.device) {
      return { errorcode: 100, message: '参数不完整' };
    }
    try {
      let exitUser = await ctx.model.User.findOne({ where: { username: params.device } });
      if (!exitUser) {
        exitUser = await ctx.model.User.create({
          id: uuid.v4(),
          username: params.device,
          roleId: 'supervip',
          from: 'device',
          homeUrl: 'app-aggregation',
          isBlock: false,
        });
      }
      if (!exitUser) {
        return { errorcode: 1, message: '用户名不存在' };
      }
      if (exitUser.isBlock == 1) {
        return { errorcode: 2, message: '用户已停用' };
      }
      const token = await ctx.service.users.generateToken(exitUser.dataValues);
      await exitUser.update({
        ip: ctx.request.ip,
        lastLogin: moment().format('YYYY-MM-DD HH:mm:ss'),
        token,
      });
      return await ctx.model.User.findOne({
        where: { id: exitUser.id },
        include: [{
          model: this.ctx.model.Role,
          attributes: [ 'code', 'name' ],
          include: [{
            model: this.ctx.model.RolePermission,
            include: [{ model: this.ctx.model.Permission }],
          }, {
            model: this.ctx.model.RoleMenu,
            include: [{ model: this.ctx.model.Menu }],
          }],
        }],
      });
    } catch (error) {
      ctx.logger.error('设备登录异常:', error);
      return { errorcode: 500, message: '登录失败', error: error.message };
    }
  }

  /**
   * 修改 用户密码
   * @param params
   */
  async updateUserPassword(params) {
    const { ctx, app } = this;
    const { password, email, captcha } = params;
    if (!password || !email || !captcha) {
      return { errorcode: 100, message: '参数不完整' };
    }
    try {
      const user = await ctx.model.User.findOne({ where: { email } });
      if (user) {
        // 验证码校验
        const verificationCode = await ctx.service.verifications.verificationCode({
          target: email,
          code: captcha,
          type: 2,
        });
        if (verificationCode) {
          const password_new = await ctx.helper.tools.saltPassword(password);
          password_new.password += password_new.salt;
          const res = await ctx.model.User.update(
            { password: password_new.password },
            { where: { email } }
          );
          await ctx.model.Verification.update(
            { available: 0 },
            { where: { target: email } }
          );
          return res;
        }
        return { errorcode: 1, message: '验证码错误或已使用过' };
      }
      return { errorcode: 2, message: '邮箱不存在' };
    } catch (error) {
      ctx.logger.error('修改密码异常:', error);
      return { errorcode: 500, message: '修改密码失败', error: error.message };
    }
  }

  async update(id, params) {
    const { ctx } = this;
    // 用户的email和username不可修改
    delete params.email;
    delete params.username;
    delete params.password;
    delete params.id;
    try {
      await ctx.model.User.update({ ...params }, {
        where: { id },
        individualHooks: true,
      });
      return true;
    } catch (error) {
      ctx.logger.error('用户信息更新异常:', error);
      return false;
    }
  }
  async destroy(id) {
    try {
      await this.ctx.model.User.destroy({ where: { id } });
      return {};
    } catch (error) {
      this.ctx.logger.error('用户删除异常:', error);
      return false;
    }
  }

  async userexist(params) {
    try {
      const user = await this.ctx.model.User.findOne({ where: { username: params.username } });
      if (user) {
        return 1;
      }
      return 0;
    } catch (error) {
      this.ctx.logger.error('用户存在性校验异常:', error);
      return 0;
    }
  }

  async show(id) {
    try {
      const user = await this.ctx.model.User.findOne({
        where: { id },
        attributes: { exclude: [ 'password', 'createdAt', 'updatedAt', 'deletedAt' ] },
        include: [{
          model: this.ctx.model.Role,
          attributes: [ 'code', 'name' ],
          include: [{
            model: this.ctx.model.RolePermission,
            include: [{ model: this.ctx.model.Permission }],
          }, {
            model: this.ctx.model.RoleMenu,
            include: [{ model: this.ctx.model.Menu }],
          }],
        }],
      });
      return user;
    } catch (error) {
      this.ctx.logger.error('用户详情查询异常:', error);
      return null;
    }
  }

  /**
   * 是否存在此用户字段
   * @param params
   */
  async existsUserUniqueFields(params) {
    const { ctx } = this;
    const { username, email, phone } = params;
    const where = {};
    where[Op.or] = [];
    username ? where[Op.or].push({ username }) : null;
    email ? where[Op.or].push({ email }) : null;
    phone ? where[Op.or].push({ phone }) : null;
    try {
      const user = await ctx.model.User.findOne({ where, attributes: { exclude: [ 'password' ] } });
      if (user) {
        return 1;
      }
      return 0;
    } catch (error) {
      ctx.logger.error('唯一性校验异常:', error);
      return 0;
    }
  }

  async decryptPassword(password) {
    const { ctx } = this;
    try {
      const { rsaPrivateKey } = await ctx.model.Configuration.findOne({ where: { name: 'register' } });
      const privateKey = new NodeRSA(rsaPrivateKey);
      privateKey.setOptions({ encryptionScheme: 'pkcs1' });
      return privateKey.decrypt(password, 'utf8');
    } catch (e) {
      ctx.logger.error('密码解密异常:', e);
      return null;
    }
  }

  async findAll(params) {
    const { pageIndex, pageSize, role, text } = params;
    let where = {};
    if (role) {
      where = Object.assign(where, { role });
    }
    if (text) {
      where = Object.assign(where, { username: { [Op.like]: `%${text}%` } });
    }
    try {
      const users = await this.ctx.model.User.findAndCountAll({
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        where,
        include: [{
          model: this.ctx.model.Role,
          attributes: [ 'code', 'name' ],
        }],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
      return users;
    } catch (error) {
      this.ctx.logger.error('用户列表查询异常:', error);
      return { count: 0, rows: [] };
    }
  }
}

module.exports = UsersService;
