'use strict';

const BaseCacheService = require('./base/cache');
const { Op } = require('sequelize');

class GroupsService extends BaseCacheService {

  // 自定义缓存配置 - 针对股票分组服务的实时性要求
  get cacheConfig() {
    return {
      // 继承基础配置
      ...super.cacheConfig,

      // 股票分组实时数据 - 需要快速响应
      groupRealtime: {
        ttl: 30, // 30秒，保证实时性
        prefix: 'groups:realtime:',
        description: '股票分组实时数据缓存',
        strategy: 'aggressive', // 激进策略：频繁更新
      },

      // 分组列表数据 - 中等实时性
      groupList: {
        ttl: 60, // 1分钟
        prefix: 'groups:list:',
        description: '分组列表数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },

      // 分组名称列表 - 较低实时性
      groupNames: {
        ttl: 300, // 5分钟
        prefix: 'groups:names:',
        description: '分组名称列表缓存',
        strategy: 'conservative', // 保守策略：较少更新
      },

      // 分组统计数据 - 较长缓存时间
      groupStats: {
        ttl: 600, // 10分钟
        prefix: 'groups:stats:',
        description: '分组统计数据缓存',
        strategy: 'conservative',
      },

      // 分组搜索数据 - 中等缓存时间
      groupSearch: {
        ttl: 180, // 3分钟
        prefix: 'groups:search:',
        description: '分组搜索数据缓存',
        strategy: 'balanced',
      },

      // 分组详情数据 - 中等缓存时间
      groupDetail: {
        ttl: 120, // 2分钟
        prefix: 'groups:detail:',
        description: '分组详情数据缓存',
        strategy: 'balanced',
      },
    };
  }

  // 优化后的添加/移除股票到分组 - 实时操作
  async arStock(id, params) {
    const { ctx } = this;
    const { code, type } = params;

    await ctx.model.GroupStock.destroy({
      where: {
        code,
      },
    });

    if (type === 'add') {
      await ctx.model.GroupStock.findOrCreate({
        where: {
          groupId: id,
          code,
        },
      });
    }

    // 清除相关缓存
    await this.clearRelatedCache('groups', {
      id,
      code,
      type: 'groupRealtime',
    });

    return {};
  }

  // 优化后的更新分组 - 实时操作
  async update(id, params) {
    const { ctx } = this;
    await ctx.model.Group.update({ ...params }, {
      where: {
        id,
      },
    });

    // 清除相关缓存
    await this.clearRelatedCache('groups', {
      id,
      type: 'groupRealtime',
    });

    return true;
  }

  // 优化后的创建分组 - 实时操作
  async create(name) {
    const group = await this.ctx.model.Group.findOrCreate({
      where: {
        name,
      },
    });

    // 清除相关缓存
    await this.clearRelatedCache('groups', {
      type: 'groupList',
    });

    return group;
  }

  // 优化后的根据ID查找分组 - 实时数据
  async findGroupById(id) {
    return await this.withCache('groupDetail', 'findGroupById', { id }, async () => {
      const {
        Stock,
        Source,
        GroupStock,
        Report,
        Research,
        NewsStock,
        News,
      } = this.ctx.model;

      const groups = await this.ctx.model.Group.findAll({
        where: {
          id,
        },
        include: {
          model: GroupStock,
          include: [{
            model: Stock,
            attributes: [ 'score', 'emv', 'macd', 'star', 'code', 'hot', 'remarks', 'keyword', 'rate', 'white', 'tags', 'isTup', 'isHeavyVolume', 'isBottomInversion', 'momentum', 'limit' ],
            include: [{
              model: Source,
              attributes: [ 'displayName', 'code', 'f3', 'f20' ],
            }, {
              model: Report,
              limit: 10,
              order: [[ 'announcementId', 'DESC' ]],
            }, {
              model: Research,
              limit: 10,
              order: [[ 'id', 'DESC' ]],
            },
            {
              model: NewsStock,
              order: [[ 'newsId', 'DESC' ]],
              include: {
                model: News,
              },
            }],
          }],
        },
      });

      // 按市值排序
      if (groups[0]) {
        groups[0].groupStocks = groups[0].groupStocks.sort((a, b) => {
          return b.stock.source.f20 - a.stock.source.f20;
        });
      }

      return groups[0];
    });
  }

  // 优化后的查找所有分组 - 分钟级数据
  async findAllGroup(params) {
    return await this.withCache('groupList', 'findAllGroup', params, async () => {
      const {
        Stock,
        Source,
        Group,
        GroupStock,
        Report,
        Research,
        News,
        Post,
        NewsStock,
        PostStock,
      } = this.ctx.model;

      if (params.onlyname == 1) {
        const groups = await this.ctx.model.Group.findAll({
          limit: 100,
        });

        return groups;
      }

      const groups = await this.ctx.model.Group.findAll({
        // where: {
        //   id: {
        //     [Op.ne]: 1, // 排除一个特定id
        //   }
        // },
        order: [[ 'id', 'DESC' ]],
        include: {
          model: GroupStock,
          include: [{
            model: Stock,
            attributes: [
              'code',
              'white',
              'star',
              // 'tags',
              'rate',
              'ret20',
              // 'decisionPercent',
              'remarks',
              'keyword',
              'score',
              // 'emv',
              // 'macd',
              // 'isBottomInversion',
              // 'isTup', 'isHeavyVolume', 'momentum', 'limit'
            ],
            include: [{
              model: Source,
              attributes: [ 'displayName', 'code', 'f3', 'f20', 'f18' ],
            },
            // {
            //   model: Report,
            //   limit: 10,
            //   order: [[ 'announcementId', 'DESC' ]],
            // }, {
            //   model: Research,
            //   limit: 10,
            //   order: [[ 'id', 'DESC' ]],
            // },
            // {
            //   model: NewsStock,
            //   order: [[ 'newsId', 'DESC' ]],
            //   include: {
            //     model: News,
            //   }
            // },{
            //   model: PostStock,
            //   order: [[ 'id', 'DESC' ]],
            //   include: {
            //     model: Post,
            //   }
            // },
            {
              model: GroupStock,
              include: {
                model: Group,
              },
            },
            ],
          }],
        },
      });

      groups.map(item => {
        return item.groupStocks.sort((a, b) => {
          return b.stock.source.f20 - a.stock.source.f20;
        });
      });

      return groups;
    });
  }

  // 优化后的查找所有分组名称 - 小时级数据
  async findAllGroupName() {
    return await this.withCache('groupNames', 'findAllGroupName', {}, async () => {
      const groups = await this.ctx.model.Group.findAll({
        limit: 100,
      });

      return groups;
    });
  }

  // 优化后的删除分组 - 实时操作
  async destroy(id) {
    await this.ctx.model.transaction(async () => {

      await this.ctx.model.Group.destroy({
        where: {
          id,
        },
      });

      await this.ctx.model.GroupStock.destroy({
        where: {
          groupId: id,
        },
      });

    });

    // 清除相关缓存
    await this.clearRelatedCache('groups', { id });

    return {};
  }

  // 高级缓存选项示例 - 强制刷新分组数据
  async refreshGroupData(id, options = {}) {
    return await this.withCache('groupDetail', 'findGroupById', { id }, async () => {
      return await this.findGroupById(id);
    }, {
      forceRefresh: options.forceRefresh || false,
      customTtl: options.customTtl || null,
    });
  }

  // 批量获取分组数据
  async getBatchGroupData(groupIds) {
    const promises = groupIds.map(id =>
      this.findGroupById(id)
    );

    return await Promise.all(promises);
  }

  // 获取分组统计信息
  async getGroupStats() {
    return await this.withCache('groupStats', 'getGroupStats', {}, async () => {
      const totalGroups = await this.ctx.model.Group.count();
      const totalStocks = await this.ctx.model.GroupStock.count();

      return {
        totalGroups,
        totalStocks,
        averageStocksPerGroup: totalGroups > 0 ? Math.round(totalStocks / totalGroups) : 0,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 搜索分组
  async searchGroups(keyword) {
    return await this.withCache('groupSearch', 'searchGroups', { keyword }, async () => {
      const groups = await this.ctx.model.Group.findAll({
        where: {
          name: {
            [Op.like]: `%${keyword}%`,
          },
        },
        limit: 20,
      });

      return groups;
    });
  }

  // 获取服务缓存统计
  async getServiceStats() {
    const stats = await this.getCacheStats('groups');
    const health = await this.getCacheHealth('groups');

    return {
      stats,
      health,
      timestamp: new Date().toISOString(),
    };
  }

  // 清理过期缓存
  async cleanupServiceCache() {
    await this.cleanupExpiredCache('groups');
    return { success: true, message: 'Groups cache cleaned up' };
  }

  // 批量刷新缓存
  async refreshServiceCache(operations) {
    await this.batchRefreshCache(operations);
    return { success: true, message: 'Groups cache refreshed' };
  }

  // 自定义缓存策略 - 针对分组数据的特殊处理
  async applyCacheStrategy(cacheKey, strategy, data) {
    // 调用父类策略
    await super.applyCacheStrategy(cacheKey, strategy, data);

    // 添加分组特定的策略逻辑
    if (strategy === 'aggressive') {
      // 对于激进策略，预加载相关分组数据
      await this.aggressiveGroupWarmup(cacheKey, data);
    } else if (strategy === 'conservative') {
      // 对于保守策略，压缩分组数据
      await this.conservativeGroupCompression(cacheKey, data);
    }
  }

  // 激进策略预热 - 预加载相关分组数据
  async aggressiveGroupWarmup(cacheKey, data) {
    try {
      // 如果是分组详情数据，预加载相关股票数据
      if (data && data.groupStocks && data.groupStocks.length > 0) {
        const stockCodes = data.groupStocks.map(gs => gs.stock.code);
        const relatedKey = `groups:related:${data.id}`;

        await this.app.redis.setex(relatedKey, 60, JSON.stringify({
          relatedStocks: stockCodes,
          timestamp: new Date().toISOString(),
        }));
      }
      this.ctx.logger.info(`Aggressive group warmup for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Aggressive group warmup error:', error);
    }
  }

  // 保守策略压缩 - 压缩分组数据
  async conservativeGroupCompression(cacheKey, data) {
    try {
      // 对大型分组数据进行压缩存储
      if (data && JSON.stringify(data).length > 5000) {
        const compressedKey = `${cacheKey}:compressed`;
        const compressedData = {
          compressed: true,
          originalSize: JSON.stringify(data).length,
          compressedSize: Math.floor(JSON.stringify(data).length * 0.7), // 模拟压缩
          timestamp: new Date().toISOString(),
        };

        await this.app.redis.setex(compressedKey, 3600, JSON.stringify(compressedData));
      }
      this.ctx.logger.info(`Conservative group compression for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Conservative group compression error:', error);
    }
  }
}

module.exports = GroupsService;
