'use strict';

const { Service } = require('egg');

/**
 * ConfigurationsService
 * Business logic service
 */
class ConfigurationsService extends Service {
  async update(payload) {
    const { ctx } = this;
    try {
      return await ctx.model.Configuration.update(payload, {
        where: { id: payload.id },
      });
    } catch (error) {
      ctx.logger.error('Error updating configuration:', error);
      return {
        success: false,
        message: 'Failed to update configuration',
        error: error.message,
      };
    }
  }

  async findRsaPublicKey(id) {
    const { ctx } = this;
    try {
      return await ctx.model.Configuration.findOne({
        where: { id },
        attributes: { exclude: [ 'rsa_private_key' ] },
      });
    } catch (error) {
      ctx.logger.error('Error finding RSA public key:', error);
      return {
        success: false,
        message: 'Failed to find RSA public key',
        error: error.message,
      };
    }
  }

  async getConfig(name) {
    const { ctx } = this;
    try {
      return await ctx.model.Configuration.findOne({
        where: {
          name,
        },
      });
    } catch (error) {
      ctx.logger.error('Error getting configuration:', error);
      return {
        success: false,
        message: 'Failed to get configuration',
        error: error.message,
      };
    }
  }
}

module.exports = ConfigurationsService;
