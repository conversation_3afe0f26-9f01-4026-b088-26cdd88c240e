'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class MenusService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'menus:minute:',
        description: '菜单列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'menus:detail:',
        description: '菜单详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 新增，写操作后清除缓存
  async create(params) {
    const one = await this.ctx.model.Menu.findOne({ where: { code: params.code } });
    if (one) {
      await this.update(params.code, params);
    } else {
      await this.ctx.model.Menu.create({ ...params });
    }
    await this.clearRelatedCache('menus');
    return true;
  }

  // 更新，写操作后清除缓存
  async update(code, params) {
    await this.ctx.model.Menu.update({ ...params }, { where: { code } });
    await this.clearRelatedCache('menus');
    return true;
  }

  // 删除，写操作后清除缓存
  async destroy(code) {
    await this.ctx.model.Menu.destroy({ where: { code } });
    await this.clearRelatedCache('menus');
    return {};
  }

  // 详情，带缓存
  async show(code) {
    return await this.withCache('detail', 'show', { code }, async () => {
      return await this.ctx.model.Menu.findOne({ where: { code } });
    });
  }

  // 列表，带缓存
  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { pageIndex, pageSize } = params;
      const where = {};
      return await this.ctx.model.Menu.findAndCountAll({
        order: [[ 'sort', 'ASC' ]],
        distinct: true,
        where,
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }
}

module.exports = MenusService;
