'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class PermissionsService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'permissions:minute:',
        description: '权限列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'permissions:detail:',
        description: '权限详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 新增，写操作后清除缓存
  async create(params) {
    const one = await this.ctx.model.Permission.findOne({ where: { code: params.code } });
    if (one) {
      await this.update(params.code, params);
    } else {
      await this.ctx.model.Permission.create({ ...params });
    }
    await this.clearRelatedCache('permissions');
    return true;
  }

  // 更新，写操作后清除缓存
  async update(code, params) {
    await this.ctx.model.Permission.update({ ...params }, { where: { code } });
    await this.clearRelatedCache('permissions');
    return true;
  }

  // 删除，写操作后清除缓存
  async destroy(code) {
    await this.ctx.model.Permission.destroy({ where: { code } });
    await this.clearRelatedCache('permissions');
    return {};
  }

  // 详情，带缓存
  async show(code) {
    return await this.withCache('detail', 'show', { code }, async () => {
      return await this.ctx.model.Permission.findOne({ where: { code } });
    });
  }

  // 列表，带缓存
  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { pageIndex, pageSize } = params;
      const where = {};
      return await this.ctx.model.Permission.findAndCountAll({
        order: [[ 'mark', 'DESC' ]],
        distinct: true,
        where,
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }
}

module.exports = PermissionsService;
