'use strict';

const BaseCacheService = require('./base/cache');

class SortsService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'sorts:minute:',
        description: '分类列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'sorts:detail:',
        description: '分类详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 排序变更，写操作后清除缓存
  async sortsChange(params) {
    const { SortSec } = this.ctx.model;
    const { list } = params;
    await this.ctx.model.transaction(async () => {
      for (let index = 0; index < list.length; index++) {
        const element = list[index];
        await SortSec.update({ sortId: element.sortId }, { where: { id: element.id } });
      }
    });
    await this.clearRelatedCache('sorts');
    return true;
  }

  // 列表，带缓存
  async findAll() {
    return await this.withCache('minute', 'findAll', {}, async () => {
      const { Sort, SortSec } = this.ctx.model;
      return await Sort.findAll({
        order: [[ 'id', 'ASC' ], [ SortSec, 'sortId', 'ASC' ]],
        include: {
          model: SortSec,
          attributes: [ 'sortId', 'id', 'parentId', 'name', 'level' ],
        },
      });
    });
  }

  // 更新，写操作后清除缓存
  async update(id, params) {
    const { ctx } = this;
    const { name, level } = params;
    let result;
    if (level === '0') {
      result = await ctx.model.Sort.update({ ...params }, { where: { id } });
    } else {
      result = await ctx.model.SortSec.update({ ...params }, { where: { id } });
    }
    await this.clearRelatedCache('sorts');
    return result;
  }

  // 新增，写操作后清除缓存
  async create(params) {
    const { Sort, SortSec } = this.ctx.model;
    const { name, parentId, level, sortId } = params;
    let result;
    if (level === '0' && !parentId) {
      result = await Sort.findOrCreate({ where: { name, level } });
    } else {
      result = await SortSec.create({ name, parentId, level, sortId });
    }
    await this.clearRelatedCache('sorts');
    return result;
  }

  // 删除，写操作后清除缓存
  async destroy(id, params) {
    const { Sort, SortSec, Stock } = this.ctx.model;
    let result;
    if (params.level === '0') {
      const sort = await Sort.findByPk(id);
      if (sort) {
        await sort.destroy();
        await SortSec.destroy({ where: { parentId: sort.id } });
        await Stock.update({ firstLevelId: 0, secondLevelId: 0 }, { where: { firstLevelId: id } });
        result = sort;
      }
    } else {
      result = await SortSec.destroy({ where: { id } });
      await Stock.update({ firstLevelId: 0, secondLevelId: 0 }, { where: { secondLevelId: id } });
    }
    await this.clearRelatedCache('sorts');
    return result;
  }
}

module.exports = SortsService;
