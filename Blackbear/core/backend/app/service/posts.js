'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const moment = require('moment');

class PostsService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'posts:minute:',
        description: '文章分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'posts:hourly:',
        description: '文章小时级数据',
        strategy: 'conservative',
      },
      daily: {
        ttl: 1800,
        prefix: 'posts:daily:',
        description: '文章日级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'posts:statistics:',
        description: '文章统计数据',
        strategy: 'conservative',
      },
      search: {
        ttl: 90,
        prefix: 'posts:search:',
        description: '文章搜索数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'posts:detail:',
        description: '文章详情数据',
        strategy: 'balanced',
      },
    };
  }

  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      return await this.ctx.model.Post.findOne({
        where: { id },
        include: [
          { model: this.ctx.model.Dictionary },
          { model: this.ctx.model.PostTag, include: [{ model: this.ctx.model.Tag, attributes: [ 'name', 'id' ] }] },
          { model: this.ctx.model.PostStock, include: [{ model: this.ctx.model.Stock, attributes: [ 'code' ], include: [{ model: this.ctx.model.Source, attributes: [ 'displayName', 'code', 'f3' ] }] }] },
          { model: this.ctx.model.User },
        ],
      });
    });
  }

  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { pageIndex, pageSize } = params;
      return await this.ctx.model.Post.findAndCountAll({
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        include: [{ model: this.ctx.model.Dictionary }],
        where: {
          status: { [Op.in]: [ 'published', 'draft' ] },
        },
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  async getDailystory(params) {
    return await this.withCache('minute', 'getDailystory', params, async () => {
      const { pageIndex, pageSize } = params;
      return await this.ctx.model.Post.findAndCountAll({
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        include: [{ model: this.ctx.model.Dictionary }],
        where: {
          status: { [Op.in]: [ 'published', 'draft' ] },
        },
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  // 写操作后清除缓存
  async create(params) {
    let post = null;
    await this.ctx.model.transaction(async () => {
      post = await this.ctx.model.Post.create({
        ...params,
        userId: params.userId ? params.userId : (this.ctx.state && this.ctx.state.user) ? this.ctx.state.user.id : '',
      });
      if (params.codes) {
        const codes = params.codes.split(',');
        for (let index = 0; index < codes.length; index++) {
          const code = codes[index];
          await this.ctx.model.PostStock.findOrCreate({ where: { code, postId: post.id } });
        }
      }
      if (params.tags) {
        const tags = params.tags.split(',');
        for (let index = 0; index < tags.length; index++) {
          const tag = tags[index];
          await this.ctx.model.PostTag.findOrCreate({ where: { tagId: tag, postId: post.id } });
        }
      }
    });
    await this.clearRelatedCache('posts', { method: 'create', id: post.id });
    return post;
  }

  async update(id, params) {
    await this.ctx.model.transaction(async () => {
      await this.ctx.model.Post.update({ ...params }, { where: { id }, individualHooks: true });
      await this.ctx.model.PostStock.destroy({ force: true, where: { postId: id } });
      await this.ctx.model.PostTag.destroy({ force: true, where: { postId: id } });
      if (params.tags) {
        const tags = params.tags.split(',');
        for (let index = 0; index < tags.length; index++) {
          const tag = tags[index];
          await this.ctx.model.PostTag.findOrCreate({ where: { tagId: tag, postId: id } });
        }
      }
      if (params.codes) {
        const codes = params.codes.split(',');
        for (let index = 0; index < codes.length; index++) {
          const code = codes[index];
          await this.ctx.model.PostStock.findOrCreate({ where: { code, postId: id } });
        }
      }
    });
    await this.clearRelatedCache('posts', { method: 'update', id });
    return true;
  }

  async destroy(id) {
    await this.ctx.model.transaction(async () => {
      await this.ctx.model.Post.destroy({ where: { id }, individualHooks: true });
      await this.ctx.model.PostStock.destroy({ force: true, where: { postId: id } });
      await this.ctx.model.PostTag.destroy({ force: true, where: { postId: id } });
    });
    await this.clearRelatedCache('posts', { method: 'destroy', id });
    return {};
  }

  async views(id) {
    const post = await this.ctx.model.Post.findByPk(id);
    await post.update({
      views: post.views + 1,
    });

    return true;
  }

  async getRandomSaying() {
    const post = await this.ctx.model.Post.findOne({
      where: {
        category: 'saying',
        status: 'published',
      },
      order: [
        Sequelize.fn('RAND'),
      ],
    });

    return post;
  }

  async getTodayReview() {
    const post = await this.ctx.model.Post.findOne({
      where: {
        category: 'dailystory',
        time: moment().format('YYYYMMDD'),
      },
    });

    return post;
  }

  async findTodayPost() {
    const posts = await this.ctx.model.Post.findAndCountAll({
      order: [[ 'id', 'ASC' ]],
      distinct: true,
      include: [{
        model: this.ctx.model.Dictionary,
      }],
      where: {
        status: 'published',
        time: moment().format('YYYYMMDD'),
        category: {
          [Op.in]: [ 'review', 'weekly', 'market', 'dailystory' ],
        },
      },
      offset: 0,
      limit: 10,
    });

    return posts.rows;
  }


  async findTodayDailystory() {
    const posts = await this.ctx.model.Post.findAndCountAll({
      order: [[ 'id', 'DESC' ]],
      distinct: true,
      include: [{
        model: this.ctx.model.Dictionary,
      }],
      where: {
        status: 'published',
        time: moment().format('YYYYMMDD'),
        category: {
          [Op.in]: [ 'dailystory' ],
        },
      },
      offset: 0,
      limit: 10,
    });

    return posts.rows;
  }

  async getHotPosts() {
    const posts = await this.ctx.model.Post.findAll({
      order: [[ 'views', 'DESC' ]],
      distinct: true,
      attributes: [ 'title', 'id' ],
      where: {
        status: 'published',
        category: {
          [Op.in]: [ 'meetingminutes', 'weekly', 'original', 'report', 'collection' ],
        },
      },
      offset: 0,
      limit: 10,
    });

    return posts;
  }

  async getLatestView() {
    const posts = await this.ctx.model.Post.findAll({
      order: [[ 'id', 'DESC' ]],
      attributes: [ 'title', 'id', 'createdAt', 'content' ],
      where: {
        status: 'published',
        category: {
          [Op.in]: [ 'dailystory' ],
        },
      },
      offset: 0,
      limit: 10,
    });

    return posts;
  }

  async getAll(num) {
    const { ctx } = this;

    const where = {
      title: {
        [Op.ne]: null,
      },
      status: 'published',
    };

    let query = {
      order: [[ 'id', 'DESC' ]],
      distinct: true,
      where,
      attributes: [ 'id', 'title', 'updatedAt' ],
    };

    if (num) {
      query = Object.assign(query, {
        offset: 0,
        limit: num,
      });
    }

    const posts = await ctx.model.Post.findAll(query);


    return posts;
  }
}

module.exports = PostsService;
