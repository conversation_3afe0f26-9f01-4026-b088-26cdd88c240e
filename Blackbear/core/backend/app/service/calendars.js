'use strict';

const moment = require('moment');
const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class CalendarsService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'calendars:minute:',
        description: '日历分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'calendars:hourly:',
        description: '日历小时级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'calendars:statistics:',
        description: '日历统计数据',
        strategy: 'conservative',
      },
      detail: {
        ttl: 180,
        prefix: 'calendars:detail:',
        description: '日历详情数据',
        strategy: 'balanced',
      },
    };
  }

  async find(params) {
    return await this.withCache('minute', 'find', params, async () => {
      const {
        Calendar,
        Event,
        Stock,
        Source,
        EventStock,
        EventTag,
        CalendarEvent,
        Tag,
        StockDaliy,
        Post,
        TagStock,
      } = this.ctx.model;
      const { pageIndex, pageSize, date, type } = params;
      const pi = pageIndex ? pageIndex : 1;
      const ps = pageSize ? pageSize : 60;
      const d = date ? date : moment().format('YYYYMMDD');
      const now = moment(d).format('YYYYMMDD');
      const ago = moment(d).subtract(30, 'days').format('YYYYMMDD');
      const later = moment(d).add(30, 'days').format('YYYYMMDD');
      let date_limit = [ ago, later ];
      if (type && type === 'all') {
        date_limit = [ ago, later ];
      } else if (type === 'later') {
        date_limit = [ now, later ];
      } else if (type === 'before') {
        date_limit = [ ago, now ];
      } else if (type === 'currentday') {
        date_limit = [ now, now ];
      }
      const calendar = Calendar.findAll({
        where: { date: { [Op.between]: date_limit } },
        attributes: [ 'date', 'mark', 'danger' ],
        order: [[ 'date', 'ASC' ]],
        offset: parseInt(pi - 1) * parseInt(ps),
        limit: parseInt(ps),
        include: {
          model: CalendarEvent,
          attributes: [ 'id', 'calendarId', 'eventId' ],
          include: [{
            model: Event,
            attributes: [ 'id', 'type', 'content', 'description', 'category' ],
            include: [{
              model: EventTag,
              attributes: [ 'id' ],
              include: { model: Tag, attributes: [ 'id', 'code', 'name' ] },
            }, {
              model: EventStock,
              attributes: [ 'id' ],
              include: {
                model: Stock,
                attributes: [ 'code' ],
                include: {
                  model: Source,
                  attributes: [ 'displayName', 'code', 'f3', 'f127', 'f149' ],
                },
              },
            }],
          }],
        },
      });
      const stockDaliy = StockDaliy.findAll({
        order: [[ 'date', 'ASC' ]],
        where: { date: { [Op.between]: date_limit } },
      });
      const postlist = Post.findAll({
        order: [[ 'time', 'ASC' ]],
        attributes: [ 'id', 'category', 'time', 'content', 'createdAt' ],
        where: {
          status: 'published',
          time: { [Op.between]: date_limit },
        },
      });
      const promiseList = [ calendar, stockDaliy, postlist ];
      const [ calendarData, daliyData, postData ] = await Promise.all(promiseList);
      return { calendarData, daliyData, postData };
    });
  }

  async create(params) {
    const { date, frequency } = params;
    const promiseList = [ this.ctx.model.Calendar.findOrCreate({ where: { date } }) ];
    if (frequency === 'year') {
      for (let y = 1; y < 30; y++) {
        const current = moment(date);
        const toDate = current.add(y, 'years');
        if (toDate) {
          promiseList.push(this.ctx.model.Calendar.findOrCreate({ where: { date: toDate.format('YYYYMMDD') } }));
        }
      }
    }
    const calendars = await Promise.all(promiseList);
    await this.clearRelatedCache('calendars', { method: 'create', date });
    return calendars;
  }

  async update(id, params) {
    const { type } = params;
    const { ctx } = this;
    const cal = await ctx.model.Calendar.findOrCreate({ where: { date: id } });
    if (type == 'mark') {
      await cal[0].update({ mark: !cal[0].get('mark') });
    } else if (type == 'danger') {
      await cal[0].update({ danger: !cal[0].get('danger') });
    }
    await this.clearRelatedCache('calendars', { method: 'update', id });
    return true;
  }
}

module.exports = CalendarsService;
