'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class PositionsService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'positions:minute:',
        description: '持仓列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'positions:detail:',
        description: '持仓详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 新增，写操作后清除缓存
  async create(params) {
    const position = await this.ctx.model.Position.create({ ...params });
    await this.clearRelatedCache('positions');
    return position;
  }

  // 更新，写操作后清除缓存
  async update(id, params) {
    await this.ctx.model.Position.update({ ...params }, { where: { id } });
    await this.clearRelatedCache('positions');
    return true;
  }

  // 删除，写操作后清除缓存
  async destroy(id) {
    await this.ctx.model.Position.destroy({ force: true, where: { id } });
    await this.clearRelatedCache('positions');
    return {};
  }

  // 详情，带缓存
  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      return await this.ctx.model.Position.findOne({ where: { id } });
    });
  }

  // 列表，带缓存
  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { pageIndex, pageSize } = params;
      const where = {};
      return await this.ctx.model.Position.findAll({
        order: [[ 'code', 'ASC' ]],
        distinct: true,
        where,
        include: [{
          model: this.ctx.model.Source,
          attributes: [ 'code', 'displayName', 'f2', 'f3' ],
        }],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }
}

module.exports = PositionsService;
