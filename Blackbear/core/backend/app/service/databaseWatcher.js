'use strict';

const { Service } = require('egg');

/**
 * DatabaseWatcherService
 * Business logic service
 */
class DatabaseWatcherService extends Service {

  // 监听数据库变化并清除相关缓存
  async watchDatabaseChanges() {
    // 这里可以集成数据库的变更通知机制
    // 例如：MySQL的binlog、PostgreSQL的NOTIFY、或者定时检查

    this.ctx.logger.info('Database watcher started');

    // 启动定时检查任务
    this.startPeriodicCheck();
  }

  // 定时检查数据库变化
  startPeriodicCheck() {
    const checkInterval = 60000; // 1分钟检查一次

    setInterval(async () => {
      try {
        await this.checkForChanges();
      } catch (error) {
        this.ctx.logger.error('Error in periodic database check', error);
      }
    }, checkInterval);
  }

  // 检查数据库变化
  async checkForChanges() {
    const { Stock, Source, News, Report, Research } = this.ctx.model;

    try {
      // 检查各表的最后更新时间
      const checks = await Promise.all([
        this.checkTableChanges('stock', Stock),
        this.checkTableChanges('source', Source),
        this.checkTableChanges('news', News),
        this.checkTableChanges('report', Report),
        this.checkTableChanges('research', Research),
      ]);

      // 如果有变化，清除相关缓存
      for (const check of checks) {
        if (check.hasChanges) {
          // 根据表名调用对应的服务清除缓存
          switch (check.table) {
            case 'stock':
              await this.ctx.service.stocks.clearRelatedCache('stocks', { code: check.lastRecord?.code });
              break;
            case 'source':
              await this.ctx.service.sources.clearRelatedCache('sources', { code: check.lastRecord?.code });
              break;
            case 'news':
              await this.ctx.service.cacheManager.clearNewsCache({ newsId: check.lastRecord?.newsID, code: check.lastRecord?.code });
              break;
            case 'report':
              await this.ctx.service.cacheManager.clearReportCache({ announcementId: check.lastRecord?.announcementId, code: check.lastRecord?.secCode });
              break;
            case 'research':
              await this.ctx.service.cacheManager.clearResearchCache({ researchId: check.lastRecord?.id, code: check.lastRecord?.code });
              break;
            default:
              // 对于未知表，使用通用方法清除缓存
              await this.ctx.service.cacheManager.clearCacheByOperation({
                table: check.table,
                action: 'update',
                record: check.lastRecord,
              });
          }
        }
      }
    } catch (error) {
      this.ctx.logger.error('Error checking database changes', error);
    }
  }

  // 检查单个表的变化
  async checkTableChanges(tableName, Model) {
    try {
      // 获取最后一条记录
      const lastRecord = await Model.findOne({
        order: [[ 'updatedAt', 'DESC' ]],
        attributes: [ 'updatedAt' ],
      });

      if (!lastRecord) {
        return { table: tableName, hasChanges: false };
      }

      // 检查是否与上次记录的时间不同
      const cacheKey = `last_update:${tableName}`;
      const lastUpdateTime = await this.app.redis.get(cacheKey);

      if (lastUpdateTime !== lastRecord.updatedAt.toISOString()) {
        // 更新最后更新时间
        await this.app.redis.set(cacheKey, lastRecord.updatedAt.toISOString());

        return {
          table: tableName,
          hasChanges: true,
          lastRecord: lastRecord.toJSON(),
        };
      }

      return { table: tableName, hasChanges: false };
    } catch (error) {
      this.ctx.logger.error(`Error checking changes for table: ${tableName}`, error);
      return { table: tableName, hasChanges: false };
    }
  }

  // 手动触发缓存清除
  async triggerCacheClear(operation) {
    try {
      await this.ctx.service.cacheManager.clearCacheByOperation(operation);
      this.ctx.logger.info('Cache clear triggered manually', operation);
    } catch (error) {
      this.ctx.logger.error('Error triggering cache clear', error);
    }
  }

  // 获取数据库变化统计
  async getChangeStats() {
    try {
      const stats = {};
      const tables = [ 'stock', 'source', 'news', 'report', 'research' ];

      for (const table of tables) {
        const cacheKey = `last_update:${table}`;
        const lastUpdate = await this.app.redis.get(cacheKey);
        stats[table] = {
          lastUpdate: lastUpdate || 'Never',
          cacheKey,
        };
      }

      return stats;
    } catch (error) {
      this.ctx.logger.error('Error getting change stats', error);
      return {};
    }
  }
}

module.exports = DatabaseWatcherService;
