'use strict';

const { Service } = require('egg');
const axios = require('axios');

/**
 * BingService
 * Business logic service
 */
class BingService extends Service {
  async pushUrls(urls) {
    const { ctx, app } = this;
    const data = {
      host: ctx.app.config.webURL,
      key: ctx.app.config.bingIndexNowKey,
      keyLocation: `${ctx.app.config.webURL}/${ctx.app.config.bingIndexNowKey}.txt`,
      urlList: urls,
    };
    const options = {
      method: 'POST',
      headers: { 'content-type': 'application/json; charset=utf-8' },
      data,
      url: 'https://api.indexnow.org/indexnow',
    };
    try {
      const result = await axios(options);
      return result.data;
    } catch (error) {
      this.ctx.logger.error('Bing推送异常:', error);
      return { success: false, message: 'Bing推送失败', error: error.message };
    }
  }
}

module.exports = BingService;
