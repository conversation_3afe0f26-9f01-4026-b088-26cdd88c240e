'use strict';

const moment = require('moment');
const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const getValue = require('../utils/getValue');

class WorthsService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'worths:minute:',
        description: '净值分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'worths:hourly:',
        description: '净值小时级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'worths:statistics:',
        description: '净值统计数据',
        strategy: 'conservative',
      },
      detail: {
        ttl: 180,
        prefix: 'worths:detail:',
        description: '净值详情数据',
        strategy: 'balanced',
      },
    };
  }

  async getLast() {

    return await this.withCache('detail', 'getLast', {}, async () => {
      const worth = await this.ctx.model.Worth.findAll({
        order: [[ 'id', 'DESC' ]],
        where: {
          date: { [Op.lt]: moment().format('YYYY-MM-DD') },
          worths: { [Op.gt]: 0 },
        },
        limit: 1,
      });

      return worth[0];
    });
  }

  async getCurrent() {
    return await this.withCache('detail', 'getCurrent', {}, async () => {
      const worth = await this.ctx.model.Worth.findAll({
        order: [[ 'id', 'DESC' ]],
        where: {
          date: moment().format('YYYY-MM-DD'),
          worths: { [Op.gt]: 0 },
        },
        limit: 1,
      });

      if (worth[0]) {
        return worth[0];
      }
      const pos = await this.ctx.service.positions.findAll({ pageIndex: 1, pageSize: 100 });
      const last = await this.getLast();
      const totalShare = getValue(last, 'totalShare', 1);

      let total = 0;
      pos.forEach(element => {
        if (element.source) {
          total = (element.source.f2 > 0 ? element.source.f2 : element.cost) * element.amount + total;
        } else {
          total = total + element.value;
        }
      });
      return {
        worths: (total / totalShare).toFixed(2),
        date: moment().format('YYYY-MM-DD'),
      };
    });
  }

  async find(params) {
    return await this.withCache('minute', 'find', params, async () => {
      const { pageIndex, pageSize, date } = params;
      const where = {};
      if (date) { where.date = date; }
      return await this.ctx.model.Worth.findAll({
        where,
        order: [[ 'date', 'DESC' ]],
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  async create(params) {
    const { ctx } = this;
    const { Worth, WorthInout } = this.ctx.model;
    const { date, totalAmount, amount, type, user } = params;
    if (moment().format('YYYY-MM-DD') != date) {
      ctx.status = 422;
      return '仅限今天';
    }
    if (moment().get('hours') > 23) {
      ctx.status = 422;
      return '8点之后不能申购或者赎回';
    }
    const worth = await Worth.findOne({ where: { date } });
    if (amount !== 0) {
      await WorthInout.create({ date, type, money: amount, user });
    }
    await this.clearRelatedCache('worths', { method: 'create', date });
    return worth;
  }
}

module.exports = WorthsService;
