'use strict';

const { Service } = require('egg');
const weibo = require('weibo');
const axios = require('axios');
const qs = require('qs');
const marked = require('marked');

/**
 * WeiboService
 * Business logic service
 */
class WeiboService extends Service {
  async send_post(post) {
    try {
      const weiboToken = await this.ctx.service.cache.get('weiboToken');
      const ct = marked.marked(post.content, { breaks: true });
      const data = {
        title: post.title,
        access_token: weiboToken,
        content: ct,
        cover: '',
        summary: post.intro,
        text: post.intro,
      };
      const options = {
        method: 'POST',
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        data: qs.stringify(data),
        url: 'https://api.weibo.com/proxy/article/publish.json',
      };
      const result = await axios(options);
      return {
        text: result.statusText,
        weiboId: result.data.idstr,
      };
    } catch (error) {
      this.ctx.logger.error('微博推送异常:', error);
      return { success: false, message: '微博推送失败', error: error.message };
    }
  }

  async send_share(content) {
    try {
      const weiboAccessToken = await this.ctx.service.cache.get('weiboAccessToken');
      const data = {
        access_token: weiboAccessToken,
        status: content,
      };
      const options = {
        method: 'POST',
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        data: qs.stringify(data),
        url: 'https://api.weibo.com/2/statuses/share.json',
      };
      const result = await axios(options);
      return {
        text: result.statusText,
        weiboId: result.data.idstr,
      };
    } catch (error) {
      this.ctx.logger.error('微博分享异常:', error);
      return { success: false, message: '微博分享失败', error: error.message };
    }
  }

  async send_text(content, topic = false) {
    try {
      const weiboCookie = await this.ctx.service.cache.get('weiboCookie');
      const weiboToken = await this.ctx.service.cache.get('weiboToken');
      let ct = marked.marked(content, { breaks: true });
      ct = ct.replace(/(<\/?a.*?>)|(<\/?span.*?>)|(<\/?p.*?>)/g, '').replace(/<br>/g, '\n');
      if (topic) {
        ct = `${ct}#今日看盘# #股票[超话]#\u00A0`;
      }
      const data = {
        content: ct,
        pic_id: '',
        visible: 0,
        share_id: '',
        media: '{}',
        vote: '{}',
        approval_state: 0,
      };
      const options = {
        method: 'POST',
        headers: {
          cookie: weiboCookie,
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36 Edg/100.0.1185.29',
          referer: 'https://weibo.com/',
          'content-type': 'application/x-www-form-urlencoded',
          'x-xsrf-token': weiboToken,
        },
        data: qs.stringify(data),
        url: 'https://weibo.com/ajax/statuses/update',
      };
      const result = await axios(options);
      if (result.data.data) {
        return {
          text: result.data.msg,
          weiboId: result.data.data.idstr,
        };
      }
      return null;
    } catch (error) {
      this.ctx.logger.error('微博文本推送异常:', error);
      return { success: false, message: '微博文本推送失败', error: error.message };
    }
  }

  async findPostAndSend(postId) {
    try {
      const existPost = await this.ctx.service.posts.show(postId);
      let str = '';
      let result = null;
      if (existPost.category == 'dailystory') {
        str = existPost.content;
        result = await this.send_text(str, true);
        if (!result) {
          str = `${str}http://finevent.com.cn/posts/${existPost.id}`;
          result = await this.send_share(str);
        }
      } else {
        result = await this.send_post(existPost);
      }
      await existPost.update({ weiboId: result.weiboId });
      return result.text;
    } catch (error) {
      this.ctx.logger.error('微博 findPostAndSend 异常:', error);
      return { success: false, message: '微博 findPostAndSend 失败', error: error.message };
    }
  }

  async findNewsAndSend(newsId) {
    try {
      const existNews = await this.ctx.service.news.show(newsId);
      const result = await this.send_text(`${existNews.mark}\n\n${existNews.content}`, false);
      if (result) {
        return '成功';
      }
      return '失败';
    } catch (error) {
      this.ctx.logger.error('微博 findNewsAndSend 异常:', error);
      return { success: false, message: '微博 findNewsAndSend 失败', error: error.message };
    }
  }
}

module.exports = WeiboService;
