'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class MediasService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'medias:minute:',
        description: '媒体列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'medias:detail:',
        description: '媒体详情数据',
        strategy: 'balanced',
      },
      statistics: {
        ttl: 600,
        prefix: 'medias:statistics:',
        description: '媒体统计数据',
        strategy: 'conservative',
      },
      search: {
        ttl: 90,
        prefix: 'medias:search:',
        description: '媒体搜索数据',
        strategy: 'balanced',
      },
    };
  }

  // 新增或更新，写操作后清除缓存
  async create(params) {
    let media = await this.ctx.model.Media.findOne({
      where: { mediaId: params.mediaId },
    });
    if (media) {
      await media.update({ ...params });
    } else {
      media = await this.ctx.model.Media.create({ ...params });
    }
    await this.clearRelatedCache('medias');
    return media;
  }

  // 关联股票，写操作后清除缓存
  async associatedStock(params) {
    const codeArray = params.code.split(',');
    for (const code of codeArray) {
      await this.ctx.model.MediaStock.findOrCreate({
        where: { mediaId: params.mediaId, code },
      });
    }
    await this.clearRelatedCache('medias');
    return true;
  }

  // 详情，带缓存
  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      return await this.ctx.model.Media.findOne({ where: { id } });
    });
  }

  // 统计，带缓存
  async get_count() {
    return await this.withCache('statistics', 'get_count', {}, async () => {
      return await this.ctx.model.Media.count({
        where: {
          read: false,
          source: { [Op.in]: [ 'xueqiu', 'xiaozuowen' ] },
        },
      });
    });
  }

  // 随机获取，不缓存（如需缓存可加）
  async getRandom(num, ex) {
    const post = await this.ctx.model.Media.findAll({
      where: {
        read: false,
        id: { [Op.notIn]: ex.split(',') },
        source: { [Op.in]: [ 'xiaozuowen', 'xueqiu' ] },
      },
      order: [[ 'createTime', 'DESC' ], [ 'source', 'ASC' ]],
      limit: num,
    });
    return post;
  }

  // 更新，写操作后清除缓存
  async update(id, params) {
    await this.ctx.model.Media.update({ ...params }, { where: { id } });
    await this.clearRelatedCache('medias');
    return true;
  }

  // 重要媒体全部，带缓存
  async getAll(params) {
    return await this.withCache('minute', 'getAll', params, async () => {
      const { source } = params;
      let where = { important: 1 };
      if (source) {
        where = Object.assign(where, { source });
      }
      return await this.ctx.model.Media.findAll({
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        attributes: [ 'id', 'updatedAt' ],
        where,
      });
    });
  }

  // 分页列表，带缓存
  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { Media } = this.ctx.model;
      const { pageIndex, pageSize, unread, important, source, createTime, dateStart, dateEnd } = params;
      let where = {};
      if (important === 'true') { where = Object.assign(where, { important: 1 }); }
      if (unread === 'true') { where = Object.assign(where, { read: false }); }
      if (source) { where = Object.assign(where, { source }); }
      if (createTime) { where = Object.assign(where, { createTime }); }
      if (dateStart && dateEnd) { where = Object.assign(where, { createTime: { [Op.between]: [ dateStart, dateEnd ] } }); }
      return await Media.findAndCountAll({
        order: [[ 'createTime', 'DESC' ], [{ model: this.ctx.model.MediaStock }, 'id', 'DESC' ]],
        distinct: true,
        where,
        include: [{
          model: this.ctx.model.MediaStock,
          attributes: { exclude: [ 'createdAt', 'updatedAt', 'deletedAt' ] },
          include: {
            model: this.ctx.model.Stock,
            attributes: [ 'code' ],
            include: {
              model: this.ctx.model.Source,
              attributes: [ 'displayName', 'code', 'f3' ],
            },
          },
        }],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  // 搜索，带缓存
  async findByText(text) {
    return await this.withCache('search', 'findByText', { text }, async () => {
      const { Media } = this.ctx.model;
      return await Media.findAll({
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        where: {
          [Op.or]: [
            { title: { [Op.like]: `%${text}%` } },
            { content: { [Op.like]: `%${text}%` } },
          ],
        },
        offset: 0,
        limit: 20,
      });
    });
  }
}

module.exports = MediasService;
