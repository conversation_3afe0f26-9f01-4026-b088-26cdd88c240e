'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class EventsService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'events:minute:',
        description: '事件分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'events:hourly:',
        description: '事件小时级数据',
        strategy: 'conservative',
      },
      daily: {
        ttl: 1800,
        prefix: 'events:daily:',
        description: '事件日级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'events:statistics:',
        description: '事件统计数据',
        strategy: 'conservative',
      },
      search: {
        ttl: 90,
        prefix: 'events:search:',
        description: '事件搜索数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'events:detail:',
        description: '事件详情数据',
        strategy: 'balanced',
      },
    };
  }

  async findAllEvents(params) {
    return await this.withCache('minute', 'findAllEvents', params, async () => {
      const { pageIndex, pageSize, tag, category, type, frequency } = params;
      const where = {};
      if (tag) { where.content = { [Op.like]: `%${tag}%` }; }
      if (frequency) { where.frequency = { [Op.eq]: frequency }; }
      if (category) { where.category = { [Op.eq]: category }; }
      if (type === 'node') { where.category = { [Op.notLike]: '%BREAKING' }; }
      return await this.ctx.model.Event.findAndCountAll({
        where,
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        include: [{
          model: this.ctx.model.CalendarEvent,
          include: { model: this.ctx.model.Calendar },
        }],
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  async deleteStock(id, params) {
    const eventstock = await this.ctx.model.EventStock.destroy({
      where: {
        eventId: id,
        code: params.code,
      },
    });
    return eventstock;
  }

  async createStock(id, params) {
    const eventstock = await this.ctx.model.EventStock.findOrCreate({
      where: {
        eventId: id,
        code: params.code,
      },
    });

    return eventstock;
  }

  async deleteTag(id, params) {
    const eventtag = await this.ctx.model.EventTag.destroy({
      where: {
        eventId: id,
        tagId: params.tagId,
      },
    });
    return eventtag;
  }

  async unbindCal(id, params) {
    const event = await this.ctx.model.CalendarEvent.destroy({
      where: {
        eventId: id,
        calendarId: params.calendarId,
      },
    });
    return event;
  }

  async createTag(id, params) {
    const eventtag = await this.ctx.model.EventTag.findOrCreate({
      where: {
        eventId: id,
        tagId: params.tagId,
      },
    });

    return eventtag;
  }

  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      const { Stock, Source, EventStock, EventTag, Tag, Dictionary } = this.ctx.model;
      return await this.ctx.model.Event.findOne({
        where: { id },
        include: [
          { model: EventTag, include: { model: Tag } },
          { model: EventStock, include: { model: Stock, include: { model: Source } } },
          { model: Dictionary },
        ],
      });
    });
  }

  async create(params) {
    const { type, content, frequency, calendar, related, category, description } = params;
    const callist = calendar.split(',');
    const event = await this.ctx.model.Event.findOrCreate({
      where: { type, content, frequency, related, category, description },
    });
    const promiseList = [];
    callist.forEach(element => {
      promiseList.push(this.ctx.model.CalendarEvent.findOrCreate({
        where: { eventId: event[0].get('id'), calendarId: element },
      }));
    });
    await Promise.all(promiseList);
    await this.clearRelatedCache('events', { method: 'create', id: event[0].get('id') });
    return event[0];
  }

  async update(id, params) {
    await this.ctx.model.Event.update({ ...params }, { where: { id } });
    await this.clearRelatedCache('events', { method: 'update', id });
    return true;
  }

  async destroy(id) {
    await this.ctx.model.Event.destroy({ where: { id } });
    await this.clearRelatedCache('events', { method: 'destroy', id });
    return true;
  }

}

module.exports = EventsService;
