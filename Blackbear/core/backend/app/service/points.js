'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const moment = require('moment');

class PointsService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'points:minute:',
        description: '积分列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'points:detail:',
        description: '积分详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 写操作后清除缓存
  async create(params) {
    const { type, userId, postId } = params;
    const user = userId || this.ctx.state.user.id;

    const today = moment().format('YYYYMMDD');

    const userInfo = await this.ctx.model.User.findOne({
      where: {
        id: user,
      },
    });

    // 　每日登录+1
    if (type == 'login' && user) {
      const point = await this.ctx.model.Point.findOne({
        where: {
          date: today,
          type,
          userId: user,
        },
      });

      if (point) {
        return false;
      }
      const transaction = await this.ctx.model.transaction();
      try {
        await this.ctx.model.Point.create({
          date: today,
          type,
          score: 1,
          desc: '每日登录',
          userId: user,
        }, { transaction });
        await userInfo.update({
          point: userInfo.dataValues.point + 1,
        }, { transaction });
        await transaction.commit();
        await this.clearRelatedCache('points');
        return true;
      } catch (e) {
        console.log(e);
        await transaction.rollback();
        throw e;
      }

    }

    // 发表文章+50
    if (type == 'post' && postId) {

      const point = await this.ctx.model.Point.findOne({
        where: {
          type,
          userId: user,
          postId,
        },
      });

      if (point) {
        return false;
      }
      const transaction = await this.ctx.model.transaction();
      try {
        await this.ctx.model.Point.create({
          type,
          score: 20,
          desc: '发布文章',
          userId: user,
          postId,
        }, { transaction });
        await userInfo.update({
          point: userInfo.dataValues.point + 20,
        }, { transaction });
        await transaction.commit();
        await this.clearRelatedCache('points');
        return true;
      } catch (e) {
        console.log(e);
        await transaction.rollback();
        throw e;
      }

    }

    if (type == 'tag' && user) {
      const point = await this.ctx.model.Point.findOne({
        where: {
          type,
          userId: user,
          date: today,
        },
      });

      if (point) {
        return false;
      }
      const transaction = await this.ctx.model.transaction();
      try {
        await this.ctx.model.Point.create({
          type,
          score: 10,
          desc: '题材活跃',
          userId: user,
          date: today,
        }, { transaction });
        await userInfo.update({
          point: userInfo.dataValues.point + 10,
        }, { transaction });
        await transaction.commit();
        await this.clearRelatedCache('points');
        return true;
      } catch (e) {
        console.log(e);
        await transaction.rollback();
        throw e;
      }

    }

    if (type == 'stock' && user) {
      const point = await this.ctx.model.Point.findOne({
        where: {
          type,
          userId: user,
          date: today,
        },
      });

      if (point) {
        return false;
      }
      const transaction = await this.ctx.model.transaction();
      try {
        await this.ctx.model.Point.create({
          type,
          score: 10,
          desc: '个股活跃',
          userId: user,
          date: today,
        }, { transaction });
        await userInfo.update({
          point: userInfo.dataValues.point + 10,
        }, { transaction });
        await transaction.commit();
        await this.clearRelatedCache('points');
        return true;
      } catch (e) {
        console.log(e);
        await transaction.rollback();
        throw e;
      }

    }

    return true;
  }

  // 更新，写操作后清除缓存
  async update(id, params) {
    await this.ctx.model.Point.update({ ...params }, { where: { id } });
    await this.clearRelatedCache('points');
    return true;
  }

  // 删除，写操作后清除缓存
  async destroy(id) {
    await this.ctx.model.Point.destroy({ where: { id } });
    await this.clearRelatedCache('points');
    return true;
  }

  // 详情，带缓存
  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      return await this.ctx.model.Point.findOne({ where: { id } });
    });
  }

  // 列表，带缓存
  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { pageIndex, pageSize } = params;
      const userId = this.ctx.state.user.id;
      const where = { userId };
      return await this.ctx.model.Point.findAndCountAll({
        order: [[ 'id', 'DESC' ]],
        distinct: true,
        where,
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }
}

module.exports = PointsService;
