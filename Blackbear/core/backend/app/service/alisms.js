'use strict';

const { Service } = require('egg');
const Core = require('@alicloud/pop-core');

/**
 * AlismsService
 * Business logic service
 */
class AlismsService extends Service {
  async sendMessage(phone, code) {
    const client = new Core({
      accessKeyId: process.env.BB_ALIBABA_CLOUD_ACCESS_KEY_ID,
      accessKeySecret: process.env.BB_ALIBABA_CLOUD_ACCESS_KEY_SECRET,
      endpoint: 'https://dysmsapi.aliyuncs.com',
      apiVersion: '2017-05-25',
    });

    const params = {
      SignName: 'finevent',
      TemplateCode: 'SMS_462225227',
      PhoneNumbers: phone,
      TemplateParam: `{"code":"${code}"}`,
    };

    const requestOption = {
      method: 'POST',
      formatParams: false,
    };

    return new Promise((resolve, reject) => {
      try {
        client.request('SendSms', params, requestOption).then(result => {
          this.ctx.logger.info('阿里云短信发送结果:', result);
          resolve(JSON.stringify(result));
        }, ex => {
          this.ctx.logger.error('阿里云短信发送异常:', ex);
          resolve(JSON.stringify({ success: false, message: '短信发送失败', error: ex.message }));
        });
      } catch (error) {
        this.ctx.logger.error('阿里云短信服务调用异常:', error);
        resolve(JSON.stringify({ success: false, message: '短信服务调用失败', error: error.message }));
      }
    });
  }
}

module.exports = AlismsService;
