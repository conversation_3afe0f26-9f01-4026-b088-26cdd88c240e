'use strict';

const { Service } = require('egg');
const { Op } = require('sequelize');
const moment = require('moment');
const uuid = require('uuid');
const svgCaptcha = require('svg-captcha');

/**
 * VerificationsService
 * Business logic service
 */
class VerificationsService extends Service {
  // 建议：对 create、smsCode 等方法加限流防刷
  async findAll(params) {
    try {
      const { ctx } = this;
      const { limit, offset, prop_order, order } = params;
      const { where } = params;
      const Order = [];
      prop_order && order ? Order.push([ prop_order, order ]) : null;
      return await ctx.model.Verification.findAndCountAll({
        limit,
        offset,
        where,
        order: Order,
      });
    } catch (error) {
      this.ctx.logger.error('Error in findAll:', error);
      return { success: false, message: '查询失败', error: error.message };
    }
  }

  async findOne(id) {
    try {
      return await this.ctx.model.Verification.findOne({ where: { id } });
    } catch (error) {
      this.ctx.logger.error('Error in findOne:', error);
      return { success: false, message: '查询失败', error: error.message };
    }
  }

  // 主要是email的code
  // 建议：加限流防刷
  async create(params) {
    const { ctx, app } = this;
    const { target, type } = params;
    const expirationTime = moment().add(15, 'minute').format('YYYY-MM-DD hh:mm:ss');
    const code = Math.random().toString().substring(2, 8);
    try {
      // 如果类型为邮箱 则发送邮件
      if (type === 1) {
        // 邮件内容安全防注入
        await app.mailer.send({
          from: '"Finevent" <<EMAIL>>',
          to: [ target ],
          subject: 'BlackBear验证码邮件',
          text: code,
          html: `<div style="display: flex;flex-direction: column;justify-content: center;align-items: center;width: 300px;height: 100px;margin: 66px auto;"><span style="line-height: 28px;">来自 BlackBear 的邮箱验证码：</span><div style="font-weight: 600;font-size: 22px;line-height: 28px;">${code}</div></div>`,
        });
      }
      // 将原来的所有失效
      await ctx.model.Verification.update({ available: 0 }, { where: { target } });
      return await ctx.model.Verification.create({
        ...params,
        id: uuid.v1(),
        code,
        target,
        available: 1,
        expirationTime,
      });
    } catch (error) {
      this.ctx.logger.error('Error in create verification:', error);
      return { success: false, message: '验证码创建失败', error: error.message };
    }
  }

  async update(id, params) {
    try {
      const { ctx } = this;
      return await ctx.model.Verification.update(params, {
        where: { id: params.id },
      });
    } catch (error) {
      this.ctx.logger.error('Error in update verification:', error);
      return { success: false, message: '验证码更新失败', error: error.message };
    }
  }

  async destroy(id, params) {
    try {
      const { ctx } = this;
      return await ctx.model.Verification.destroy({
        where: { id: params.ids },
      });
    } catch (error) {
      this.ctx.logger.error('Error in destroy verification:', error);
      return { success: false, message: '验证码删除失败', error: error.message };
    }
  }

  async svgCaptcha() {
    try {
      const captcha = svgCaptcha.create({
        size: 4,
        fontSize: 50,
        width: 100,
        height: 40,
        ignoreChars: '0o1il',
        noise: 1,
        bacground: '#cc9966',
      });
      const expirationTime = moment().add(15, 'minute').format('YYYY-MM-DD hh:mm:ss');
      const verification = await this.ctx.model.Verification.create({
        id: uuid.v1(),
        code: captcha.text,
        target: 'login',
        expirationTime,
        available: 1,
        type: 2,
      });
      return {
        img: captcha.data,
        loginId: verification.id,
      };
    } catch (error) {
      this.ctx.logger.error('Error in svgCaptcha:', error);
      return { success: false, message: '验证码生成失败', error: error.message };
    }
  }

  // 建议：加限流防刷
  async smsCode(phone) {
    const { ctx } = this;
    const expirationTime = moment().add(15, 'minute').format('YYYY-MM-DD hh:mm:ss');
    const randomStr = Math.random().toString().substring(2, 6);
    try {
      // 将原来的所有失效
      await ctx.model.Verification.update({ available: 0 }, { where: { target: phone } });
      const res = await ctx.service.alisms.sendMessage(phone, randomStr);
      const verification = await this.ctx.model.Verification.create({
        id: uuid.v1(),
        code: randomStr,
        target: phone,
        expirationTime,
        available: 1,
        type: 3,
      });
      return res;
    } catch (error) {
      this.ctx.logger.error('Error in smsCode:', error);
      return { success: false, message: '短信验证码发送失败', error: error.message };
    }
  }

  /**
   * 验证此验证码
   * @param params
   * @return {Promise<*>}
   */
  async verificationCode(params) {
    const { ctx } = this;
    const { target, code, type } = params;
    try {
      const res = await ctx.model.Verification.findOne({
        where: {
          target,
          code,
          type,
          available: 1,
          expirationTime: { [Op.gt]: moment().format('YYYY-MM-DD hh:mm:ss') },
        },
      });
      // 敏感信息不打印日志
      return res;
    } catch (error) {
      this.ctx.logger.error('Error in verificationCode:', error);
      return { success: false, message: '验证码校验失败', error: error.message };
    }
  }
}

module.exports = VerificationsService;
