'use strict';

const BaseCacheService = require('./base/cache');

class OperateService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'operate:minute:',
        description: '操作日志列表',
        strategy: 'balanced',
      },
    };
  }

  // 写操作后清除缓存
  async createLog(dict) {
    const { ctx } = this;
    await ctx.model.OperateLog.create({ ...dict });
    await this.clearRelatedCache('operate');
  }

  // 日志查询，带缓存
  async getLog(params) {
    return await this.withCache('minute', 'getLog', params, async () => {
      const { pageIndex, pageSize, username } = params;
      const { ctx } = this;
      const where = {};
      if (username) { where.username = username; }
      const logs = await ctx.model.OperateLog.findAndCountAll({
        where,
        include: [{
          model: ctx.model.User,
          attributes: [ 'id', 'username', 'photo' ],
        }],
        order: [[ 'id', 'DESC' ]],
        attributes: { exclude: [ 'updatedAt', 'deletedAt' ] },
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
      return logs;
    });
  }
}

module.exports = OperateService;
