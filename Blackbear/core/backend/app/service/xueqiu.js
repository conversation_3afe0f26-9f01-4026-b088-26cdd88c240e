'use strict';

const { Service } = require('egg');
const axios = require('axios');
const qs = require('qs');
const marked = require('marked');
const { result } = require('lodash');

/**
 * XueqiuService
 * Business logic service
 */
class XueqiuService extends Service {
  async send_text(content) {
    try {
      const xueqiuCookie = await this.ctx.service.cache.get('xueqiuCookie');
      let ct = marked.marked(content, { breaks: true });
      ct = ct.replace(/(<\/?a.*?>)|(<\/?span.*?>)|(<\/?p.*?>)/g, '').replace(/\n/g, '<br/>');
      const obj1 = { text: ct, type: 0 };
      const options1 = {
        method: 'POST',
        headers: {
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46',
          referer: 'https://xueqiu.com/',
          'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
          cookie: xueqiuCookie,
        },
        data: qs.stringify(obj1),
        url: 'https://xueqiu.com/statuses/text_check.json',
      };
      const result1 = await axios(options1);
      const obj2 = { api_path: '/statuses/update.json', time: Date.now() };
      const options2 = {
        method: 'GET',
        headers: {
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46',
          referer: 'https://xueqiu.com/',
          'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
          cookie: xueqiuCookie,
        },
        url: `https://xueqiu.com/provider/session/token.json?api_path=${obj2.api_path}&_=${obj2.time}`,
      };
      const result2 = await axios(options2);
      if (result1.data.success == true && result2.data.session_token) {
        const obj3 = {
          status: ct,
          allow_reward: false,
          post_position: 'pc_home_post',
          post_source: '',
          session_token: result2.data.session_token,
        };
        const options3 = {
          method: 'POST',
          headers: {
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46',
            referer: 'https://xueqiu.com/',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            cookie: xueqiuCookie,
          },
          data: qs.stringify(obj3),
          url: 'https://xueqiu.com/statuses/update.json',
        };
        const result3 = await axios(options3);
        if (result3.data && result3.data.id) {
          return result3.data.id;
        }
      }
      return null;
    } catch (error) {
      this.ctx.logger.error('雪球 send_text 异常:', error);
      return { success: false, message: '雪球 send_text 失败', error: error.message };
    }
  }

  async send_post(post) {
    try {
      const xueqiuCookie = await this.ctx.service.cache.get('xueqiuCookie');
      const ct = await this.replaceImg(marked.marked(post.content, { breaks: true }));
      const obj = {
        text: ct,
        title: post.title,
        cover_pic: '',
        flags: false,
        original_event: '',
        status_id: '',
        legal_user_visible: false,
        is_private: false,
      };
      const options = {
        method: 'POST',
        headers: {
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46',
          referer: 'https://mp.xueqiu.com/write',
          'content-type': 'application/x-www-form-urlencoded',
          cookie: xueqiuCookie,
        },
        data: qs.stringify(obj),
        url: 'https://mp.xueqiu.com/xq/statuses/draft/save.json',
      };
      const result = await axios(options);
      if (result.data && result.data.id) {
        return result.data.id;
      }
      return null;
    } catch (error) {
      this.ctx.logger.error('雪球 send_post 异常:', error);
      return { success: false, message: '雪球 send_post 失败', error: error.message };
    }
  }

  async replaceImg(content) {
    const b = /<img [^>]*src=['"]([^'"]+)[^>]*>/g;
    const s = content.match(b);
    if (!s) {
      return content;
    }
    for (let i = 0; i < s.length; i++) {
      const srcImg = s[i].replace(b, '$1');
      const distImg = await this.uploadImg(srcImg);
      content = content.replace(new RegExp(srcImg, 'g'), distImg);
    }
    return content;
  }

  async uploadImg(url) {
    try {
      const xueqiuCookie = await this.ctx.service.cache.get('xueqiuCookie');
      const options = {
        method: 'POST',
        headers: {
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46',
          referer: 'https://mp.xueqiu.com/write',
          'content-type': 'application/x-www-form-urlencoded',
          cookie: xueqiuCookie,
        },
        data: qs.stringify({ src: url }),
        url: 'https://mp.xueqiu.com/node/snowman/service/images/paste',
      };
      const result = await axios(options);
      if (result.data.filename) {
        return `https:${result.data.url}/${result.data.filename}`;
      }
      return null;
    } catch (error) {
      this.ctx.logger.error('雪球 uploadImg 异常:', error);
      return null;
    }
  }

  async findPostAndSend(postId) {
    try {
      const existPost = await this.ctx.service.posts.show(postId);
      let result = null;
      if (existPost.category == 'dailystory') {
        result = await this.send_text(existPost.content);
      } else {
        result = await this.send_post(existPost);
      }
      if (result) {
        await existPost.update({ xueqiuId: result });
        return '成功';
      }
      return '失败';
    } catch (error) {
      this.ctx.logger.error('雪球 findPostAndSend 异常:', error);
      return { success: false, message: '雪球 findPostAndSend 失败', error: error.message };
    }
  }

  async findNewsAndSend(newsId) {
    try {
      const existNews = await this.ctx.service.news.show(newsId);
      const result = await this.send_text(`${existNews.mark}<br><br>${existNews.content}`);
      if (result) {
        return '成功';
      }
      return '失败';
    } catch (error) {
      this.ctx.logger.error('雪球 findNewsAndSend 异常:', error);
      return { success: false, message: '雪球 findNewsAndSend 失败', error: error.message };
    }
  }
}

module.exports = XueqiuService;
