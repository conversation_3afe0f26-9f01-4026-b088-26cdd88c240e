'use strict';

const { Service } = require('egg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const token = 'mi0DxpFeAocShsWz';

/**
 * BaiduService
 * Business logic service
 */
class BaiduService extends Service {
  async pushUrl(url) {
    const data = {
      token,
      site: `finevent.top${url}`,
    };
    const options = {
      method: 'POST',
      headers: { 'content-type': 'multipart/form-data' },
      data,
      url: 'http://data.zz.baidu.com/urls?site=https://finevent.top&token=mi0DxpFeAocShsWz',
    };
    try {
      await axios(options);
      this.ctx.logger.info(`push url: https://finevent.top${url}`);
    } catch (error) {
      this.ctx.logger.error('百度推送异常:', error);
      return { success: false, message: '百度推送失败', error: error.message };
    }
  }

  async pushAll() {
    const { ctx } = this;
    try {
      const [ tag, post, media ] = await Promise.all([
        ctx.service.tags.getAll(),
        ctx.service.posts.getAll(),
        ctx.service.medias.getAll(),
      ]);
      const urls = [];
      tag.forEach(item => {
        urls.push(`https://finevent.top/tag/${item.id}`);
      });
      post.forEach(item => {
        urls.push(`https://finevent.top/post/${item.id}`);
      });
      media.forEach(item => {
        urls.push(`https://finevent.top/media/${item.id}`);
      });
      const file = path.join(__dirname, 'urls.txt');
      await fs.writeFile(file, urls.join('\n'), (err, result) => {
        if (err) { this.ctx.logger.error('写入urls.txt异常:', err); }
      });
      const { exec } = require('child_process');
      exec(`curl -H 'Content-Type:text/plain' --data-binary @${file} 'http://data.zz.baidu.com/urls?site=https://finevent.top&token=mi0DxpFeAocShsWz'`, (error, stdout, stderr) => {
        if (error) {
          this.ctx.logger.error('百度批量推送失败:', error);
        } else {
          this.ctx.logger.info('百度批量推送成功:', stdout);
        }
      });
    } catch (error) {
      this.ctx.logger.error('百度批量推送异常:', error);
      return { success: false, message: '百度批量推送失败', error: error.message };
    }
  }
}

module.exports = BaiduService;
