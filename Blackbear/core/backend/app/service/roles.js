'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class RolesService extends BaseCacheService {
  get cacheConfig() {
    return {
      minute: {
        ttl: 60,
        prefix: 'roles:minute:',
        description: '角色分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'roles:hourly:',
        description: '角色小时级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'roles:statistics:',
        description: '角色统计数据',
        strategy: 'conservative',
      },
      detail: {
        ttl: 180,
        prefix: 'roles:detail:',
        description: '角色详情数据',
        strategy: 'balanced',
      },
    };
  }

  async create(params) {
    const one = await this.ctx.model.Role.findOne({ where: { ...params } });
    const code = await this.ctx.model.Role.findOne({ where: { code: params.code } });
    if (one) { throw new Error('已存在'); }
    if (code) { throw new Error('code已存在'); }
    const user = await this.ctx.model.Role.create({ ...params });
    await this.clearRelatedCache('roles', { method: 'create', code: params.code });
    return user;
  }

  async update(code, params) {
    await this.ctx.model.Role.update({ ...params }, { where: { code } });
    await this.clearRelatedCache('roles', { method: 'update', code });
    return true;
  }

  async destroy(code) {
    const exrole = await this.ctx.model.Role.findOne({ where: { code } });
    if (exrole.isDefault) {
      return { code: 1, reason: '默认角色无法删除，请先设置其它默认角色！' };
    }
    if (code === 'admin.top') {
      return { code: 2, reason: '超级管理员无法删除！' };
    }
    const transaction = await this.ctx.model.transaction();
    try {
      await this.ctx.model.Role.destroy({ where: { code } }, { transaction });
      await this.ctx.model.RoleMenu.destroy({ where: { roleId: code } }, { transaction });
      await this.ctx.model.RolePermission.destroy({ where: { roleId: code } }, { transaction });
      await this.clearRelatedCache('roles', { method: 'destroy', code });
      return true;
    } catch (e) {
      await transaction.rollback();
      throw e;
    }
  }

  async show(code) {
    return await this.withCache('detail', 'show', { code }, async () => {
      return await this.ctx.model.Role.findOne({ where: { code } });
    });
  }

  async findAll(params) {
    return await this.withCache('minute', 'findAll', params, async () => {
      const { pageIndex, pageSize } = params;
      const where = {};
      return await this.ctx.model.Role.findAndCountAll({
        order: [[ 'code', 'DESC' ]],
        distinct: true,
        where,
        include: [
          { model: this.ctx.model.RolePermission, include: [{ model: this.ctx.model.Permission }] },
          { model: this.ctx.model.RoleMenu, include: [{ model: this.ctx.model.Menu }] },
        ],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  async updateIsDefault(code, params) {
    const transaction = await this.ctx.model.transaction();
    await this.ctx.model.Role.update({ isDefault: 0 }, { where: { isDefault: 1 }, transaction });
    const res = await this.ctx.model.Role.update({ isDefault: 1 }, { where: { code }, transaction });
    if (res && res[0] === 1) {
      await transaction.commit();
      await this.clearRelatedCache('roles', { method: 'updateIsDefault', code });
      return true;
    }
    await transaction.rollback();
    return false;
  }

  async bulkCreateMenu(code, params) {
    const arr = params.ids.split(',');
    const payload = arr.map(e => (e.length > 0 ? { roleId: code, menuId: e } : null)).filter(Boolean);
    const res = await this.ctx.model.RoleMenu.bulkCreate(payload);
    await this.clearRelatedCache('roles', { method: 'bulkCreateMenu', code });
    return res;
  }

  async bulkCreatePremission(code, params) {
    const arr = params.ids.split(',');
    const payload = arr.map(e => (e.length > 0 ? { roleId: code, permissionId: e } : null)).filter(Boolean);
    const res = await this.ctx.model.RolePermission.bulkCreate(payload);
    await this.clearRelatedCache('roles', { method: 'bulkCreatePremission', code });
    return res;
  }

  async bulkDeleteMenu(code, params) {
    const res = await this.ctx.model.RoleMenu.destroy({ force: true, where: { id: params.ids.split(',') } });
    await this.clearRelatedCache('roles', { method: 'bulkDeleteMenu', code });
    return res;
  }

  async bulkDeletePremission(code, params) {
    const res = await this.ctx.model.RolePermission.destroy({ force: true, where: { id: params.ids.split(',') } });
    await this.clearRelatedCache('roles', { method: 'bulkDeletePremission', code });
    return res;
  }
}

module.exports = RolesService;
