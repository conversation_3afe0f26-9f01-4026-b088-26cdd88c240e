'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const moment = require('moment');
const axios = require('axios');

class QuantsService extends BaseCacheService {

  // 自定义缓存配置 - 针对量化数据服务的实时性要求
  get cacheConfig() {
    return {
      // 继承基础配置
      ...super.cacheConfig,

      // 量化实时数据 - 需要快速响应
      quantRealtime: {
        ttl: 30, // 30秒，保证实时性
        prefix: 'quants:realtime:',
        description: '量化实时数据缓存',
        strategy: 'aggressive', // 激进策略：频繁更新
      },

      // 量化分钟数据 - 中等实时性
      quantMinute: {
        ttl: 60, // 1分钟
        prefix: 'quants:minute:',
        description: '量化分钟数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },

      // 量化小时数据 - 较低实时性
      quantHourly: {
        ttl: 3600, // 1小时
        prefix: 'quants:hourly:',
        description: '量化小时数据缓存',
        strategy: 'conservative', // 保守策略：较少更新
      },

      // 量化长期数据 - 很长缓存时间
      quantLongterm: {
        ttl: 7200, // 2小时
        prefix: 'quants:longterm:',
        description: '量化长期数据缓存',
        strategy: 'conservative',
      },

      // 量化配置数据 - 中等缓存时间
      quantConfig: {
        ttl: 1800, // 30分钟
        prefix: 'quants:config:',
        description: '量化配置数据缓存',
        strategy: 'conservative',
      },

      // 量化指数数据 - 中等缓存时间
      quantIndex: {
        ttl: 1200, // 20分钟
        prefix: 'quants:index:',
        description: '量化指数数据缓存',
        strategy: 'balanced',
      },

      // 量化统计数据 - 较长缓存时间
      quantStats: {
        ttl: 600, // 10分钟
        prefix: 'quants:stats:',
        description: '量化统计数据缓存',
        strategy: 'conservative',
      },
    };
  }

  // 返回重要量化数据 - 分钟级缓存
  async getStockDaliy(limit) {
    return await this.withCache('quantMinute', 'getStockDaliy', { limit }, async () => {
      const stocks = await this.ctx.model.StockDaliy.findAll({
        order: [[ 'date', 'DESC' ]],
        limit: limit ? limit : 360,
        attributes: {
          exclude: [ 'hot', 'rqyl', 'rqmcl', 'rqye', 'rzche', 'rzmre', 'createdAt', 'updatedAt', 'deletedAt',
            'sentiment', 'sentimentDesc', 'funds', 'fundsDesc', 'shiborON' ],
        },
        where: {
          isOpen: true,
          volume: {
            [Op.gt]: 0,
          },
        },
      });

      return stocks.reverse();
    });
  }

  // 区别上面那个方法，确保当天完整数据 - 分钟级缓存
  async getStockDaliyFull(limit) {
    return await this.withCache('quantMinute', 'getStockDaliyFull', { limit }, async () => {
      const stocks = await this.ctx.model.StockDaliy.findAll({
        order: [[ 'date', 'DESC' ]],
        limit: limit ? limit : 360,
        attributes: {
          exclude: [ 'hot', 'rqyl', 'rqmcl', 'rqye', 'rzche', 'rzmre', 'createdAt', 'updatedAt', 'deletedAt',
            'sentiment', 'sentimentDesc', 'funds', 'fundsDesc', 'shiborON' ],
        },
        where: {
          isOpen: true,
          volume: {
            [Op.gt]: 0,
          },
          // totalvalue: {
          //   [Op.gt]: 0,
          // },
          crowding: {
            [Op.gt]: 0,
          },
        },
      });

      return stocks.reverse();
    });
  }

  // 获取今日数据 - 实时缓存（30秒）
  async getToadyDaliy() {
    return await this.withCache('quantRealtime', 'getToadyDaliy', {}, async () => {
      const today = moment().format('YYYYMMDD');
      const stock = await this.ctx.model.StockDaliy.findByPk(today);
      return stock;
    });
  }

  // 获取最新数据 - 实时缓存（30秒）
  async getLastDaliy() {
    return await this.withCache('quantRealtime', 'getLastDaliy', {}, async () => {
      const today = moment().format('YYYYMMDD');
      const stock = await this.ctx.model.StockDaliy.findOne({
        where: {
          date: {
            [Op.lt]: today,
          },
          isOpen: true,
        },
        order: [[ 'date', 'DESC' ]],
      });

      return stock;
    });
  }

  // 获取最新融资融券余额 - 实时缓存（30秒）
  async getLastRzrq() {
    return await this.withCache('quantRealtime', 'getLastRzrq', {}, async () => {
      const stock = await this.ctx.model.StockDaliy.findOne({
        where: {
          rzrqye: {
            [Op.ne]: null,
          },
        },
        attributes: [ 'date', 'rzrqye' ],
        order: [[ 'date', 'DESC' ]],
      });

      return stock.rzrqye;
    });
  }

  // 获取融资融券余额历史 - 小时级缓存
  async getRzrqye(limit) {
    return await this.withCache('quantHourly', 'getRzrqye', { limit }, async () => {
      const stock = await this.ctx.model.StockDaliy.findAll({
        where: {
          isOpen: true,
          rzrqye: {
            [Op.ne]: null,
          },
        },
        attributes: [ 'date', 'rzrqye' ],
        limit: limit ? limit : 250,
        order: [[ 'date', 'DESC' ]],
      });

      return stock;
    });
  }

  // 获取北向资金数据 - 实时缓存（包含外部API调用）
  async getBeixiang(limit) {
    return await this.withCache('quantRealtime', 'getBeixiang', { limit }, async () => {
      const stock = await this.ctx.model.StockDaliy.findAll({
        where: {
          isOpen: true,
          NorthMoney: {
            [Op.ne]: null,
          },
        },
        attributes: [ 'date', 'NorthMoney', 'HSMoney', 'SSMoney' ],
        limit: limit ? limit : 250,
        order: [[ 'date', 'DESC' ]],
      });

      const result = await axios.get('http://push2.eastmoney.com/api/qt/kamt/get?fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f63&ut=b2884a393a59ad64002292a3e90d46a5&_=1599120897799');
      const bx = result.data.data;

      if (bx) {
        const todayD = {
          date: moment().format('YYYYMMDD'),
          NorthMoney: ((bx.hk2sh.netBuyAmt + bx.hk2sz.netBuyAmt) / 10000).toFixed(2),
        };

        stock.unshift(todayD);
      }

      return stock;
    });
  }

  // 获取成交量数据 - 小时级缓存
  async getCjl(limit) {
    return await this.withCache('quantHourly', 'getCjl', { limit }, async () => {
      const stock = await this.ctx.model.StockDaliy.findAll({
        where: {
          isOpen: true,
          volume: {
            [Op.ne]: null,
          },
        },
        attributes: [ 'date', 'volume', 'shVolume', 'szVolume', 'cybVolume' ],
        limit: limit ? limit : 250,
        order: [[ 'date', 'DESC' ]],
      });

      return stock;
    });
  }

  // 获取配置 - 长期缓存（30分钟）
  async getConfig() {
    return await this.withCache('quantConfig', 'getConfig', {}, async () => {
      return await this.ctx.model.Configuration.findByPk(1);
    });
  }

  // 更新配置 - 清除配置缓存
  async updateConfig(params) {
    const quant = await this.ctx.model.Configuration.findByPk(1);
    await quant.update({ ...params });

    // 清除相关缓存
    await this.clearRelatedCache('quants', { type: 'quantConfig' });

    return true;
  }

  // 获取股票列表 - 长期缓存（2小时）
  async getStockList(params) {
    return await this.withCache('quantLongterm', 'getStockList', params, async () => {
      return await this.ctx.model.Quant.findAll();
    });
  }

  // 获取中证指数数据 - 小时级缓存
  async getCNI(code) {
    return await this.withCache('quantIndex', 'getCNI', { code }, async () => {
      const quant = await this.ctx.model.Configuration.findByPk(1);
      const stocks = await this.ctx.model.Csindex.findAll({
        where: {
          code,
          date: {
            [Op.gte]: quant.decisionDate,
          },
        },
        limit: 250,
        order: [[ 'date', 'DESC' ]],
      });

      return stocks;
    });
  }

  // 获取今日中证CNI - 实时缓存（30秒）
  async getTodayCNI() {
    return await this.withCache('quantRealtime', 'getTodayCNI', {}, async () => {
      const lastOpen = await this.ctx.model.StockDaliy.findOne({
        where: {
          isOpen: true,
          volume: {
            [Op.gt]: 0,
          },
          crowding: {
            [Op.gt]: 0,
          },
        },
        order: [[ 'date', 'DESC' ]],
      });

      const stocks = await this.ctx.model.Csindex.findAll({
        where: {
          tradedate: {
            [Op.eq]: lastOpen.date,
          },
          type: 1,
        },
        order: [[ 'deviation', 'DESC' ]],
        limit: 250,
      });

      return stocks;
    });
  }

  // 高级缓存选项示例 - 强制刷新量化数据
  async refreshQuantData(method, params = {}, options = {}) {
    return await this.withCache('quantRealtime', method, params, async () => {
      // 根据方法名调用对应的查询方法
      switch (method) {
        case 'getToadyDaliy':
          return await this.getToadyDaliy();
        case 'getLastDaliy':
          return await this.getLastDaliy();
        case 'getLastRzrq':
          return await this.getLastRzrq();
        case 'getBeixiang':
          return await this.getBeixiang(params.limit);
        case 'getTodayCNI':
          return await this.getTodayCNI();
        default:
          throw new Error(`Unknown method: ${method}`);
      }
    }, {
      forceRefresh: options.forceRefresh || false,
      customTtl: options.customTtl || null,
    });
  }

  // 获取量化统计信息
  async getQuantStats() {
    return await this.withCache('quantStats', 'getQuantStats', {}, async () => {
      const { StockDaliy, Quant, Csindex } = this.ctx.model;

      const totalStocks = await StockDaliy.count();
      const totalQuants = await Quant.count();
      const totalIndexes = await Csindex.count();

      const today = moment().format('YYYYMMDD');
      const todayStocks = await StockDaliy.count({
        where: {
          date: today,
          isOpen: true,
        },
      });

      return {
        totalStocks,
        totalQuants,
        totalIndexes,
        todayStocks,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 获取量化数据趋势
  async getQuantTrends(days = 30) {
    return await this.withCache('quantStats', 'getQuantTrends', { days }, async () => {
      const { StockDaliy } = this.ctx.model;

      const startDate = moment().subtract(days, 'days').format('YYYYMMDD');
      const trends = await StockDaliy.findAll({
        where: {
          date: {
            [Op.gte]: startDate,
          },
          isOpen: true,
        },
        attributes: [
          'date',
          'volume',
          'rzrqye',
          'NorthMoney',
          'crowding',
        ],
        order: [[ 'date', 'ASC' ]],
      });

      return {
        trends,
        days,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 批量获取量化数据
  async getBatchQuantData(methods) {
    const promises = methods.map(({ method, params = {} }) => {
      switch (method) {
        case 'getStockDaliy':
          return this.getStockDaliy(params.limit);
        case 'getToadyDaliy':
          return this.getToadyDaliy();
        case 'getLastDaliy':
          return this.getLastDaliy();
        case 'getRzrqye':
          return this.getRzrqye(params.limit);
        case 'getBeixiang':
          return this.getBeixiang(params.limit);
        case 'getCjl':
          return this.getCjl(params.limit);
        case 'getConfig':
          return this.getConfig();
        case 'getStockList':
          return this.getStockList(params);
        default:
          return Promise.resolve(null);
      }
    });

    return await Promise.all(promises);
  }

  // 获取服务缓存统计
  async getServiceStats() {
    const stats = await this.getCacheStats('quants');
    const health = await this.getCacheHealth('quants');

    return {
      stats,
      health,
      timestamp: new Date().toISOString(),
    };
  }

  // 清理过期缓存
  async cleanupServiceCache() {
    await this.cleanupExpiredCache('quants');
    return { success: true, message: 'Quants cache cleaned up' };
  }

  // 批量刷新缓存
  async refreshServiceCache(operations) {
    await this.batchRefreshCache(operations);
    return { success: true, message: 'Quants cache refreshed' };
  }

  // 自定义缓存策略 - 针对量化数据的特殊处理
  async applyCacheStrategy(cacheKey, strategy, data) {
    // 调用父类策略
    await super.applyCacheStrategy(cacheKey, strategy, data);

    // 添加量化特定的策略逻辑
    if (strategy === 'aggressive') {
      // 对于激进策略，预加载相关量化数据
      await this.aggressiveQuantWarmup(cacheKey, data);
    } else if (strategy === 'conservative') {
      // 对于保守策略，压缩量化数据
      await this.conservativeQuantCompression(cacheKey, data);
    }
  }

  // 激进策略预热 - 预加载相关量化数据
  async aggressiveQuantWarmup(cacheKey, data) {
    try {
      // 如果是股票数据，预加载相关指数数据
      if (data && data.length > 0 && data[0].date) {
        const dates = [ ...new Set(data.map(item => item.date)) ];
        const relatedKey = `quants:related:dates:${dates.join(',')}`;

        await this.app.redis.setex(relatedKey, 60, JSON.stringify({
          relatedDates: dates,
          timestamp: new Date().toISOString(),
        }));
      }
      this.ctx.logger.info(`Aggressive quant warmup for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Aggressive quant warmup error:', error);
    }
  }

  // 保守策略压缩 - 压缩量化数据
  async conservativeQuantCompression(cacheKey, data) {
    try {
      // 对大型量化数据进行压缩存储
      if (data && JSON.stringify(data).length > 8000) {
        const compressedKey = `${cacheKey}:compressed`;
        const compressedData = {
          compressed: true,
          originalSize: JSON.stringify(data).length,
          compressedSize: Math.floor(JSON.stringify(data).length * 0.5), // 模拟压缩
          timestamp: new Date().toISOString(),
        };

        await this.app.redis.setex(compressedKey, 3600, JSON.stringify(compressedData));
      }
      this.ctx.logger.info(`Conservative quant compression for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Conservative quant compression error:', error);
    }
  }

  // 获取量化数据对比
  async getQuantComparison(params = {}) {
    return await this.withCache('quantStats', 'getQuantComparison', params, async () => {
      const { StockDaliy } = this.ctx.model;

      const { days = 7, compareDays = 30 } = params;

      const currentData = await StockDaliy.findAll({
        where: {
          date: {
            [Op.gte]: moment().subtract(days, 'days').format('YYYYMMDD'),
          },
          isOpen: true,
        },
        attributes: [
          'date',
          'volume',
          'rzrqye',
          'NorthMoney',
          'crowding',
        ],
        order: [[ 'date', 'DESC' ]],
      });

      const compareData = await StockDaliy.findAll({
        where: {
          date: {
            [Op.gte]: moment().subtract(compareDays, 'days').format('YYYYMMDD'),
            [Op.lt]: moment().subtract(days, 'days').format('YYYYMMDD'),
          },
          isOpen: true,
        },
        attributes: [
          'date',
          'volume',
          'rzrqye',
          'NorthMoney',
          'crowding',
        ],
        order: [[ 'date', 'DESC' ]],
      });

      return {
        current: currentData,
        compare: compareData,
        days,
        compareDays,
        timestamp: new Date().toISOString(),
      };
    });
  }

  // 获取量化数据预警
  async getQuantAlerts() {
    return await this.withCache('quantRealtime', 'getQuantAlerts', {}, async () => {
      const { StockDaliy } = this.ctx.model;

      const today = moment().format('YYYYMMDD');
      const todayData = await StockDaliy.findByPk(today);

      const alerts = [];

      if (todayData) {
        // 成交量预警
        if (todayData.volume > 1000000000) {
          alerts.push({
            type: 'volume',
            level: 'high',
            message: '成交量异常放大',
            value: todayData.volume,
          });
        }

        // 融资融券预警
        if (todayData.rzrqye > 1500000000000) {
          alerts.push({
            type: 'rzrqye',
            level: 'high',
            message: '融资融券余额过高',
            value: todayData.rzrqye,
          });
        }

        // 拥挤度预警
        if (todayData.crowding > 0.8) {
          alerts.push({
            type: 'crowding',
            level: 'high',
            message: '市场拥挤度过高',
            value: todayData.crowding,
          });
        }
      }

      return {
        alerts,
        timestamp: new Date().toISOString(),
      };
    });
  }
}

module.exports = QuantsService;
