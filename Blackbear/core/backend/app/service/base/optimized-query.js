'use strict';

const { Service } = require('egg');

/**
 * 优化查询服务基类
 * 提供查询缓存、性能监控、批量操作等功能
 */
/**
 * OptimizedQueryService
 * Business logic service
 */
class OptimizedQueryService extends Service {

  constructor(ctx) {
    super(ctx);
    this.cachePrefix = 'query_cache:';
    this.defaultTTL = 300; // 5分钟
    this.slowQueryThreshold = 1000; // 1秒
  }

  /**
   * 带缓存的查询包装器
   * @param {string} cacheKey - 缓存键
   * @param {Function} queryFn - 查询函数
   * @param {Object} options - 选项
   * @return {Promise} 查询结果
   */
  async withCache(cacheKey, queryFn, options = {}) {
    const {
      ttl = this.defaultTTL,
      forceRefresh = false,
      enableCache = true,
    } = options;

    const fullCacheKey = `${this.cachePrefix}${cacheKey}`;

    try {
      // 如果禁用缓存或强制刷新，直接执行查询
      if (!enableCache || forceRefresh) {
        return await this.executeWithMonitoring(queryFn, cacheKey);
      }

      // 尝试从缓存获取
      const cached = await this.app.redis.get(fullCacheKey);
      if (cached) {
        this.ctx.logger.debug(`Cache hit for key: ${cacheKey}`);
        return JSON.parse(cached);
      }

      // 缓存未命中，执行查询
      this.ctx.logger.debug(`Cache miss for key: ${cacheKey}`);
      const result = await this.executeWithMonitoring(queryFn, cacheKey);

      // 存储到缓存
      if (result !== null && result !== undefined) {
        await this.app.redis.setex(fullCacheKey, ttl, JSON.stringify(result));
      }

      return result;

    } catch (error) {
      this.ctx.logger.error(`Query error for key ${cacheKey}:`, error);
      throw error;
    }
  }

  /**
   * 带性能监控的查询执行
   * @param {Function} queryFn - 查询函数
   * @param {string} queryKey - 查询标识
   * @return {Promise} 查询结果
   */
  async executeWithMonitoring(queryFn, queryKey) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    try {
      const result = await queryFn();
      const executionTime = Date.now() - startTime;
      const endMemory = process.memoryUsage();

      // 记录性能指标
      this.recordPerformanceMetrics({
        queryKey,
        executionTime,
        memoryDelta: endMemory.heapUsed - startMemory.heapUsed,
        success: true,
      });

      // 检查慢查询
      if (executionTime > this.slowQueryThreshold) {
        this.recordSlowQuery(queryKey, executionTime);
      }

      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;

      // 记录失败的查询
      this.recordPerformanceMetrics({
        queryKey,
        executionTime,
        success: false,
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * 批量查询优化
   * @param {Array} queries - 查询数组
   * @param {Object} options - 选项
   * @return {Promise} 查询结果数组
   */
  async batchQuery(queries, options = {}) {
    const { useTransaction = true, concurrency = 5 } = options;

    if (useTransaction) {
      const transaction = await this.ctx.model.transaction();
      try {
        const results = await this.executeBatchWithConcurrency(
          queries.map(query => () => query.execute({ transaction })),
          concurrency
        );
        await transaction.commit();
        return results;
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } else {
      return await this.executeBatchWithConcurrency(
        queries.map(query => () => query.execute()),
        concurrency
      );
    }
  }

  /**
   * 并发控制的批量执行
   * @param {Array} tasks - 任务数组
   * @param {number} concurrency - 并发数
   * @return {Promise} 结果数组
   */
  async executeBatchWithConcurrency(tasks, concurrency) {
    const results = [];
    const executing = [];

    for (const task of tasks) {
      const promise = task().then(result => {
        executing.splice(executing.indexOf(promise), 1);
        return result;
      });

      results.push(promise);
      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
      }
    }

    return Promise.all(results);
  }

  /**
   * 游标分页查询（适用于大数据量）
   * @param {Object} model - Sequelize模型
   * @param {Object} options - 查询选项
   * @return {Promise} 分页结果
   */
  async cursorPagination(model, options = {}) {
    const {
      cursor,
      limit = 20,
      cursorField = 'id',
      direction = 'ASC',
      where = {},
      include = [],
      attributes,
    } = options;

    const { Op } = this.app.Sequelize;
    const whereClause = { ...where };

    // 添加游标条件
    if (cursor) {
      const operator = direction === 'ASC' ? Op.gt : Op.lt;
      whereClause[cursorField] = { [operator]: cursor };
    }

    const results = await model.findAll({
      where: whereClause,
      include,
      attributes,
      limit: limit + 1, // 多查询一条用于判断是否有下一页
      order: [[ cursorField, direction ]],
    });

    const hasNextPage = results.length > limit;
    const items = hasNextPage ? results.slice(0, -1) : results;
    const nextCursor = items.length > 0 ? items[items.length - 1][cursorField] : null;

    return {
      items,
      hasNextPage,
      nextCursor,
      totalCount: items.length,
    };
  }

  /**
   * 智能查询优化器
   * @param {Object} queryOptions - 查询选项
   * @return {Object} 优化后的查询选项
   */
  optimizeQuery(queryOptions) {
    const optimized = { ...queryOptions };

    // 优化include关联查询
    if (optimized.include) {
      optimized.include = this.optimizeIncludes(optimized.include);
    }

    // 优化attributes字段选择
    if (!optimized.attributes) {
      optimized.attributes = { exclude: [ 'deletedAt', 'createdAt', 'updatedAt' ] };
    }

    // 添加查询提示
    if (!optimized.benchmark) {
      optimized.benchmark = true;
    }

    return optimized;
  }

  /**
   * 优化关联查询
   * @param {Array} includes - 关联查询数组
   * @return {Array} 优化后的关联查询
   */
  optimizeIncludes(includes) {
    return includes.map(include => {
      const optimizedInclude = { ...include };

      // 限制关联数据数量
      if (!optimizedInclude.limit && !optimizedInclude.required) {
        optimizedInclude.limit = 10;
      }

      // 排除不必要的字段
      if (!optimizedInclude.attributes) {
        optimizedInclude.attributes = { exclude: [ 'deletedAt' ] };
      }

      // 递归优化嵌套关联
      if (optimizedInclude.include) {
        optimizedInclude.include = this.optimizeIncludes(optimizedInclude.include);
      }

      return optimizedInclude;
    });
  }

  /**
   * 清除相关缓存
   * @param {string|Array} patterns - 缓存键模式
   */
  async clearCache(patterns) {
    const patternsArray = Array.isArray(patterns) ? patterns : [ patterns ];

    for (const pattern of patternsArray) {
      const keys = await this.app.redis.keys(`${this.cachePrefix}${pattern}*`);
      if (keys.length > 0) {
        await this.app.redis.del(...keys);
        this.ctx.logger.info(`Cleared ${keys.length} cache keys for pattern: ${pattern}`);
      }
    }
  }

  /**
   * 记录性能指标
   * @param {Object} metrics - 性能指标
   */
  recordPerformanceMetrics(metrics) {
    // 发送到性能监控系统
    this.app.emit('performance-metrics', {
      ...metrics,
      timestamp: new Date(),
      service: this.constructor.name,
      userId: this.ctx.userInfo?.id,
      ip: this.ctx.ip,
    });
  }

  /**
   * 记录慢查询
   * @param {string} queryKey - 查询标识
   * @param {number} executionTime - 执行时间
   */
  recordSlowQuery(queryKey, executionTime) {
    this.ctx.logger.warn(`Slow query detected: ${queryKey}, execution time: ${executionTime}ms`);

    // 发送到监控系统
    this.app.emit('slow-query', {
      queryKey,
      executionTime,
      timestamp: new Date(),
      service: this.constructor.name,
      stack: new Error().stack,
    });
  }

  /**
   * 获取查询统计信息
   * @return {Object} 统计信息
   */
  async getQueryStats() {
    const cacheKeys = await this.app.redis.keys(`${this.cachePrefix}*`);

    return {
      totalCacheKeys: cacheKeys.length,
      cacheHitRate: await this.calculateCacheHitRate(),
      averageQueryTime: await this.getAverageQueryTime(),
      slowQueryCount: await this.getSlowQueryCount(),
    };
  }

  /**
   * 计算缓存命中率
   * @return {number} 命中率百分比
   */
  async calculateCacheHitRate() {
    // 这里需要根据实际的监控数据来计算
    // 简化实现，实际项目中应该有更详细的统计
    return 85.5; // 示例值
  }

  /**
   * 获取平均查询时间
   * @return {number} 平均时间（毫秒）
   */
  async getAverageQueryTime() {
    // 简化实现
    return 150; // 示例值
  }

  /**
   * 获取慢查询数量
   * @return {number} 慢查询数量
   */
  async getSlowQueryCount() {
    // 简化实现
    return 5; // 示例值
  }
}

module.exports = OptimizedQueryService;
