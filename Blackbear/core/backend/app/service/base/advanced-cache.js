'use strict';

const { Service } = require('egg');

/**
 * 高级缓存管理服务
 * 提供多级缓存、缓存预热、智能失效等功能
 */
/**
 * AdvancedCacheService
 * Business logic service
 */
class AdvancedCacheService extends Service {

  constructor(ctx) {
    super(ctx);
    this.cacheConfig = {
      // 缓存层级配置
      levels: {
        L1: { ttl: 60, storage: 'memory' }, // 1分钟内存缓存
        L2: { ttl: 300, storage: 'redis' }, // 5分钟Redis缓存
        L3: { ttl: 3600, storage: 'redis' }, // 1小时持久缓存
      },
      // 缓存键前缀
      prefixes: {
        hot: 'hot_data:',
        user: 'user_data:',
        stock: 'stock_data:',
        news: 'news_data:',
        system: 'system_data:',
      },
      // 缓存策略
      strategies: {
        writeThrough: 'write_through',
        writeBack: 'write_back',
        writeAround: 'write_around',
      },
    };

    // 内存缓存（L1）
    this.memoryCache = new Map();
    this.memoryCacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
    };
  }

  /**
   * 多级缓存获取
   * @param {string} key - 缓存键
   * @param {Function} fetcher - 数据获取函数
   * @param {Object} options - 缓存选项
   * @return {Promise} 缓存数据
   */
  async getWithMultiLevelCache(key, fetcher, options = {}) {
    const {
      category = 'system',
      enableL1 = true,
      enableL2 = true,
      enableL3 = false,
      forceRefresh = false,
    } = options;

    const fullKey = this.buildCacheKey(key, category);

    try {
      // 强制刷新时跳过所有缓存
      if (forceRefresh) {
        return await this.fetchAndCache(fullKey, fetcher, options);
      }

      // L1: 内存缓存
      if (enableL1) {
        const l1Data = this.getFromMemoryCache(fullKey);
        if (l1Data !== null) {
          this.memoryCacheStats.hits++;
          this.recordCacheHit('L1', fullKey);
          return l1Data;
        }
        this.memoryCacheStats.misses++;
      }

      // L2: Redis缓存
      if (enableL2) {
        const l2Data = await this.getFromRedisCache(fullKey);
        if (l2Data !== null) {
          // 回填L1缓存
          if (enableL1) {
            this.setToMemoryCache(fullKey, l2Data, this.cacheConfig.levels.L1.ttl);
          }
          this.recordCacheHit('L2', fullKey);
          return l2Data;
        }
      }

      // L3: 持久Redis缓存
      if (enableL3) {
        const l3Key = `${fullKey}:persistent`;
        const l3Data = await this.getFromRedisCache(l3Key);
        if (l3Data !== null) {
          // 回填L1和L2缓存
          if (enableL1) {
            this.setToMemoryCache(fullKey, l3Data, this.cacheConfig.levels.L1.ttl);
          }
          if (enableL2) {
            await this.setToRedisCache(fullKey, l3Data, this.cacheConfig.levels.L2.ttl);
          }
          this.recordCacheHit('L3', fullKey);
          return l3Data;
        }
      }

      // 所有缓存都未命中，获取数据并缓存
      this.recordCacheMiss(fullKey);
      return await this.fetchAndCache(fullKey, fetcher, options);

    } catch (error) {
      this.ctx.logger.error(`Multi-level cache error for key ${fullKey}:`, error);
      // 缓存失败时直接返回数据
      return await fetcher();
    }
  }

  /**
   * 获取数据并缓存到多个层级
   * @param {string} fullKey - 完整缓存键
   * @param {Function} fetcher - 数据获取函数
   * @param {Object} options - 选项
   * @return {Promise} 数据
   */
  async fetchAndCache(fullKey, fetcher, options) {
    const {
      enableL1 = true,
      enableL2 = true,
      enableL3 = false,
      strategy = this.cacheConfig.strategies.writeThrough,
    } = options;

    const data = await fetcher();

    if (data !== null && data !== undefined) {
      // 根据策略缓存数据
      if (strategy === this.cacheConfig.strategies.writeThrough) {
        await this.cacheToAllLevels(fullKey, data, { enableL1, enableL2, enableL3 });
      } else if (strategy === this.cacheConfig.strategies.writeBack) {
        // 延迟写入，先缓存到L1
        if (enableL1) {
          this.setToMemoryCache(fullKey, data, this.cacheConfig.levels.L1.ttl);
        }
        // 异步写入其他层级
        setImmediate(() => {
          this.cacheToAllLevels(fullKey, data, { enableL1: false, enableL2, enableL3 });
        });
      }
    }

    return data;
  }

  /**
   * 缓存到所有启用的层级
   * @param {string} fullKey - 缓存键
   * @param {*} data - 数据
   * @param {Object} levels - 启用的层级
   */
  async cacheToAllLevels(fullKey, data, levels) {
    const promises = [];

    if (levels.enableL1) {
      this.setToMemoryCache(fullKey, data, this.cacheConfig.levels.L1.ttl);
    }

    if (levels.enableL2) {
      promises.push(
        this.setToRedisCache(fullKey, data, this.cacheConfig.levels.L2.ttl)
      );
    }

    if (levels.enableL3) {
      promises.push(
        this.setToRedisCache(`${fullKey}:persistent`, data, this.cacheConfig.levels.L3.ttl)
      );
    }

    await Promise.all(promises);
  }

  /**
   * 缓存预热
   * @param {Array} preloadTasks - 预加载任务
   */
  async warmupCache(preloadTasks) {
    this.ctx.logger.info(`Starting cache warmup with ${preloadTasks.length} tasks`);

    const results = await Promise.allSettled(
      preloadTasks.map(async task => {
        try {
          const { key, fetcher, options = {} } = task;
          await this.getWithMultiLevelCache(key, fetcher, {
            ...options,
            forceRefresh: true,
          });
          return { key, status: 'success' };
        } catch (error) {
          this.ctx.logger.error(`Cache warmup failed for key ${task.key}:`, error);
          return { key: task.key, status: 'failed', error: error.message };
        }
      })
    );

    const successful = results.filter(r => r.value?.status === 'success').length;
    this.ctx.logger.info(`Cache warmup completed: ${successful}/${preloadTasks.length} successful`);

    return results;
  }

  /**
   * 智能缓存失效
   * @param {string|Array} patterns - 失效模式
   * @param {Object} options - 选项
   */
  async invalidateCache(patterns, options = {}) {
    const {
      levels = [ 'L1', 'L2', 'L3' ],
      cascade = true,
      async = false,
    } = options;

    const patternsArray = Array.isArray(patterns) ? patterns : [ patterns ];

    const invalidateTask = async () => {
      for (const pattern of patternsArray) {
        // L1内存缓存失效
        if (levels.includes('L1')) {
          this.invalidateMemoryCache(pattern);
        }

        // L2和L3 Redis缓存失效
        if (levels.includes('L2') || levels.includes('L3')) {
          await this.invalidateRedisCache(pattern, levels);
        }

        // 级联失效相关缓存
        if (cascade) {
          await this.cascadeInvalidate(pattern);
        }
      }
    };

    if (async) {
      setImmediate(invalidateTask);
    } else {
      await invalidateTask();
    }
  }

  /**
   * 构建缓存键
   * @param {string} key - 原始键
   * @param {string} category - 分类
   * @return {string} 完整缓存键
   */
  buildCacheKey(key, category) {
    const prefix = this.cacheConfig.prefixes[category] || this.cacheConfig.prefixes.system;
    return `${prefix}${key}`;
  }

  /**
   * 内存缓存操作
   * @param key
   */
  getFromMemoryCache(key) {
    const item = this.memoryCache.get(key);
    if (!item) { return null; }

    if (Date.now() > item.expiry) {
      this.memoryCache.delete(key);
      return null;
    }

    return item.data;
  }

  setToMemoryCache(key, data, ttl) {
    const expiry = Date.now() + (ttl * 1000);
    this.memoryCache.set(key, { data, expiry });
    this.memoryCacheStats.sets++;

    // 限制内存缓存大小
    if (this.memoryCache.size > 1000) {
      this.cleanupMemoryCache();
    }
  }

  invalidateMemoryCache(pattern) {
    const keys = Array.from(this.memoryCache.keys());
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));

    let deleted = 0;
    for (const key of keys) {
      if (regex.test(key)) {
        this.memoryCache.delete(key);
        deleted++;
      }
    }

    this.memoryCacheStats.deletes += deleted;
    this.ctx.logger.debug(`Invalidated ${deleted} memory cache entries for pattern: ${pattern}`);
  }

  cleanupMemoryCache() {
    const now = Date.now();
    const keys = Array.from(this.memoryCache.keys());

    let cleaned = 0;
    for (const key of keys) {
      const item = this.memoryCache.get(key);
      if (!item || now > item.expiry) {
        this.memoryCache.delete(key);
        cleaned++;
      }
    }

    this.ctx.logger.debug(`Cleaned up ${cleaned} expired memory cache entries`);
  }

  /**
   * Redis缓存操作
   * @param key
   */
  async getFromRedisCache(key) {
    try {
      const data = await this.app.redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      this.ctx.logger.error(`Redis get error for key ${key}:`, error);
      return null;
    }
  }

  async setToRedisCache(key, data, ttl) {
    try {
      await this.app.redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      this.ctx.logger.error(`Redis set error for key ${key}:`, error);
    }
  }

  async invalidateRedisCache(pattern, levels) {
    try {
      const keys = await this.app.redis.keys(`*${pattern}*`);
      if (keys.length > 0) {
        await this.app.redis.del(...keys);
        this.ctx.logger.debug(`Invalidated ${keys.length} Redis cache entries for pattern: ${pattern}`);
      }
    } catch (error) {
      this.ctx.logger.error(`Redis invalidation error for pattern ${pattern}:`, error);
    }
  }

  /**
   * 级联失效相关缓存
   * @param {string} pattern - 失效模式
   */
  async cascadeInvalidate(pattern) {
    // 根据业务逻辑定义相关缓存的失效规则
    const cascadeRules = {
      'stock_data:': [ 'user_data:portfolio*', 'hot_data:stocks*' ],
      'user_data:': [ 'system_data:user_stats*' ],
      'news_data:': [ 'hot_data:news*', 'stock_data:news*' ],
    };

    for (const [ triggerPattern, relatedPatterns ] of Object.entries(cascadeRules)) {
      if (pattern.includes(triggerPattern)) {
        for (const relatedPattern of relatedPatterns) {
          await this.invalidateCache(relatedPattern, { cascade: false });
        }
      }
    }
  }

  /**
   * 记录缓存命中
   * @param {string} level - 缓存层级
   * @param {string} key - 缓存键
   */
  recordCacheHit(level, key) {
    this.app.emit('cache-hit', {
      level,
      key,
      timestamp: new Date(),
      service: this.constructor.name,
    });
  }

  /**
   * 记录缓存未命中
   * @param {string} key - 缓存键
   */
  recordCacheMiss(key) {
    this.app.emit('cache-miss', {
      key,
      timestamp: new Date(),
      service: this.constructor.name,
    });
  }

  /**
   * 获取缓存统计信息
   * @return {Object} 统计信息
   */
  async getCacheStats() {
    const redisInfo = await this.app.redis.info('memory');
    const redisKeys = await this.app.redis.dbsize();

    return {
      memory: {
        ...this.memoryCacheStats,
        size: this.memoryCache.size,
        hitRate: this.memoryCacheStats.hits / (this.memoryCacheStats.hits + this.memoryCacheStats.misses) * 100,
      },
      redis: {
        keys: redisKeys,
        memory: this.parseRedisMemoryInfo(redisInfo),
      },
      timestamp: new Date(),
    };
  }

  /**
   * 解析Redis内存信息
   * @param {string} info - Redis info输出
   * @return {Object} 内存信息
   */
  parseRedisMemoryInfo(info) {
    const lines = info.split('\r\n');
    const memoryInfo = {};

    for (const line of lines) {
      if (line.includes('used_memory:')) {
        memoryInfo.used = parseInt(line.split(':')[1]);
      } else if (line.includes('used_memory_human:')) {
        memoryInfo.usedHuman = line.split(':')[1];
      }
    }

    return memoryInfo;
  }
}

module.exports = AdvancedCacheService;
