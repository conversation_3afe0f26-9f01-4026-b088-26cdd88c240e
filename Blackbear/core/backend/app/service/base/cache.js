'use strict';

const { Service } = require('egg');

/**
 * BaseCacheService
 * Business logic service
 */
class BaseCacheService extends Service {

  // 基础缓存配置 - 子类可以覆盖
  get cacheConfig() {
    return {
      // 实时数据 - 缓存时间很短，保证实时性
      realtime: {
        ttl: 30, // 30秒
        prefix: 'base:realtime:',
        description: '实时数据缓存',
        strategy: 'aggressive', // 激进策略：频繁更新
      },
      // 分钟级数据 - 稍微长一点的缓存
      minute: {
        ttl: 60, // 1分钟
        prefix: 'base:minute:',
        description: '分钟级数据缓存',
        strategy: 'balanced', // 平衡策略：适中更新
      },
      // 小时级数据 - 中等缓存时间
      hourly: {
        ttl: 300, // 5分钟
        prefix: 'base:hourly:',
        description: '小时级数据缓存',
        strategy: 'conservative', // 保守策略：较少更新
      },
      // 日级数据 - 较长缓存时间
      daily: {
        ttl: 1800, // 30分钟
        prefix: 'base:daily:',
        description: '日级数据缓存',
        strategy: 'conservative',
      },
      // 统计数据 - 较长缓存时间
      statistics: {
        ttl: 600, // 10分钟
        prefix: 'base:statistics:',
        description: '统计数据缓存',
        strategy: 'conservative',
      },
      // 排行榜数据 - 中等缓存时间
      ranking: {
        ttl: 120, // 2分钟
        prefix: 'base:ranking:',
        description: '排行榜数据缓存',
        strategy: 'balanced',
      },
      // 搜索数据 - 较短缓存时间
      search: {
        ttl: 90, // 1.5分钟
        prefix: 'base:search:',
        description: '搜索数据缓存',
        strategy: 'balanced',
      },
      // 详情数据 - 中等缓存时间
      detail: {
        ttl: 180, // 3分钟
        prefix: 'base:detail:',
        description: '详情数据缓存',
        strategy: 'balanced',
      },
      // 长期数据 - 很长缓存时间
      longterm: {
        ttl: 7200, // 2小时
        prefix: 'base:longterm:',
        description: '长期数据缓存',
        strategy: 'conservative',
      },
      // 永久数据 - 几乎不更新的数据
      permanent: {
        ttl: 86400, // 24小时
        prefix: 'base:permanent:',
        description: '永久数据缓存',
        strategy: 'conservative',
      },
    };
  }

  // 获取服务特定的缓存配置
  getServiceCacheConfig() {
    return this.cacheConfig;
  }

  // 获取缓存键
  getCacheKey(type, method, params = {}) {
    const config = this.getServiceCacheConfig()[type];
    if (!config) {
      throw new Error(`Unknown cache type: ${type}`);
    }

    const paramsStr = JSON.stringify(params);
    const hash = this.ctx.helper.md5(paramsStr);
    return `${config.prefix}${method}:${hash}`;
  }

  // 智能缓存包装器
  async withCache(type, method, params, queryFn, options = {}) {
    // 全局缓存开关
    if (this.app.config.cacheEnabled === false) {
      return await this.executeQuery(method, queryFn);
    }

    const cacheKey = this.getCacheKey(type, method, params);
    const config = this.getServiceCacheConfig()[type];

    if (!config) {
      throw new Error(`Unknown cache type: ${type}`);
    }

    // 合并选项
    const cacheOptions = {
      forceRefresh: false, // 强制刷新缓存
      skipCache: false, // 跳过缓存
      customTtl: null, // 自定义TTL
      ...options,
    };

    // 如果跳过缓存，直接执行查询
    if (cacheOptions.skipCache) {
      return await this.executeQuery(method, queryFn);
    }

    // 如果强制刷新，删除现有缓存
    if (cacheOptions.forceRefresh) {
      await this.app.redis.del(cacheKey);
    }

    try {
      // 尝试从缓存获取
      const cached = await this.app.redis.get(cacheKey);
      if (cached) {
        this.ctx.logger.info(`Cache hit for ${method} (${type})`);
        return JSON.parse(cached);
      }
    } catch (error) {
      this.ctx.logger.warn('Redis cache get error:', error);
    }

    // 执行查询并缓存结果
    const result = await this.executeQuery(method, queryFn);

    // 使用自定义TTL或配置TTL
    const ttl = cacheOptions.customTtl || config.ttl;
    this.setCacheAsync(cacheKey, ttl, result, config.strategy);

    return result;
  }

  // 执行查询并记录性能
  async executeQuery(method, queryFn) {
    const startTime = Date.now();
    const result = await queryFn();
    const queryTime = Date.now() - startTime;

    // 记录查询性能
    this.ctx.logger.info(`Query ${method} took ${queryTime}ms`);

    return result;
  }

  // 异步设置缓存（支持策略）
  async setCacheAsync(cacheKey, ttl, data, strategy = 'balanced') {
    try {
      await this.app.redis.setex(cacheKey, ttl, JSON.stringify(data));
      this.ctx.logger.info(`Cache set for key: ${cacheKey} (strategy: ${strategy})`);

      // 根据策略执行额外操作
      await this.applyCacheStrategy(cacheKey, strategy, data);
    } catch (error) {
      this.ctx.logger.warn('Redis cache set error:', error);
    }
  }

  // 应用缓存策略
  async applyCacheStrategy(cacheKey, strategy, data) {
    switch (strategy) {
      case 'aggressive':
        // 激进策略：设置缓存预热
        await this.setCacheWarmup(cacheKey, data);
        break;
      case 'balanced':
        // 平衡策略：设置缓存统计
        await this.updateCacheStats(cacheKey, 'set');
        break;
      case 'conservative':
        // 保守策略：设置缓存压缩
        await this.setCacheCompression(cacheKey, data);
        break;
      default:
        // 默认策略
        break;
    }
  }

  // 缓存预热
  async setCacheWarmup(cacheKey, data) {
    try {
      // 可以在这里实现缓存预热逻辑
      // 例如：预加载相关数据
      this.ctx.logger.info(`Cache warmup for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Cache warmup error:', error);
    }
  }

  // 更新缓存统计
  async updateCacheStats(cacheKey, operation) {
    try {
      const statsKey = `cache:stats:${operation}`;
      await this.app.redis.incr(statsKey);
      // 设置统计数据的过期时间
      await this.app.redis.expire(statsKey, 3600); // 1小时
    } catch (error) {
      this.ctx.logger.warn('Cache stats update error:', error);
    }
  }

  // 缓存压缩
  async setCacheCompression(cacheKey, data) {
    try {
      // 可以在这里实现数据压缩逻辑
      // 例如：对大数据进行压缩存储
      this.ctx.logger.info(`Cache compression for: ${cacheKey}`);
    } catch (error) {
      this.ctx.logger.warn('Cache compression error:', error);
    }
  }

  // 清除相关缓存
  async clearRelatedCache(servicePrefix, operation = {}) {
    try {
      const { code, type, method, strategy } = operation;

      if (code) {
        // 清除特定代码相关的缓存
        await this.clearCacheByPattern(`${servicePrefix}:*:*${code}*`);
      } else if (method) {
        // 清除特定方法的缓存
        await this.clearCacheByPattern(`${servicePrefix}:*:${method}:*`);
      } else if (type) {
        // 清除特定类型的缓存
        await this.clearCacheByPattern(`${servicePrefix}:${type}:*`);
      } else if (strategy) {
        // 清除特定策略的缓存
        await this.clearCacheByStrategy(servicePrefix, strategy);
      } else {
        // 清除所有服务缓存
        await this.clearCacheByPattern(`${servicePrefix}:*`);
      }

      this.ctx.logger.info(`${servicePrefix} cache cleared`, operation);
    } catch (error) {
      this.ctx.logger.error(`Failed to clear ${servicePrefix} cache`, error);
    }
  }

  // 根据策略清除缓存
  async clearCacheByStrategy(servicePrefix, strategy) {
    try {
      const config = this.getServiceCacheConfig();
      const strategyTypes = Object.keys(config).filter(type =>
        config[type].strategy === strategy
      );

      for (const type of strategyTypes) {
        await this.clearCacheByPattern(`${servicePrefix}:${type}:*`);
      }

      this.ctx.logger.info(`Cleared ${strategy} strategy cache for ${servicePrefix}`);
    } catch (error) {
      this.ctx.logger.error(`Failed to clear cache by strategy: ${strategy}`, error);
    }
  }

  // 根据模式清除缓存
  async clearCacheByPattern(pattern) {
    try {
      const keys = await this.app.redis.keys(pattern);
      if (keys.length > 0) {
        await this.app.redis.del(...keys);
        this.ctx.logger.info(`Cleared ${keys.length} cache keys with pattern: ${pattern}`);
      }
    } catch (error) {
      this.ctx.logger.error(`Failed to clear cache with pattern: ${pattern}`, error);
    }
  }

  // 获取缓存统计信息
  async getCacheStats(servicePrefix) {
    try {
      const stats = {};
      const config = this.getServiceCacheConfig();

      for (const [ type, typeConfig ] of Object.entries(config)) {
        const keys = await this.app.redis.keys(`${typeConfig.prefix}*`);
        stats[type] = {
          count: keys.length,
          ttl: typeConfig.ttl,
          strategy: typeConfig.strategy,
          description: typeConfig.description,
        };
      }

      // 获取总体统计
      const totalKeys = await this.app.redis.keys(`${servicePrefix}:*`);
      stats.total = {
        count: totalKeys.length,
        memory: await this.getCacheMemoryUsage(servicePrefix),
      };

      return stats;
    } catch (error) {
      this.ctx.logger.error(`Failed to get ${servicePrefix} cache stats`, error);
      return {};
    }
  }

  // 获取缓存内存使用情况
  async getCacheMemoryUsage(servicePrefix) {
    try {
      const keys = await this.app.redis.keys(`${servicePrefix}:*`);
      let totalMemory = 0;

      for (const key of keys) {
        const memory = await this.app.redis.memory('USAGE', key);
        totalMemory += memory || 0;
      }

      return totalMemory;
    } catch (error) {
      this.ctx.logger.warn('Failed to get cache memory usage:', error);
      return 0;
    }
  }

  // 强制刷新缓存
  async refreshCache(type, method, params = {}) {
    try {
      const cacheKey = this.getCacheKey(type, method, params);
      await this.app.redis.del(cacheKey);
      this.ctx.logger.info(`Forced refresh cache for ${type}:${method}`);
    } catch (error) {
      this.ctx.logger.error(`Failed to refresh cache for ${type}:${method}`, error);
    }
  }

  // 批量刷新缓存
  async batchRefreshCache(operations) {
    try {
      const promises = operations.map(op =>
        this.refreshCache(op.type, op.method, op.params)
      );
      await Promise.all(promises);
      this.ctx.logger.info(`Batch refreshed ${operations.length} caches`);
    } catch (error) {
      this.ctx.logger.error('Failed to batch refresh cache:', error);
    }
  }

  // 获取缓存健康状态
  async getCacheHealth(servicePrefix) {
    try {
      const stats = await this.getCacheStats(servicePrefix);
      const config = this.getServiceCacheConfig();

      const health = {
        status: 'healthy',
        issues: [],
        recommendations: [],
      };

      // 检查缓存命中率
      const hitRate = await this.getCacheHitRate(servicePrefix);
      if (hitRate < 0.5) {
        health.status = 'warning';
        health.issues.push('Low cache hit rate');
        health.recommendations.push('Consider adjusting TTL values');
      }

      // 检查内存使用
      if (stats.total && stats.total.memory > 100 * 1024 * 1024) { // 100MB
        health.status = 'warning';
        health.issues.push('High memory usage');
        health.recommendations.push('Consider implementing cache compression');
      }

      // 检查过期缓存
      const expiredKeys = await this.getExpiredCacheKeys(servicePrefix);
      if (expiredKeys.length > 100) {
        health.issues.push('Many expired cache keys');
        health.recommendations.push('Consider implementing cache cleanup');
      }

      return health;
    } catch (error) {
      this.ctx.logger.error('Failed to get cache health:', error);
      return { status: 'error', issues: [ 'Failed to check cache health' ] };
    }
  }

  // 获取缓存命中率
  async getCacheHitRate(servicePrefix) {
    try {
      const hits = await this.app.redis.get('cache:stats:hit') || 0;
      const misses = await this.app.redis.get('cache:stats:miss') || 0;
      const total = parseInt(hits) + parseInt(misses);

      return total > 0 ? parseInt(hits) / total : 0;
    } catch (error) {
      this.ctx.logger.warn('Failed to get cache hit rate:', error);
      return 0;
    }
  }

  // 获取过期缓存键
  async getExpiredCacheKeys(servicePrefix) {
    try {
      const keys = await this.app.redis.keys(`${servicePrefix}:*`);
      const expiredKeys = [];

      for (const key of keys) {
        const ttl = await this.app.redis.ttl(key);
        if (ttl === -1) { // 没有设置过期时间
          expiredKeys.push(key);
        }
      }

      return expiredKeys;
    } catch (error) {
      this.ctx.logger.warn('Failed to get expired cache keys:', error);
      return [];
    }
  }

  // 清理过期缓存
  async cleanupExpiredCache(servicePrefix) {
    try {
      const expiredKeys = await this.getExpiredCacheKeys(servicePrefix);
      if (expiredKeys.length > 0) {
        await this.app.redis.del(...expiredKeys);
        this.ctx.logger.info(`Cleaned up ${expiredKeys.length} expired cache keys`);
      }
    } catch (error) {
      this.ctx.logger.error('Failed to cleanup expired cache:', error);
    }
  }
}

module.exports = BaseCacheService;
