'use strict';

const { Service } = require('egg');

/**
 * CacheService
 * Business logic service
 */
class CacheService extends Service {
  async get(key) {
    const { redis } = this.app;
    let data = await redis.get(key);
    if (!data) { return; }
    data = JSON.parse(data);
    return data;
  }

  async setex(key, value, seconds) {
    const { redis } = this.app;
    value = JSON.stringify(value);
    if (!seconds) {
      await redis.set(key, value);
    } else {
      await redis.set(key, value, 'EX', seconds);
    }
  }

  async incr(key, seconds) {
    const { redis } = this.app;
    const result = await redis.multi().incr(key).expire(key, seconds)
      .exec();
    return result[0][1];
  }
}

module.exports = CacheService;
