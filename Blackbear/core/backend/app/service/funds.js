'use strict';

const BaseCacheService = require('./base/cache');
const { Op } = require('sequelize');

class FundsService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'funds:minute:',
        description: '基金列表数据',
        strategy: 'balanced',
      },
    };
  }

  // 列表，带缓存
  async find(params) {
    return await this.withCache('minute', 'find', params, async () => {
      const { ctx } = this;
      const { Stock, Source, Fund, FundStock } = this.ctx.model;
      const { pageIndex, pageSize, sortField, sortOrder, text } = params;
      let order = [[ 'code', 'ASC' ]];
      const where = {};
      if (sortField && sortOrder) {
        order = [[ sortField, sortOrder ]];
      }
      if (text) {
        Object.assign(where, {
          [Op.or]: [
            { code: { [Op.like]: `%${text}%` } },
            { name: { [Op.like]: `%${text}%` } },
          ],
        });
      }
      return await Fund.findAndCountAll({
        distinct: true,
        where: { ...where },
        order,
        include: [{
          model: FundStock,
          include: [{
            model: Stock,
            include: [{ model: Source }],
          }],
        }],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }
}

module.exports = FundsService;
