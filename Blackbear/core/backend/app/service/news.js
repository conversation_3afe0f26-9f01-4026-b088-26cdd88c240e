'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;
const moment = require('moment');

// 统一缓存配置
class NewsService extends BaseCacheService {
  get cacheConfig() {
    return {
      realtime: {
        ttl: 30,
        prefix: 'news:realtime:',
        description: '新闻实时数据',
        strategy: 'aggressive',
      },
      minute: {
        ttl: 60,
        prefix: 'news:minute:',
        description: '新闻分钟级数据',
        strategy: 'balanced',
      },
      hourly: {
        ttl: 300,
        prefix: 'news:hourly:',
        description: '新闻小时级数据',
        strategy: 'conservative',
      },
      daily: {
        ttl: 1800,
        prefix: 'news:daily:',
        description: '新闻日级数据',
        strategy: 'conservative',
      },
      statistics: {
        ttl: 600,
        prefix: 'news:statistics:',
        description: '新闻统计数据',
        strategy: 'conservative',
      },
      search: {
        ttl: 90,
        prefix: 'news:search:',
        description: '新闻搜索数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'news:detail:',
        description: '新闻详情数据',
        strategy: 'balanced',
      },
    };
  }

  // 查询新闻详情
  async show(id) {
    return await this.withCache('detail', 'show', { id }, async () => {
      return await this.ctx.model.News.findOne({
        where: { newsID: id },
      });
    });
  }

  // 更新新闻（写操作不缓存，清除相关缓存）
  async update(id, params) {
    await this.ctx.model.News.update({ ...params }, {
      where: { newsID: id },
      individualHooks: true,
    });
    await this.clearRelatedCache('news', { method: 'update', id });
  }

  // 创建新闻
  async create(params) {
    // 支持传入对象参数
    const news = await this.ctx.model.News.create(params);
    await this.clearRelatedCache('news', { method: 'create', id: news.newsID });
    return news;
  }

  // 删除新闻
  async delete(id) {
    const result = await this.ctx.model.News.destroy({
      where: { newsID: id },
    });
    await this.clearRelatedCache('news', { method: 'delete', id });
    return result;
  }

  // 获取未读新闻数量
  async get_count() {
    return await this.withCache('statistics', 'get_count', {}, async () => {
      return await this.ctx.model.News.count({
        where: { read: false },
      });
    });
  }

  // 全部标记为已读
  async allread() {
    await this.ctx.model.News.update({ read: true }, { where: { read: false } });
    await this.clearRelatedCache('news', { method: 'allread' });
    return true;
  }

  // 标记单条为已读
  async read(id, params) {
    await this.ctx.model.News.update({ read: true }, { where: { newsID: id } });
    await this.clearRelatedCache('news', { method: 'read', id });
    return true;
  }

  // 获取随机新闻
  async getRandom(num, ex) {
    return await this.withCache('minute', 'getRandom', { num, ex }, async () => {
      return await this.ctx.model.News.findAll({
        where: {
          read: false,
          newsID: { [Op.notIn]: ex.split(',') },
        },
        order: [[ 'time', 'DESC' ]],
        limit: num,
        include: [{
          model: this.ctx.model.NewsStock,
          attributes: { exclude: [ 'createdAt', 'updatedAt', 'deletedAt' ] },
          include: {
            model: this.ctx.model.Stock,
            attributes: [ 'code' ],
            include: {
              model: this.ctx.model.Source,
              attributes: [ 'displayName', 'code', 'f3' ],
            },
          },
        }],
      });
    });
  }

  // 获取重要新闻
  async getImportantNews(num) {
    return await this.withCache('minute', 'getImportantNews', { num }, async () => {
      return await this.ctx.model.News.findAll({
        where: {
          important: true,
          time: { [Op.gt]: moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss') },
        },
        order: [[ 'newsID', 'DESC' ]],
        limit: num,
        include: [{
          model: this.ctx.model.NewsStock,
          attributes: { exclude: [ 'createdAt', 'updatedAt', 'deletedAt' ] },
          include: {
            model: this.ctx.model.Stock,
            attributes: [ 'code' ],
            include: {
              model: this.ctx.model.Source,
              attributes: [ 'displayName', 'code', 'f3' ],
            },
          },
        }],
      });
    });
  }

  // 新闻列表分页查询
  async findAllNews(params) {
    return await this.withCache('minute', 'findAllNews', params, async () => {
      const { NewsStock, Stock, Source, Aggregation } = this.ctx.model;
      const { pageIndex, pageSize, text, newsid, unread, important, source, type } = params;
      const where = {};
      if (source) { where.source = source; }
      if (type) { where.type = type; }
      if (unread === 'true') { where.read = false; }
      if (important === 'true') { where.important = true; }
      if (text) { where.content = { [Op.like]: `%${text}%` }; }
      if (newsid) { where.newsID = { [Op.in]: newsid.split(',') }; }
      return await this.ctx.model.News.findAndCountAll({
        order: [[ 'newsID', 'DESC' ]],
        distinct: true,
        where,
        attributes: { exclude: [ 'deletedAt' ] },
        include: [
          { model: NewsStock, include: [{ model: Stock, include: [ Source ] }] },
          { model: Aggregation },
        ],
        offset: (parseInt(pageIndex) - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
    });
  }

  async findByText(text) {
    return await this.withCache('search', 'findByText', { text }, async () => {
      const {
        News,
        NewsStock,
        Stock,
        Source,
      } = this.ctx.model;

      const news = await News.findAll({
        order: [[ 'newsID', 'DESC' ]],
        distinct: true,
        where: {
          content: {
            [Op.like]: `%${text}%`,
          },
        },
        include: [{
          model: NewsStock,
          attributes: {
            exclude: [ 'createdAt', 'updatedAt', 'deletedAt' ],
          },
          include: {
            model: Stock,
            attributes: [ 'code' ],
            include: {
              model: Source,
              attributes: [ 'displayName', 'code', 'f3' ],
            },
          },
        }],
        offset: 0,
        limit: 20,
      });

      return news;
    });
  }

  // 其它原有方法可按需继续补充缓存
}

module.exports = NewsService;
