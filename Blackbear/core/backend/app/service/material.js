'use strict';

const BaseCacheService = require('./base/cache');
const Sequelize = require('sequelize');
const { Op } = Sequelize;

class MaterialService extends BaseCacheService {
  // 统一缓存配置
  get cacheConfig() {
    return {
      ...super.cacheConfig,
      minute: {
        ttl: 60,
        prefix: 'material:minute:',
        description: '素材列表数据',
        strategy: 'balanced',
      },
      detail: {
        ttl: 180,
        prefix: 'material:detail:',
        description: '素材详情数据',
        strategy: 'balanced',
      },
      search: {
        ttl: 90,
        prefix: 'material:search:',
        description: '素材搜索数据',
        strategy: 'balanced',
      },
    };
  }

  // 列表查询，带缓存
  async find(params) {
    return await this.withCache('minute', 'find', params, async () => {
      const { pageIndex, pageSize, category } = params;
      let where = {};
      if (category) {
        where = Object.assign(where, { category });
      }
      const materials = await this.ctx.model.Material.findAndCountAll({
        where,
        order: [[ 'id', 'DESC' ]],
        offset: parseInt(pageIndex - 1) * parseInt(pageSize),
        limit: parseInt(pageSize),
      });
      return materials;
    });
  }

  // 新增，写操作后清除相关缓存
  async create(params) {
    const m = await this.ctx.model.Material.findOrCreate({
      where: params,
    });
    await this.clearRelatedCache('material');
    return m;
  }
}

module.exports = MaterialService;
