'use strict';

const { Service } = require('egg');

/**
 * CacheManagerService
 * Business logic service
 */
class CacheManagerService extends Service {

  // 缓存前缀配置
  get cachePrefixes() {
    return {
      stocks: 'stocks:',
      news: 'news:',
      reports: 'reports:',
      research: 'research:',
      industry: 'industry:',
      statistics: 'statistics:',
      sources: 'sources:',
      tags: 'tags:',
      groups: 'groups:',
      quants: 'quants:',
      messages: 'messages:',
    };
  }

  // 清除所有股票相关缓存
  async clearStockCache(options = {}) {
    const { code, method, type } = options;

    try {
      // 使用BaseCacheService的清除方法
      await this.ctx.service.stocks.clearRelatedCache('stocks', options);
      this.ctx.logger.info('Stock cache cleared', { code, method, type });
    } catch (error) {
      this.ctx.logger.error('Failed to clear stock cache', error);
    }
  }

  // 清除新闻相关缓存
  async clearNewsCache(options = {}) {
    const { newsId, code } = options;

    try {
      if (newsId) {
        await this.clearCacheByPattern(`${this.cachePrefixes.news}*:${newsId}*`);
      } else if (code) {
        await this.clearCacheByPattern(`${this.cachePrefixes.news}*:${code}*`);
      } else {
        await this.clearCacheByPattern(`${this.cachePrefixes.news}*`);
      }

      this.ctx.logger.info('News cache cleared', { newsId, code });
    } catch (error) {
      this.ctx.logger.error('Failed to clear news cache', error);
    }
  }

  // 清除报告相关缓存
  async clearReportCache(options = {}) {
    const { announcementId, code } = options;

    try {
      if (announcementId) {
        await this.clearCacheByPattern(`${this.cachePrefixes.reports}*:${announcementId}*`);
      } else if (code) {
        await this.clearCacheByPattern(`${this.cachePrefixes.reports}*:${code}*`);
      } else {
        await this.clearCacheByPattern(`${this.cachePrefixes.reports}*`);
      }

      this.ctx.logger.info('Report cache cleared', { announcementId, code });
    } catch (error) {
      this.ctx.logger.error('Failed to clear report cache', error);
    }
  }

  // 清除研究相关缓存
  async clearResearchCache(options = {}) {
    const { researchId, code } = options;

    try {
      if (researchId) {
        await this.clearCacheByPattern(`${this.cachePrefixes.research}*:${researchId}*`);
      } else if (code) {
        await this.clearCacheByPattern(`${this.cachePrefixes.research}*:${code}*`);
      } else {
        await this.clearCacheByPattern(`${this.cachePrefixes.research}*`);
      }

      this.ctx.logger.info('Research cache cleared', { researchId, code });
    } catch (error) {
      this.ctx.logger.error('Failed to clear research cache', error);
    }
  }

  // 清除行业相关缓存
  async clearIndustryCache() {
    try {
      await this.clearCacheByPattern(`${this.cachePrefixes.industry}*`);
      this.ctx.logger.info('Industry cache cleared');
    } catch (error) {
      this.ctx.logger.error('Failed to clear industry cache', error);
    }
  }

  // 清除统计相关缓存
  async clearStatisticsCache() {
    try {
      await this.clearCacheByPattern(`${this.cachePrefixes.statistics}*`);
      this.ctx.logger.info('Statistics cache cleared');
    } catch (error) {
      this.ctx.logger.error('Failed to clear statistics cache', error);
    }
  }

  // 清除数据源相关缓存
  async clearSourcesCache(options = {}) {
    const { method, code } = options;

    try {
      // 使用BaseCacheService的清除方法
      await this.ctx.service.sources.clearRelatedCache('sources', options);
      this.ctx.logger.info('Sources cache cleared', { method, code });
    } catch (error) {
      this.ctx.logger.error('Failed to clear sources cache', error);
    }
  }

  // 清除标签相关缓存
  async clearTagsCache(options = {}) {
    const { tagId, userId, code, type, method } = options;

    try {
      // 使用BaseCacheService的清除方法
      await this.ctx.service.tags.clearRelatedCache('tags', options);
      this.ctx.logger.info('Tags cache cleared', { tagId, userId, code, type, method });
    } catch (error) {
      this.ctx.logger.error('Failed to clear tags cache', error);
    }
  }

  // 清除分组相关缓存
  async clearGroupsCache(options = {}) {
    const { id, code, type, method } = options;

    try {
      // 使用BaseCacheService的清除方法
      await this.ctx.service.groups.clearRelatedCache('groups', options);
      this.ctx.logger.info('Groups cache cleared', { id, code, type, method });
    } catch (error) {
      this.ctx.logger.error('Failed to clear groups cache', error);
    }
  }

  // 清除量化数据相关缓存
  async clearQuantsCache(options = {}) {
    const { method, type, limit } = options;

    try {
      // 使用BaseCacheService的清除方法
      await this.ctx.service.quants.clearRelatedCache('quants', options);
      this.ctx.logger.info('Quants cache cleared', { method, type, limit });
    } catch (error) {
      this.ctx.logger.error('Failed to clear quants cache', error);
    }
  }

  // 清除消息相关缓存
  async clearMessagesCache(options = {}) {
    const { userId, type, messageId } = options;

    try {
      // 使用BaseCacheService的清除方法
      await this.ctx.service.messages.clearRelatedCache('messages', options);
      this.ctx.logger.info('Messages cache cleared', { userId, type, messageId });
    } catch (error) {
      this.ctx.logger.error('Failed to clear messages cache', error);
    }
  }

  // 根据模式清除缓存
  async clearCacheByPattern(pattern) {
    try {
      const keys = await this.app.redis.keys(pattern);
      if (keys.length > 0) {
        await this.app.redis.del(...keys);
        this.ctx.logger.info(`Cleared ${keys.length} cache keys with pattern: ${pattern}`);
      }
    } catch (error) {
      this.ctx.logger.error(`Failed to clear cache with pattern: ${pattern}`, error);
    }
  }

  // 清除所有缓存
  async clearAllCache() {
    try {
      const patterns = Object.values(this.cachePrefixes);
      for (const pattern of patterns) {
        await this.clearCacheByPattern(`${pattern}*`);
      }
      this.ctx.logger.info('All cache cleared');
    } catch (error) {
      this.ctx.logger.error('Failed to clear all cache', error);
    }
  }

  // 根据数据库操作类型清除相关缓存
  async clearCacheByOperation(operation, data) {
    const { table, action, record } = operation;

    try {
      switch (table) {
        case 'stock':
          await this.clearStockCache({ code: record?.code });
          break;
        case 'source':
          await this.clearStockCache({ code: record?.code });
          break;
        case 'news':
          await this.clearNewsCache({
            newsId: record?.newsID,
            code: record?.code,
          });
          break;
        case 'newsstock':
          await this.clearNewsCache({ code: record?.code });
          await this.clearStockCache({ code: record?.code });
          break;
        case 'report':
          await this.clearReportCache({
            announcementId: record?.announcementId,
            code: record?.secCode,
          });
          break;
        case 'research':
          await this.clearResearchCache({
            researchId: record?.id,
            code: record?.code,
          });
          break;
        case 'tag':
          await this.clearTagsCache({ tagId: record?.id });
          break;
        case 'tagstock':
          await this.clearTagsCache({ tagId: record?.tagId, code: record?.code });
          await this.clearStockCache({ code: record?.code });
          break;
        case 'taguser':
          await this.clearTagsCache({ tagId: record?.tagId, userId: record?.userId });
          break;
        case 'post':
          await this.clearStockCache(); // 帖子可能影响股票热度
          break;
        case 'poststock':
          await this.clearStockCache({ code: record?.code });
          break;
        case 'fundstock':
          await this.clearStockCache({ code: record?.code });
          break;
        case 'groupstock':
          await this.clearStockCache({ code: record?.code });
          break;
        case 'source':
          await this.clearSourcesCache({ code: record?.code });
          await this.clearStockCache({ code: record?.code });
          break;
        default:
          // 对于未知表，清除所有缓存以确保数据一致性
          await this.clearAllCache();
      }

      this.ctx.logger.info('Cache cleared by operation', { table, action, record });
    } catch (error) {
      this.ctx.logger.error('Failed to clear cache by operation', error);
    }
  }

  // 获取缓存统计信息
  async getCacheStats() {
    try {
      const stats = {};

      for (const [ name, prefix ] of Object.entries(this.cachePrefixes)) {
        const keys = await this.app.redis.keys(`${prefix}*`);
        stats[name] = keys.length;
      }

      return stats;
    } catch (error) {
      this.ctx.logger.error('Failed to get cache stats', error);
      return {};
    }
  }

  // 设置缓存过期时间
  async setCacheExpiration(pattern, ttl = 300) {
    try {
      const keys = await this.app.redis.keys(pattern);
      for (const key of keys) {
        await this.app.redis.expire(key, ttl);
      }
      this.ctx.logger.info(`Set expiration for ${keys.length} keys with pattern: ${pattern}`);
    } catch (error) {
      this.ctx.logger.error(`Failed to set cache expiration for pattern: ${pattern}`, error);
    }
  }
}

module.exports = CacheManagerService;
