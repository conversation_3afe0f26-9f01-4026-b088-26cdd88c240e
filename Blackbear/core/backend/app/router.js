'use strict';

const { RateLimit } = require('koa2-ratelimit');

/**
 * 路由配置模块
 * 统一管理所有API路由，包括认证、权限控制和限流配置
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
  const { router, controller, middleware, jwt, io } = app;

  // ==================== WebSocket 配置 ====================
  /**
   * WebSocket 命名空间配置
   */
  const nsp = io.of('/');
  nsp.route('server', io.controller.index.ping);
  nsp.route('ack', io.controller.index.ack);

  // ==================== 健康检查和监控路由 ====================
  // 健康检查端点（无需认证）
  router.get('/api/health', controller.health.index);
  router.get('/api/health/detailed', controller.health.detailed);
  router.get('/api/health/ready', controller.health.ready);
  router.get('/api/health/live', controller.health.live);
  router.get('/api/health/metrics', controller.health.metrics);

  // ==================== 路由命名空间定义 ====================
  const apiV1Router = app.router.namespace('/api/v1'); // 主要API版本
  const apiWebRouter = app.router.namespace('/api/web'); // Web端公开API
  const apiAgentRouter = app.router.namespace('/api/agent'); // Agent端API
  // const apiWechatRouter = app.router.namespace('/api/wx'); // 微信API (已废弃)

  // ==================== 控制器解构 ====================
  const {
    // 核心业务控制器
    home, operate, chart, sources, worths, tags, sorts, news, groups, calendars,
    research, reports, reporttime, events, posts, users, stocks, quants, upload,
    hotword, task, medias, positions, points, funds, kline, material,

    // 系统管理控制器
    menus, permissions, roles, messages, verifications, configurations, devices, dictionary,

    // 监控和工具控制器
    scheduleMonitor, puppeteer, middlewareMonitor, cache,
  } = controller;
  // ==================== 中间件配置 ====================

  // 限流中间件配置
  const postLimit = middleware.postLimit(10); // 文章操作限流
  const tagLimit = middleware.tagLimit(10); // 标签操作限流
  const stockLimit = middleware.stockLimit(15); // 股票操作限流
  const eventLimit = middleware.eventLimit(15); // 事件操作限流
  const researchLimit = middleware.researchLimit(15); // 研报操作限流

  // 业务权限中间件
  const postEdit = middleware.postEdit(); // 文章编辑权限
  const tagEdit = middleware.tagEdit(); // 标签编辑权限
  const postOpen = middleware.postOpen(); // 文章公开访问控制
  const postPoint = middleware.postPoint(); // 文章积分控制

  // 系统权限中间件
  const permission = middleware.permissionHandler(); // 权限检查
  const operateLog = middleware.operateLog(); // 操作日志记录

  // 外部限流中间件
  const verificationsLimiter = RateLimit.middleware({
    interval: 15 * 60 * 1000, // 15分钟窗口
    max: 30, // 最大30次请求
    message: '请求次数过多，请稍后重试！',
  });

  const smsLimiter = RateLimit.middleware({
    interval: { min: 3 }, // 3分钟间隔
    max: 5, // 最大5次请求
  });

  // ==================== 认证和用户管理路由 ====================

  // 第三方登录配置
  const weiboAuth = app.passport.authenticate('weibo', {
    successRedirect: '/login/success',
    failureRedirect: '/login/failure',
  });

  // 微博登录路由
  router.get('/passport/weibo', weiboAuth);
  router.get('/auth/weibo/callback', weiboAuth);

  // 用户认证相关路由（应用智能限流和验证）
  router.get('/login/success', users.loginsuccess); // 登录成功回调
  router.get('/login/failure', users.loginfailure); // 登录失败回调

  // 登录接口 - 严格限流
  router.post('/login', RateLimit.middleware({
    interval: 15 * 60 * 1000,
    max: 5,
    message: '登录尝试过于频繁，请15分钟后再试'
  }), users.login);

  router.post('/loginwithphone', RateLimit.middleware({
    interval: 15 * 60 * 1000,
    max: 5,
    message: '手机登录尝试过于频繁，请15分钟后再试'
  }), users.loginWithPhone);

  router.post('/loginwithdevice', users.loginWithDevice);
  router.post('/logout', users.logout);

  // 用户注册和管理 - 应用验证
  router.post('/userexist', users.userexist);

  router.post('/register', RateLimit.middleware({
    interval: 60 * 60 * 1000,
    max: 3,
    message: '注册过于频繁，请1小时后再试'
  }), users.create);

  router.post('/resetpwd', RateLimit.middleware({
    interval: 60 * 60 * 1000,
    max: 3,
    message: '密码重置过于频繁，请1小时后再试'
  }), users.resetpwd);

  // 验证码相关路由
  router.resources('verifications', '/verifications', verificationsLimiter, verifications);
  router.get('/svgcaptcha', verificationsLimiter, verifications.svgCaptcha); // 图形验证码
  router.get('/smscode', smsLimiter, verifications.smsCode); // 短信验证码

  // 系统配置和健康检查
  router.get('/publickey', configurations.findRsaPublicKey); // 获取RSA公钥
  router.get('/healthy', home.healthy); // 健康检查

  // ==================== API v1 路由配置 (需要JWT认证) ====================

  // -------------------- 工具和监控模块 --------------------

  // Puppeteer 爬虫工具
  apiV1Router.get('/puppeteer/screenshot', jwt, permission, puppeteer.screenshot);
  apiV1Router.post('/puppeteer/scrape', jwt, permission, puppeteer.scrape);
  apiV1Router.post('/puppeteer/pdf', jwt, permission, puppeteer.generatePdf);
  apiV1Router.post('/puppeteer/performance', jwt, permission, puppeteer.analyzePerformance);

  // -------------------- 首页和核心功能 --------------------

  // 首页相关
  apiV1Router.get('/home', jwt, permission, home.index); // 首页数据
  apiV1Router.get('/aktools', jwt, home.aktools); // AK工具
  apiV1Router.get('/trend', jwt, home.trend); // 趋势数据
  apiV1Router.get('/changes', jwt, home.changes); // 变化数据
  apiV1Router.get('/search', jwt, permission, home.globalsearch); // 全局搜索
  apiV1Router.get('/unread', jwt, permission, home.unread); // 未读消息
  apiV1Router.get('/ramble', jwt, permission, home.ramble); // 随机浏览
  apiV1Router.get('/aggregation', jwt, permission, home.aggregation); // 聚合数据
  apiV1Router.post('/aggregation/:id/read', jwt, permission, home.aggregationRead); // 标记聚合已读

  // 热词管理
  apiV1Router.get('/hotword', jwt, permission, hotword.getUserDict); // 获取用户词典
  apiV1Router.post('/hotword', jwt, permission, hotword.addUserDict); // 添加用户词典
  apiV1Router.post('/hotword/delete', jwt, permission, hotword.deleteUserDict); // 删除用户词典
  apiV1Router.post('/hotwordstop', jwt, permission, hotword.addStopWord); // 添加停用词

  // 量化相关
  apiV1Router.get('/quant', jwt, permission, quants.index); // 量化首页
  apiV1Router.post('/quant', jwt, permission, quants.update); // 更新量化数据
  apiV1Router.get('/quant/list', jwt, permission, quants.list); // 量化列表

  // 任务管理
  apiV1Router.post('/task', jwt, permission, task.run); // 运行任务
  apiV1Router.post('/task/curl', jwt, permission, task.runByCurl); // 通过CURL运行任务
  apiV1Router.get('/task', jwt, permission, task.all); // 获取所有任务
  apiV1Router.get('/task/:id', jwt, permission, task.show); // 获取任务详情

  // 操作相关
  apiV1Router.post('/operate/share', jwt, permission, operate.share); // 分享操作
  apiV1Router.post('/operate/sharenews', jwt, permission, operate.shareNews); // 分享新闻
  apiV1Router.get('/operate/getlog', jwt, permission, operate.getlog); // 获取操作日志
  // -------------------- 资源管理模块 --------------------

  // 标签管理 (Tags)
  apiV1Router.resources('tags', '/tags', jwt, tagEdit, operateLog, tags);
  apiV1Router.get('/tags/:id', jwt, permission, tags.show); // 获取标签详情
  apiV1Router.get('/mytags', jwt, permission, tags.mytags); // 获取我的标签
  apiV1Router.post('/tags/:id/lock', jwt, permission, tagEdit, tags.lock); // 锁定标签
  apiV1Router.post('/tags/:id/unlock', jwt, permission, tagEdit, tags.unlock); // 解锁标签
  apiV1Router.post('/tags/:id/json', jwt, permission, tagEdit, tags.updateWithJson); // JSON更新标签
  apiV1Router.put('/tags/:id/addUser', jwt, permission, tagEdit, operateLog, tags.addUser); // 添加用户到标签
  apiV1Router.put('/tags/:id/removeUser', jwt, permission, tagEdit, operateLog, tags.removeUser); // 从标签移除用户
  apiV1Router.put('/tags/:id/addSubtag', jwt, permission, tagEdit, operateLog, tags.addSubtag); // 添加子标签
  apiV1Router.put('/tags/:id/removeSubtag', jwt, permission, tagEdit, operateLog, tags.removeSubtag); // 移除子标签
  apiV1Router.put('/tags/:id/createStock', jwt, permission, tagEdit, tagLimit, operateLog, tags.createStock); // 关联股票
  apiV1Router.put('/tags/:id/deleteStock', jwt, permission, tagEdit, tagLimit, operateLog, tags.deleteStock); // 取消关联股票
  apiV1Router.put('/tags/:id/reason', jwt, permission, tagEdit, tagLimit, operateLog, tags.tagReason); // 设置标签原因

  // 股票管理 (Stocks) - 特殊路由必须在resources之前定义
  apiV1Router.get('/stocks/:id/history', jwt, permission, stocks.showHistory); // 股票历史数据
  apiV1Router.get('/stockscore', jwt, permission, stocks.scoreList); // 股票评分列表
  apiV1Router.resources('stocks', '/stocks', jwt, permission, stockLimit, operateLog, stocks);

  // 新闻管理 (News) - 特殊路由必须在resources之前定义
  apiV1Router.put('/news/:id/read', jwt, permission, news.read); // 标记新闻已读
  apiV1Router.post('/news/allread', jwt, permission, news.allread); // 标记所有新闻已读
  apiV1Router.get('/news/important', jwt, permission, news.importantNews); // 重要新闻
  apiV1Router.resources('news', '/news', jwt, permission, news);

  // 其他资源管理
  apiV1Router.resources('worths', '/worths', jwt, permission, worths); // 净值管理
  apiV1Router.resources('sorts', '/sorts', jwt, permission, sorts); // 分类管理
  apiV1Router.resources('medias', '/medias', jwt, permission, medias); // 媒体管理
  apiV1Router.resources('groups', '/groups', jwt, permission, groups); // 分组管理
  apiV1Router.put('/groups/:id/stock', jwt, permission, groups.arStock); // 分组关联股票
  apiV1Router.resources('calendars', '/calendars', jwt, permission, calendars); // 日历管理
  apiV1Router.resources('devices', '/devices', jwt, permission, devices); // 设备管理
  // -------------------- 内容管理模块 --------------------

  // 研报管理 (Research)
  apiV1Router.resources('researchs', '/researchs', jwt, permission, research);
  apiV1Router.put('/researchs/:id/read', jwt, permission, research.read); // 标记研报已读
  apiV1Router.post('/researchs/allread', jwt, permission, research.allread); // 标记所有研报已读
  apiV1Router.put('/researchs/:id/createTag', jwt, permission, researchLimit, operateLog, research.createTag); // 创建研报标签
  apiV1Router.put('/researchs/:id/deleteTag', jwt, permission, researchLimit, operateLog, research.deleteTag); // 删除研报标签

  // 报告管理 (Reports)
  apiV1Router.resources('reports', '/reports', jwt, permission, reports); // 报告管理
  apiV1Router.resources('reporttime', '/reporttime', jwt, reporttime); // 报告时间管理

  // 文章管理 (Posts)
  apiV1Router.resources('posts', '/posts', jwt, permission, postEdit, postLimit, operateLog, posts);

  // 事件管理 (Events)
  apiV1Router.resources('events', '/events', jwt, permission, eventLimit, operateLog, events);
  apiV1Router.put('/events/:id/createStock', jwt, permission, eventLimit, operateLog, events.createStock); // 事件关联股票
  apiV1Router.put('/events/:id/deleteStock', jwt, permission, eventLimit, operateLog, events.deleteStock); // 事件取消关联股票
  apiV1Router.put('/events/:id/createTag', jwt, permission, eventLimit, operateLog, events.createTag); // 事件创建标签
  apiV1Router.put('/events/:id/deleteTag', jwt, permission, eventLimit, operateLog, events.deleteTag); // 事件删除标签
  apiV1Router.put('/events/:id/unbindCal', jwt, permission, eventLimit, operateLog, events.unbindCal); // 事件解绑日历

  // -------------------- 用户和权限管理 --------------------

  // 用户管理
  apiV1Router.resources('users', '/users', jwt, permission, users); // 用户管理
  apiV1Router.resources('positions', '/positions', jwt, permission, positions); // 职位管理
  apiV1Router.resources('points', '/points', jwt, permission, points); // 积分管理

  // 基础数据管理
  apiV1Router.resources('funds', '/funds', jwt, permission, funds); // 基金管理
  apiV1Router.resources('material', '/material', jwt, permission, material); // 素材管理
  // -------------------- 系统管理模块 --------------------

  // 权限和角色管理
  apiV1Router.resources('menus', '/menus', jwt, permission, menus); // 菜单管理
  apiV1Router.resources('permissions', '/permissions', jwt, permission, permissions); // 权限管理
  apiV1Router.resources('roles', '/roles', jwt, permission, roles); // 角色管理
  apiV1Router.put('/roles/:id/default', jwt, permission, roles.updateIsDefault); // 设置默认角色
  apiV1Router.put('/roles/:id/bulkCreateMenu', jwt, permission, roles.bulkCreateMenu); // 批量创建菜单
  apiV1Router.put('/roles/:id/bulkCreatePremission', jwt, permission, roles.bulkCreatePremission); // 批量创建权限
  apiV1Router.put('/roles/:id/bulkDeleteMenu', jwt, permission, roles.bulkDeleteMenu); // 批量删除菜单
  apiV1Router.put('/roles/:id/bulkDeletePremission', jwt, permission, roles.bulkDeletePremission); // 批量删除权限

  // 消息管理
  apiV1Router.resources('messages', '/messages', jwt, permission, messages); // 消息管理
  apiV1Router.post('/messages/send', jwt, permission, messages.sendMessage); // 发送消息

  // 系统配置
  apiV1Router.post('/configurations', jwt, permission, configurations.update); // 更新配置
  apiV1Router.get('/getConfig', jwt, permission, configurations.getConfig); // 获取配置
  apiV1Router.get('/getSiteConfig', configurations.getSiteConfig); // 获取站点配置
  apiV1Router.post('/updateConfig', jwt, permission, configurations.updateConfig); // 更新配置

  // -------------------- 数据和图表模块 --------------------

  // 文件上传
  apiV1Router.post('/upload', jwt, upload.upload); // 文件上传

  // 基础数据
  apiV1Router.get('/dictionary', jwt, dictionary.index); // 字典数据
  apiV1Router.get('/chart', jwt, chart.index); // 图表数据
  apiV1Router.get('/sources', jwt, sources.index); // 数据源

  // 主题和分类
  apiV1Router.get('/topic', jwt, permission, tags.getAllTopic); // 获取所有主题
  apiV1Router.get('/topic/:id', jwt, permission, tags.getTopicById); // 获取主题详情
  apiV1Router.post('/topic/change', jwt, permission, tags.topicChange); // 更改主题
  apiV1Router.post('/sorts/change', jwt, permission, sorts.sortsChange); // 更改分类

  // 股票相关数据
  apiV1Router.get('/overview', jwt, permission, stocks.overview); // 股票概览
  apiV1Router.get('/starstock', jwt, permission, stocks.getStarStock); // 明星股票
  apiV1Router.get('/kline/:id', jwt, kline.show); // K线详情
  apiV1Router.get('/kline', jwt, kline.index); // K线列表
  apiV1Router.get('/dailystory', jwt, posts.dailystory); // 每日故事

  // -------------------- 监控和管理模块 --------------------

  // 定时任务监控
  apiV1Router.get('/schedule/tasks', jwt, permission, scheduleMonitor.getTasks); // 获取任务列表
  apiV1Router.get('/schedule/health', jwt, permission, scheduleMonitor.getHealth); // 获取健康状态
  apiV1Router.get('/schedule/tasks/:name', jwt, permission, scheduleMonitor.getTaskDetail); // 获取任务详情
  apiV1Router.get('/schedule/report', jwt, permission, scheduleMonitor.generateReport); // 生成报告
  apiV1Router.get('/schedule/recommendations', jwt, permission, scheduleMonitor.getRecommendations); // 获取建议
  apiV1Router.put('/schedule/tasks/:name/config', jwt, permission, scheduleMonitor.updateTaskConfig); // 更新任务配置
  apiV1Router.get('/schedule/export', jwt, permission, scheduleMonitor.exportConfig); // 导出配置
  apiV1Router.get('/schedule/statistics', jwt, permission, scheduleMonitor.getStatistics); // 获取统计信息

  // 中间件监控
  apiV1Router.get('/middleware/status', jwt, permission, middlewareMonitor.getMiddlewareStatus); // 中间件状态
  apiV1Router.get('/middleware/health', jwt, permission, middlewareMonitor.getMiddlewareHealth); // 中间件健康检查
  apiV1Router.get('/middleware/:name', jwt, permission, middlewareMonitor.getMiddlewareDetail); // 中间件详情
  apiV1Router.get('/middleware/report', jwt, permission, middlewareMonitor.generateMiddlewareReport); // 生成中间件报告
  apiV1Router.get('/middleware/performance', jwt, permission, middlewareMonitor.getPerformanceMetrics); // 性能指标
  apiV1Router.get('/middleware/validate', jwt, permission, middlewareMonitor.validateMiddlewareConfig); // 验证配置
  apiV1Router.get('/middleware/export', jwt, permission, middlewareMonitor.exportMiddlewareConfig); // 导出中间件配置
  apiV1Router.get('/middleware/rate-limit/stats', jwt, permission, middlewareMonitor.getRateLimitStats); // 限流统计
  apiV1Router.post('/middleware/rate-limit/cleanup', jwt, permission, middlewareMonitor.cleanupRateLimitRecords); // 清理限流记录
  apiV1Router.post('/middleware/rate-limit/reset', jwt, permission, middlewareMonitor.resetUserRateLimit); // 重置用户限流

  // -------------------- 缓存管理模块 --------------------

  // 缓存清除
  apiV1Router.post('/cache/clear/all', jwt, permission, cache.clearAll); // 清除所有缓存
  apiV1Router.post('/cache/clear/stock', jwt, permission, cache.clearStock); // 清除股票缓存
  apiV1Router.post('/cache/clear/tags', jwt, permission, cache.clearTags); // 清除标签缓存
  apiV1Router.post('/cache/clear/groups', jwt, permission, cache.clearGroups); // 清除分组缓存
  apiV1Router.post('/cache/clear/quants', jwt, permission, cache.clearQuants); // 清除量化数据缓存
  apiV1Router.post('/cache/clear/messages', jwt, permission, cache.clearMessages); // 清除消息缓存
  apiV1Router.post('/cache/clear/news', jwt, permission, cache.clearNews); // 清除新闻缓存
  apiV1Router.post('/cache/clear/report', jwt, permission, cache.clearReport); // 清除报告缓存
  apiV1Router.post('/cache/clear/research', jwt, permission, cache.clearResearch); // 清除研究缓存
  apiV1Router.post('/cache/clear/industry', jwt, permission, cache.clearIndustry); // 清除行业缓存
  apiV1Router.post('/cache/clear/statistics', jwt, permission, cache.clearStatistics); // 清除统计缓存
  apiV1Router.post('/cache/clear/sources', jwt, permission, cache.clearSources); // 清除数据源缓存
  apiV1Router.post('/cache/clear/operation', jwt, permission, cache.clearByOperation); // 根据操作清除缓存

  // 缓存管理
  apiV1Router.get('/cache/stats', jwt, permission, cache.getStats); // 获取缓存统计
  apiV1Router.post('/cache/check-changes', jwt, permission, cache.checkChanges); // 手动检查数据库变化
  apiV1Router.post('/cache/set-expiration', jwt, permission, cache.setExpiration); // 设置缓存过期时间

  // -------------------- 第三方集成 --------------------

  // Chrome插件集成
  apiV1Router.post('/cookie', operate.cookie); // 获取社交媒体Cookie

  // ==================== Web API 路由配置 (公开访问) ====================

  // -------------------- 首页和基础数据 --------------------

  // 移动端和首页
  apiWebRouter.get('/mobile', home.mobile); // H5首页
  apiWebRouter.get('/sidebar', home.sidebar); // 侧边栏数据
  apiWebRouter.get('/hotword', home.hotword); // 热词数据
  apiWebRouter.get('/trend', home.trend); // 个股趋势
  apiWebRouter.get('/announcement', home.announcement); // 个股公告

  // 图表和数据
  apiWebRouter.get('/chart', chart.index); // 图表数据
  apiWebRouter.get('/mchart', chart.mobile); // 移动端图表
  apiWebRouter.get('/sources', sources.index); // 数据源
  apiWebRouter.get('/category', dictionary.index); // 分类数据

  // -------------------- 内容展示 --------------------

  // 主题和标签
  apiWebRouter.get('/topic', tags.getAllTopic); // 所有主题
  apiWebRouter.get('/topic/:id', tags.getTopicById); // 主题详情
  apiWebRouter.get('/tags', tags.index); // 标签列表
  apiWebRouter.get('/tags/:id', tags.show); // 标签详情

  // 股票信息
  apiWebRouter.get('/stocks', stocks.index); // 股票列表
  apiWebRouter.get('/stocks/:id', stocks.show); // 股票详情

  // 事件和日历
  apiWebRouter.get('/events', events.index); // 事件列表
  apiWebRouter.get('/events/:id', events.show); // 事件详情
  apiWebRouter.get('/calendars', calendars.index); // 日历数据

  // 文章和媒体
  apiWebRouter.get('/posts', posts.index); // 文章列表
  apiWebRouter.get('/posts/:id', postOpen, postPoint, posts.show); // 文章详情 (需要访问控制)
  apiWebRouter.post('/posts/:id/views', posts.views); // 文章浏览量
  apiWebRouter.get('/medias', medias.index); // 媒体列表
  apiWebRouter.get('/medias/:id', medias.show); // 媒体详情

  // 消息和配置
  apiWebRouter.get('/message', messages.index); // 消息列表
  apiWebRouter.get('/getCateConfig', configurations.getCateConfig); // 分类配置

  // -------------------- SEO和工具 --------------------

  // Sitemap专用接口 (仅返回ID)
  apiWebRouter.get('/getallposts', postOpen, posts.getall); // 所有文章ID
  apiWebRouter.get('/getalltags', tags.getall); // 所有标签ID
  apiWebRouter.get('/getallmedias', medias.getall); // 所有媒体ID

  // 设备管理
  apiWebRouter.post('/devices/active', devices.create); // 激活设备
  apiWebRouter.post('/devices/check', devices.check); // 检查设备


  // ==================== Agent API 路由配置 (需要JWT认证) ====================

  // -------------------- 媒体内容管理 --------------------

  // 小作文管理
  apiAgentRouter.get('/medias', jwt, medias.index); // 获取媒体列表
  apiAgentRouter.get('/medias/:id', jwt, medias.show); // 获取媒体详情
  apiAgentRouter.post('/medias', jwt, operateLog, medias.create); // 创建媒体内容
  apiAgentRouter.put('/medias/:id', jwt, operateLog, medias.update); // 更新媒体内容
  apiAgentRouter.post('/medias/stock', jwt, operateLog, medias.associatedStock); // 关联个股

  // -------------------- 数据更新 --------------------

  // 股票数据编辑
  apiAgentRouter.post('/stocks', jwt, operateLog, stocks.update); // 更新股票数据

  // 标签和主题管理
  apiAgentRouter.get('/tags/:id', jwt, tags.show); // 获取标签详情
  apiAgentRouter.post('/tags/:id/json', jwt, operateLog, tags.updateWithJson); // JSON更新标签

  // -------------------- 内容发布 --------------------

  // 文章创建和分享
  apiAgentRouter.post('/posts', jwt, operateLog, posts.create); // 创建文章
  apiAgentRouter.post('/operate/share', jwt, operateLog, operate.share); // 分享到社交平台

  // 消息发送
  apiAgentRouter.post('/messages/send', jwt, operateLog, messages.sendMessage); // 发送消息
};

