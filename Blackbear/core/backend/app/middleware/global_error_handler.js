'use strict';

/**
 * 全局错误处理中间件
 * 统一处理所有未捕获的错误，提供一致的错误响应格式
 */

module.exports = (options = {}) => {
  const defaultOptions = {
    // 是否在响应中包含错误堆栈
    includeStack: process.env.NODE_ENV !== 'production',
    
    // 是否记录错误日志
    logErrors: true,
    
    // 错误分类配置
    errorTypes: {
      ValidationError: { status: 400, code: 'VALIDATION_ERROR' },
      UnauthorizedError: { status: 401, code: 'UNAUTHORIZED' },
      ForbiddenError: { status: 403, code: 'FORBIDDEN' },
      NotFoundError: { status: 404, code: 'NOT_FOUND' },
      ConflictError: { status: 409, code: 'CONFLICT' },
      RateLimitError: { status: 429, code: 'RATE_LIMIT_EXCEEDED' },
      InternalServerError: { status: 500, code: 'INTERNAL_SERVER_ERROR' },
    },
    
    // 敏感信息过滤
    sensitiveFields: ['password', 'token', 'secret', 'key', 'auth'],
    
    ...options,
  };

  return async function globalErrorHandler(ctx, next) {
    try {
      await next();
    } catch (error) {
      // 记录错误日志
      if (defaultOptions.logErrors) {
        logError(ctx, error);
      }

      // 生成错误响应
      const errorResponse = generateErrorResponse(error, defaultOptions);
      
      // 设置响应状态和内容
      ctx.status = errorResponse.status;
      ctx.body = errorResponse.body;
      
      // 设置错误相关的响应头
      ctx.set('X-Error-Code', errorResponse.body.code);
      ctx.set('X-Request-ID', ctx.traceId || generateRequestId());
      
      // 触发错误事件（用于监控和告警）
      ctx.app.emit('error', error, ctx);
    }
  };
};

/**
 * 记录错误日志
 */
function logError(ctx, error) {
  const logData = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
    },
    request: {
      method: ctx.method,
      url: ctx.url,
      headers: filterSensitiveData(ctx.headers, ['authorization', 'cookie']),
      body: filterSensitiveData(ctx.request.body),
      query: ctx.query,
      ip: ctx.ip,
      userAgent: ctx.get('User-Agent'),
    },
    user: ctx.user ? {
      id: ctx.user.id,
      username: ctx.user.username,
      role: ctx.user.role,
    } : null,
    traceId: ctx.traceId,
    timestamp: new Date().toISOString(),
  };

  // 根据错误严重程度选择日志级别
  if (isClientError(error)) {
    ctx.logger.warn('Client error occurred', logData);
  } else {
    ctx.logger.error('Server error occurred', logData);
  }
}

/**
 * 生成错误响应
 */
function generateErrorResponse(error, options) {
  // 确定错误类型和状态码
  const errorInfo = determineErrorInfo(error, options.errorTypes);
  
  // 生成基础响应体
  const body = {
    success: false,
    message: getErrorMessage(error),
    code: errorInfo.code,
    timestamp: new Date().toISOString(),
  };

  // 添加错误详情（开发环境）
  if (options.includeStack) {
    body.details = {
      name: error.name,
      stack: error.stack,
    };
  }

  // 添加验证错误详情
  if (error.name === 'ValidationError' && error.errors) {
    body.validation = formatValidationErrors(error.errors);
  }

  // 添加请求ID（用于错误追踪）
  if (error.requestId) {
    body.requestId = error.requestId;
  }

  return {
    status: errorInfo.status,
    body,
  };
}

/**
 * 确定错误信息
 */
function determineErrorInfo(error, errorTypes) {
  // 检查自定义错误类型
  if (errorTypes[error.name]) {
    return errorTypes[error.name];
  }

  // 检查HTTP状态码
  if (error.status || error.statusCode) {
    const status = error.status || error.statusCode;
    return {
      status,
      code: getCodeByStatus(status),
    };
  }

  // 检查特定错误类型
  if (error.name === 'ValidationError') {
    return { status: 400, code: 'VALIDATION_ERROR' };
  }

  if (error.name === 'UnauthorizedError' || error.message.includes('unauthorized')) {
    return { status: 401, code: 'UNAUTHORIZED' };
  }

  if (error.name === 'ForbiddenError' || error.message.includes('forbidden')) {
    return { status: 403, code: 'FORBIDDEN' };
  }

  if (error.name === 'NotFoundError' || error.message.includes('not found')) {
    return { status: 404, code: 'NOT_FOUND' };
  }

  // 数据库错误
  if (error.name === 'SequelizeValidationError') {
    return { status: 400, code: 'VALIDATION_ERROR' };
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    return { status: 409, code: 'CONFLICT' };
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return { status: 400, code: 'FOREIGN_KEY_CONSTRAINT' };
  }

  // 默认为服务器错误
  return { status: 500, code: 'INTERNAL_SERVER_ERROR' };
}

/**
 * 获取错误消息
 */
function getErrorMessage(error) {
  // 生产环境隐藏敏感错误信息
  if (process.env.NODE_ENV === 'production') {
    if (error.status >= 500) {
      return 'Internal server error';
    }
  }

  return error.message || 'An error occurred';
}

/**
 * 根据状态码获取错误代码
 */
function getCodeByStatus(status) {
  const statusCodes = {
    400: 'BAD_REQUEST',
    401: 'UNAUTHORIZED',
    403: 'FORBIDDEN',
    404: 'NOT_FOUND',
    405: 'METHOD_NOT_ALLOWED',
    409: 'CONFLICT',
    422: 'UNPROCESSABLE_ENTITY',
    429: 'RATE_LIMIT_EXCEEDED',
    500: 'INTERNAL_SERVER_ERROR',
    502: 'BAD_GATEWAY',
    503: 'SERVICE_UNAVAILABLE',
    504: 'GATEWAY_TIMEOUT',
  };

  return statusCodes[status] || 'UNKNOWN_ERROR';
}

/**
 * 格式化验证错误
 */
function formatValidationErrors(errors) {
  if (Array.isArray(errors)) {
    return errors.map(err => ({
      field: err.path || err.field,
      message: err.message,
      value: err.value,
    }));
  }

  if (typeof errors === 'object') {
    return Object.keys(errors).map(field => ({
      field,
      message: errors[field].message || errors[field],
      value: errors[field].value,
    }));
  }

  return errors;
}

/**
 * 过滤敏感数据
 */
function filterSensitiveData(data, additionalFields = []) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveFields = [
    'password', 'token', 'secret', 'key', 'auth',
    'authorization', 'cookie', 'session',
    ...additionalFields,
  ];

  const filtered = { ...data };

  for (const field of sensitiveFields) {
    if (filtered[field]) {
      filtered[field] = '[FILTERED]';
    }
  }

  return filtered;
}

/**
 * 判断是否为客户端错误
 */
function isClientError(error) {
  const status = error.status || error.statusCode || 500;
  return status >= 400 && status < 500;
}

/**
 * 生成请求ID
 */
function generateRequestId() {
  return Math.random().toString(36).substr(2, 9);
}
