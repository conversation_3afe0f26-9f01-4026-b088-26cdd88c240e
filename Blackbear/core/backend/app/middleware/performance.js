'use strict';

/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function performance(ctx, next) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    // 记录请求开始
    ctx.logger.info(`Request started: ${ctx.method} ${ctx.url}`, {
      ip: ctx.ip,
      userAgent: ctx.get('User-Agent'),
      timestamp: new Date().toISOString(),
    });

    try {
      // 执行下一个中间件
      await next();

      // 计算执行时间
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      const endMemory = process.memoryUsage();

      // 计算内存使用变化
      const memoryDiff = {
        rss: endMemory.rss - startMemory.rss,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external,
      };

      // 记录请求完成
      ctx.logger.info(`Request completed: ${ctx.method} ${ctx.url}`, {
        status: ctx.status,
        executionTime: `${executionTime}ms`,
        memoryDiff: {
          rss: `${Math.round(memoryDiff.rss / 1024 / 1024 * 100) / 100}MB`,
          heapUsed: `${Math.round(memoryDiff.heapUsed / 1024 / 1024 * 100) / 100}MB`,
          heapTotal: `${Math.round(memoryDiff.heapTotal / 1024 / 1024 * 100) / 100}MB`,
          external: `${Math.round(memoryDiff.external / 1024 / 1024 * 100) / 100}MB`,
        },
        timestamp: new Date().toISOString(),
      });

      // 慢查询警告
      if (executionTime > (options.slowQueryThreshold || 1000)) {
        ctx.logger.warn(`Slow request detected: ${ctx.method} ${ctx.url}`, {
          executionTime: `${executionTime}ms`,
          threshold: `${options.slowQueryThreshold || 1000}ms`,
        });
      }

      // 内存使用警告
      const memoryThreshold = options.memoryThreshold || 100 * 1024 * 1024; // 100MB
      if (Math.abs(memoryDiff.heapUsed) > memoryThreshold) {
        ctx.logger.warn(`High memory usage detected: ${ctx.method} ${ctx.url}`, {
          memoryDiff: `${Math.round(Math.abs(memoryDiff.heapUsed) / 1024 / 1024 * 100) / 100}MB`,
          threshold: `${Math.round(memoryThreshold / 1024 / 1024 * 100) / 100}MB`,
        });
      }

    } catch (error) {
      // 计算执行时间
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // 记录错误
      ctx.logger.error(`Request failed: ${ctx.method} ${ctx.url}`, {
        error: error.message,
        stack: error.stack,
        executionTime: `${executionTime}ms`,
        status: ctx.status,
        timestamp: new Date().toISOString(),
      });

      throw error;
    }
  };
};
