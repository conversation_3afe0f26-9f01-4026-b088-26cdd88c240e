'use strict';

const moment = require('moment');
const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 事件操作限流中间件
 * 限制用户每日事件操作次数
 * @param {number} limit - 每日操作限制次数
 */
module.exports = limit => {
  return async function eventLimit(ctx, next) {
    const { service, method, helper } = ctx;
    const { user } = ctx.state;

    // GET请求或管理员跳过限制
    if (method === 'GET' || helper.isAdmin()) {
      await next();
      return;
    }

    // 生成今日限制键
    const today = moment().format('YYYYMMDD');
    const userId = user?.id || 'anonymous';
    const cacheKey = `events_count_${userId}_${today}`;

    try {
      // 检查今日操作次数
      let todayCount = (await service.cache.get(cacheKey)) || 0;

      if (todayCount >= limit) {
        // 设置限流响应头
        ctx.set('X-RateLimit-Limit', limit);
        ctx.set('X-RateLimit-Remaining', 0);
        ctx.set('X-RateLimit-Reset', moment().endOf('day').unix());

        unifiedResponse.setResponse(ctx, null, `今天的事件操作数量已达到限制（${limit}）`, 429);
        return;
      }

      await next();

      // 操作成功后增加计数
      if (ctx.status === 200) {
        todayCount += 1;
        await service.cache.incr(cacheKey, 60 * 60 * 24); // 24小时过期

        // 设置限流响应头
        ctx.set('X-RateLimit-Limit', limit);
        ctx.set('X-RateLimit-Remaining', Math.max(0, limit - todayCount));
        ctx.set('X-RateLimit-Reset', moment().endOf('day').unix());
      }
    } catch (error) {
      console.error('Event limit middleware error:', error);
      // 缓存错误时不阻止操作，但记录日志
      await next();
    }
  };
};
