'use strict';

const { unifiedResponse, CustomErrors } = require('../utils/unified-response-system');
const SecurityValidator = require('../utils/security-validator');

/**
 * 全局错误处理中间件
 * 统一处理应用程序中的所有错误，提供一致的错误响应格式
 *
 * 功能：
 * 1. 捕获所有未处理的错误
 * 2. 根据错误类型返回相应的HTTP状态码
 * 3. 统一错误响应格式
 * 4. 生产环境敏感信息保护
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function errorHandler(ctx, next) {
    try {
      await next();
    } catch (error) {
      // 安全日志记录 - 脱敏处理
      const secureLogData = {
        error: {
          name: error.name,
          message: error.message,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        },
        request: SecurityValidator.maskSensitiveData({
          method: ctx.method,
          url: ctx.url,
          headers: ctx.headers,
          ip: ctx.ip,
          userAgent: ctx.get('user-agent'),
          userId: ctx.userInfo?.id,
        }, [ 'authorization', 'cookie', 'x-api-key' ]),
        timestamp: new Date().toISOString(),
      };

      // 记录错误日志到应用程序
      ctx.app.emit('error', error, ctx);

      // 安全事件记录
      if (isSecurityRelatedError(error)) {
        ctx.logger.warn('Security-related error detected:', secureLogData);
      }

      // 错误类型映射表
      const errorTypeMap = {
        [CustomErrors.ValidationError]: { status: 422, includeErrors: true },
        [CustomErrors.UnauthorizedError]: { status: 401 },
        [CustomErrors.ForbiddenError]: { status: 403 },
        [CustomErrors.NotFoundError]: { status: 404 },
        [CustomErrors.BadRequestError]: { status: 400 },
        [CustomErrors.TimeoutError]: { status: 408 },
        [CustomErrors.ConflictError]: { status: 409 },
        [CustomErrors.RateLimitError]: { status: 429 },
        [CustomErrors.ServiceUnavailableError]: { status: 503 },
        [CustomErrors.BusinessError]: { status: error.status || 400, code: error.code },
      };

      // JWT相关错误特殊处理
      if (error.name === 'TokenExpiredError' || error.message === 'jwt expired') {
        ctx.body = unifiedResponse.error('登录已过期，请重新登录', 401);
        ctx.status = 401;
        return;
      }

      if (error.name === 'JsonWebTokenError') {
        ctx.body = unifiedResponse.error('登录信息无效', 401);
        ctx.status = 401;
        return;
      }

      // 查找匹配的错误类型
      let errorConfig = null;
      for (const [ ErrorClass, config ] of Object.entries(errorTypeMap)) {
        if (ErrorClass && typeof ErrorClass === 'function' && error instanceof ErrorClass) {
          errorConfig = config;
          break;
        }
      }

      if (errorConfig) {
        // 已知错误类型处理
        const { status, code, includeErrors } = errorConfig;
        const responseData = includeErrors && error.errors ? { errors: error.errors } : null;

        ctx.body = unifiedResponse.error(error.message, code || status, responseData);
        ctx.status = status;
      } else {
        // 未知错误类型处理
        const status = error.status || 500;

        // 生产环境保护敏感信息
        const errorMessage = status === 500 && ctx.app.config.env === 'prod'
          ? 'Internal Server Error'
          : error.message || '服务器内部错误';

        ctx.body = unifiedResponse.error(errorMessage, status);
        ctx.status = status;

        // 验证错误包含详细信息
        if (status === 422 && error.errors) {
          ctx.body.data = { errors: error.errors };
        }
      }

      // 记录详细错误信息（仅在开发环境）
      if (ctx.app.config.env !== 'prod') {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack,
          url: ctx.request.url,
          method: ctx.request.method,
          userAgent: ctx.request.headers['user-agent'],
        });
      }
    }
  };
};

/**
 * 检查是否为安全相关错误
 * @param {Error} error - 错误对象
 * @return {boolean} 是否为安全相关错误
 */
function isSecurityRelatedError(error) {
  const securityErrorPatterns = [
    /sql injection/i,
    /xss/i,
    /csrf/i,
    /unauthorized/i,
    /forbidden/i,
    /invalid token/i,
    /authentication/i,
    /permission denied/i,
  ];

  return securityErrorPatterns.some(pattern =>
    pattern.test(error.message) || pattern.test(error.name)
  );
}

