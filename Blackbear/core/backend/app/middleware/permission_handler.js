'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 权限处理中间件
 * 综合处理设备权限、用户认证和资源权限验证
 *
 * 验证流程：
 * 1. 设备权限检查
 * 2. 用户认证检查
 * 3. 用户状态检查
 * 4. 资源权限验证
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function permissionHandler(ctx, next) {
    try {
      // 1. 设备权限检查
      const clientId = ctx.request.headers.clientid;
      if (clientId) {
        const device = await ctx.model.Device.findOne({
          where: { clientId },
        });

        if (device && !device.active) {
          unifiedResponse.setResponse(ctx, 'no permission', '当前设备已经被禁用！', 403);
          return;
        }
      }

      // 2. 用户认证检查
      const authHeader = ctx.request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        unifiedResponse.setResponse(ctx, null, '缺少认证信息', 401);
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const userInfo = await ctx.app.jwt.verify(token, ctx.app.config.jwt.secret);

      if (!userInfo?.id) {
        unifiedResponse.setResponse(ctx, null, '用户信息无效', 401);
        return;
      }

      // 3. 用户状态检查
      if (userInfo.isBlock) {
        unifiedResponse.setResponse(ctx, null, '用户已被禁用', 403);
        return;
      }

      // 4. 路径过滤：只对API路径进行权限验证
      const isApiPath = /^\/api\/(v1|web)\//.test(ctx.request.url);
      if (!isApiPath) {
        await next();
        return;
      }

      // 5. 资源权限验证
      const action = ctx.request.method.toLowerCase();
      let url = ctx.request.url.replace(/\?.*/g, ''); // 移除查询参数

      // 处理动态路由参数
      if (ctx.params?.id) {
        url = url.replace(ctx.params.id, ':id');
      }

      // 检查资源是否需要权限控制
      const permissionKey = ctx.helper.redisKeys.permissionsBaseActionUrl(action, url);
      const permission = await ctx.app.redis.hgetall(permissionKey);

      if (permission?.code) {
        // 需要权限控制，检查用户权限
        const rolePermissionKey = ctx.helper.redisKeys.rolePermissionsBaseRoleId(userInfo.roleId);
        const userPermissions = await ctx.app.redis.sunion(rolePermissionKey);
        const requiredPermission = `${action}_${url}`;

        if (!userPermissions.includes(requiredPermission)) {
          unifiedResponse.setResponse(ctx, null, '权限不足', 403);
          return;
        }
      }

      // 权限验证通过，继续执行
      await next();

    } catch (error) {
      console.error('Permission handler error:', error);

      // 特殊错误处理
      if (error.name === 'TokenExpiredError') {
        unifiedResponse.setResponse(ctx, null, 'Token已过期', 401);
        return;
      }

      if (error.name === 'JsonWebTokenError') {
        unifiedResponse.setResponse(ctx, null, 'Token无效', 401);
        return;
      }

      // 通用错误处理
      unifiedResponse.setResponse(ctx, null, '权限验证失败', 500);
    }
  };
};
