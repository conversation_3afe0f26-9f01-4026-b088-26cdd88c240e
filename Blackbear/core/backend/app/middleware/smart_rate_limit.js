'use strict';

/**
 * 智能限流中间件
 * 支持基于用户角色、IP、API端点的差异化限流策略
 */

module.exports = (options = {}) => {
  const defaultOptions = {
    // 全局默认限流
    global: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000, // 最大请求数
    },
    
    // 基于用户角色的限流
    userRoles: {
      admin: { max: 10000, windowMs: 15 * 60 * 1000 },
      vip: { max: 5000, windowMs: 15 * 60 * 1000 },
      premium: { max: 2000, windowMs: 15 * 60 * 1000 },
      normal: { max: 1000, windowMs: 15 * 60 * 1000 },
      guest: { max: 100, windowMs: 15 * 60 * 1000 },
    },
    
    // 基于API端点的限流
    endpoints: {
      // 认证相关 - 严格限流
      'POST /login': { max: 5, windowMs: 15 * 60 * 1000 },
      'POST /register': { max: 3, windowMs: 60 * 60 * 1000 },
      'POST /resetpwd': { max: 3, windowMs: 60 * 60 * 1000 },
      'GET /smscode': { max: 5, windowMs: 60 * 1000 },
      
      // 上传相关 - 中等限流
      'POST /api/v1/upload': { max: 50, windowMs: 60 * 1000 },
      
      // 搜索相关 - 中等限流
      'GET /api/v1/search': { max: 100, windowMs: 60 * 1000 },
      
      // 数据写入 - 中等限流
      'POST /api/v1/posts': { max: 20, windowMs: 60 * 1000 },
      'PUT /api/v1/posts/*': { max: 30, windowMs: 60 * 1000 },
      'DELETE /api/v1/posts/*': { max: 10, windowMs: 60 * 1000 },
      
      // 批量操作 - 严格限流
      'POST /api/v1/cache/clear/*': { max: 5, windowMs: 60 * 1000 },
      'POST /api/v1/task': { max: 10, windowMs: 60 * 1000 },
      
      // 公开API - 宽松限流
      'GET /api/web/*': { max: 500, windowMs: 60 * 1000 },
      'GET /api/health*': { max: 1000, windowMs: 60 * 1000 },
    },
    
    // 基于IP的特殊限流
    ipWhitelist: (process.env.BB_IP_WHITELIST || '').split(',').filter(Boolean),
    ipBlacklist: (process.env.BB_IP_BLACKLIST || '').split(',').filter(Boolean),
    
    // 限流存储
    store: 'redis', // 'memory' | 'redis'
    keyPrefix: 'rate_limit:',
    
    // 响应配置
    message: 'Too many requests, please try again later',
    headers: true,
    
    // 跳过限流的条件
    skip: (ctx) => {
      // 健康检查不限流
      if (ctx.path.startsWith('/api/health')) {
        return true;
      }
      
      // 内部服务不限流
      if (ctx.get('X-Internal-Request') === 'true') {
        return true;
      }
      
      return false;
    },
    
    ...options,
  };

  return async function smartRateLimit(ctx, next) {
    // 检查是否跳过限流
    if (defaultOptions.skip && defaultOptions.skip(ctx)) {
      return await next();
    }

    // 检查IP黑名单
    if (isIPBlacklisted(ctx.ip, defaultOptions.ipBlacklist)) {
      ctx.status = 403;
      ctx.body = {
        success: false,
        message: 'Access denied',
        code: 'IP_BLACKLISTED',
      };
      return;
    }

    // 检查IP白名单（白名单IP不限流）
    if (isIPWhitelisted(ctx.ip, defaultOptions.ipWhitelist)) {
      return await next();
    }

    // 获取限流配置
    const limitConfig = getLimitConfig(ctx, defaultOptions);
    
    // 生成限流键
    const key = generateRateLimitKey(ctx, limitConfig, defaultOptions.keyPrefix);
    
    // 检查限流
    const result = await checkRateLimit(ctx, key, limitConfig, defaultOptions.store);
    
    // 设置响应头
    if (defaultOptions.headers) {
      ctx.set('X-RateLimit-Limit', limitConfig.max);
      ctx.set('X-RateLimit-Remaining', Math.max(0, limitConfig.max - result.current));
      ctx.set('X-RateLimit-Reset', new Date(result.resetTime));
    }

    // 检查是否超过限制
    if (result.current > limitConfig.max) {
      ctx.status = 429;
      ctx.body = {
        success: false,
        message: defaultOptions.message,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
      };
      
      // 记录限流日志
      ctx.logger.warn('Rate limit exceeded', {
        ip: ctx.ip,
        user: ctx.user?.id,
        path: ctx.path,
        method: ctx.method,
        current: result.current,
        limit: limitConfig.max,
      });
      
      return;
    }

    await next();
  };
};

/**
 * 获取限流配置
 */
function getLimitConfig(ctx, options) {
  // 1. 检查端点特定配置
  const endpointKey = `${ctx.method} ${ctx.path}`;
  if (options.endpoints[endpointKey]) {
    return options.endpoints[endpointKey];
  }

  // 2. 检查通配符端点配置
  for (const [pattern, config] of Object.entries(options.endpoints)) {
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1);
      if (endpointKey.startsWith(prefix)) {
        return config;
      }
    }
  }

  // 3. 检查用户角色配置
  if (ctx.user && ctx.user.role && options.userRoles[ctx.user.role]) {
    return options.userRoles[ctx.user.role];
  }

  // 4. 使用全局默认配置
  return options.global;
}

/**
 * 生成限流键
 */
function generateRateLimitKey(ctx, limitConfig, keyPrefix) {
  const parts = [keyPrefix];
  
  // 如果用户已认证，使用用户ID
  if (ctx.user && ctx.user.id) {
    parts.push('user', ctx.user.id);
  } else {
    // 否则使用IP
    parts.push('ip', ctx.ip);
  }
  
  // 添加端点信息
  parts.push(ctx.method, ctx.path);
  
  // 添加时间窗口
  const windowStart = Math.floor(Date.now() / limitConfig.windowMs) * limitConfig.windowMs;
  parts.push(windowStart);
  
  return parts.join(':');
}

/**
 * 检查限流
 */
async function checkRateLimit(ctx, key, limitConfig, store) {
  if (store === 'redis' && ctx.app.redis) {
    return await checkRateLimitRedis(ctx, key, limitConfig);
  } else {
    return await checkRateLimitMemory(ctx, key, limitConfig);
  }
}

/**
 * Redis限流检查
 */
async function checkRateLimitRedis(ctx, key, limitConfig) {
  const redis = ctx.app.redis;
  const windowStart = Math.floor(Date.now() / limitConfig.windowMs) * limitConfig.windowMs;
  const resetTime = windowStart + limitConfig.windowMs;
  
  // 使用Redis INCR命令原子性增加计数
  const current = await redis.incr(key);
  
  // 如果是第一次访问，设置过期时间
  if (current === 1) {
    await redis.pexpire(key, limitConfig.windowMs);
  }
  
  return {
    current,
    resetTime,
  };
}

/**
 * 内存限流检查
 */
async function checkRateLimitMemory(ctx, key, limitConfig) {
  // 简单的内存实现（生产环境建议使用Redis）
  if (!ctx.app.rateLimitStore) {
    ctx.app.rateLimitStore = new Map();
  }
  
  const store = ctx.app.rateLimitStore;
  const now = Date.now();
  const windowStart = Math.floor(now / limitConfig.windowMs) * limitConfig.windowMs;
  const resetTime = windowStart + limitConfig.windowMs;
  
  const record = store.get(key) || { count: 0, resetTime: resetTime };
  
  // 如果窗口已过期，重置计数
  if (now >= record.resetTime) {
    record.count = 0;
    record.resetTime = resetTime;
  }
  
  record.count++;
  store.set(key, record);
  
  // 清理过期记录
  if (Math.random() < 0.01) { // 1%概率清理
    for (const [k, v] of store.entries()) {
      if (now >= v.resetTime) {
        store.delete(k);
      }
    }
  }
  
  return {
    current: record.count,
    resetTime: record.resetTime,
  };
}

/**
 * 检查IP是否在白名单
 */
function isIPWhitelisted(ip, whitelist) {
  return whitelist.includes(ip);
}

/**
 * 检查IP是否在黑名单
 */
function isIPBlacklisted(ip, blacklist) {
  return blacklist.includes(ip);
}
