'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 管理员权限验证中间件
 * 验证用户是否具有管理员权限
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function adminRequired(ctx, next) {
    const { user } = ctx.state;

    // 检查用户是否已登录
    if (!user) {
      unifiedResponse.setResponse(ctx, null, '需要登录', 401);
      return;
    }

    // 检查用户是否具有管理员权限
    if (user.roleId !== 'admin.top') {
      unifiedResponse.setResponse(ctx, null, '无管理员权限', 403);
      return;
    }

    await next();
  };
};
