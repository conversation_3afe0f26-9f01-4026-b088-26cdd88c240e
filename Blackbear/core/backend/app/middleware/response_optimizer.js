'use strict';

const zlib = require('zlib');
const { promisify } = require('util');

/**
 * API响应优化中间件
 * 提供响应压缩、格式优化、性能监控等功能
 * @param options
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  const defaultOptions = {
    // 压缩配置
    compression: {
      enabled: true,
      threshold: 1024, // 1KB以上启用压缩
      algorithms: [ 'gzip', 'deflate', 'br' ],
      level: 6, // 压缩级别 (1-9)
      chunkSize: 16 * 1024, // 16KB chunk size
    },
    // 缓存配置
    cache: {
      enabled: true,
      maxAge: 300, // 5分钟
      private: false,
      mustRevalidate: false,
    },
    // 响应优化配置
    optimization: {
      removeEmptyFields: true,
      compactArrays: true,
      optimizeNumbers: true,
      trimStrings: true,
    },
    // 性能监控配置
    monitoring: {
      enabled: true,
      slowResponseThreshold: 2000, // 2秒
      largeResponseThreshold: 1024 * 1024, // 1MB
    },
    // 安全头配置
    security: {
      enabled: true,
      headers: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
      },
    },
  };

  // 深度合并配置
  const config = {
    compression: { ...defaultOptions.compression, ...options.compression },
    cache: { ...defaultOptions.cache, ...options.cache },
    optimization: { ...defaultOptions.optimization, ...options.optimization },
    monitoring: { ...defaultOptions.monitoring, ...options.monitoring },
    security: {
      ...defaultOptions.security,
      ...options.security,
      headers: { ...defaultOptions.security.headers, ...options.security?.headers }
    },
  };
  const gzipAsync = promisify(zlib.gzip);
  const deflateAsync = promisify(zlib.deflate);
  const brotliCompressAsync = promisify(zlib.brotliCompress);

  return async function responseOptimizer(ctx, next) {
    const startTime = Date.now();
    const originalSend = ctx.respond;

    // 添加响应优化器到上下文
    ctx.responseOptimizer = new ResponseOptimizer(ctx, config);

    try {
      await next();

      // 优化响应数据
      if (ctx.body && typeof ctx.body === 'object') {
        ctx.body = ctx.responseOptimizer.optimizeResponseData(ctx.body);
      }

      // 设置安全头
      if (config.security.enabled) {
        ctx.responseOptimizer.setSecurityHeaders();
      }

      // 设置缓存头
      if (config.cache.enabled && ctx.method === 'GET' && ctx.status === 200) {
        ctx.responseOptimizer.setCacheHeaders();
      }

      // 响应压缩
      if (config.compression.enabled && ctx.body) {
        await ctx.responseOptimizer.compressResponse();
      }

      // 性能监控
      if (config.monitoring.enabled) {
        ctx.responseOptimizer.monitorPerformance(startTime);
      }

    } catch (error) {
      ctx.logger.error('Response optimizer error:', error);
      throw error;
    }
  };
};

/**
 * 响应优化器类
 */
class ResponseOptimizer {
  constructor(ctx, config) {
    this.ctx = ctx;
    this.config = config;
  }

  /**
   * 优化响应数据
   * @param {Object} data - 响应数据
   * @return {Object} 优化后的数据
   */
  optimizeResponseData(data) {
    if (!this.config.optimization) { return data; }

    let optimized = JSON.parse(JSON.stringify(data)); // 深拷贝

    if (this.config.optimization.removeEmptyFields) {
      optimized = this.removeEmptyFields(optimized);
    }

    if (this.config.optimization.compactArrays) {
      optimized = this.compactArrays(optimized);
    }

    if (this.config.optimization.optimizeNumbers) {
      optimized = this.optimizeNumbers(optimized);
    }

    if (this.config.optimization.trimStrings) {
      optimized = this.trimStrings(optimized);
    }

    return optimized;
  }

  /**
   * 移除空字段
   * @param {Object} obj - 对象
   * @return {Object} 处理后的对象
   */
  removeEmptyFields(obj) {
    if (Array.isArray(obj)) {
      return obj.map(item => this.removeEmptyFields(item)).filter(item => item !== null);
    }

    if (obj && typeof obj === 'object') {
      const cleaned = {};
      for (const [ key, value ] of Object.entries(obj)) {
        if (value !== null && value !== undefined && value !== '' &&
            !(Array.isArray(value) && value.length === 0) &&
            !(typeof value === 'object' && Object.keys(value).length === 0)) {
          cleaned[key] = this.removeEmptyFields(value);
        }
      }
      return cleaned;
    }

    return obj;
  }

  /**
   * 压缩数组
   * @param {Object} obj - 对象
   * @return {Object} 处理后的对象
   */
  compactArrays(obj) {
    if (Array.isArray(obj)) {
      return obj.filter(item => item !== null && item !== undefined)
        .map(item => this.compactArrays(item));
    }

    if (obj && typeof obj === 'object') {
      const compacted = {};
      for (const [ key, value ] of Object.entries(obj)) {
        compacted[key] = this.compactArrays(value);
      }
      return compacted;
    }

    return obj;
  }

  /**
   * 优化数字
   * @param {Object} obj - 对象
   * @return {Object} 处理后的对象
   */
  optimizeNumbers(obj) {
    if (Array.isArray(obj)) {
      return obj.map(item => this.optimizeNumbers(item));
    }

    if (obj && typeof obj === 'object') {
      const optimized = {};
      for (const [ key, value ] of Object.entries(obj)) {
        optimized[key] = this.optimizeNumbers(value);
      }
      return optimized;
    }

    if (typeof obj === 'number') {
      // 移除不必要的小数位
      if (obj % 1 === 0) {
        return parseInt(obj);
      }
      // 保留合理的小数位数
      return Math.round(obj * 100) / 100;
    }

    return obj;
  }

  /**
   * 修剪字符串
   * @param {Object} obj - 对象
   * @return {Object} 处理后的对象
   */
  trimStrings(obj) {
    if (Array.isArray(obj)) {
      return obj.map(item => this.trimStrings(item));
    }

    if (obj && typeof obj === 'object') {
      const trimmed = {};
      for (const [ key, value ] of Object.entries(obj)) {
        trimmed[key] = this.trimStrings(value);
      }
      return trimmed;
    }

    if (typeof obj === 'string') {
      return obj.trim();
    }

    return obj;
  }

  /**
   * 设置安全头
   */
  setSecurityHeaders() {
    const { headers } = this.config.security;

    for (const [ header, value ] of Object.entries(headers)) {
      this.ctx.set(header, value);
    }

    // 动态安全头
    if (this.ctx.protocol === 'https') {
      this.ctx.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }

    // Content Security Policy
    if (!this.ctx.get('Content-Security-Policy')) {
      this.ctx.set('Content-Security-Policy', "default-src 'self'");
    }
  }

  /**
   * 设置缓存头
   */
  setCacheHeaders() {
    const cacheConfig = this.config.cache;

    if (cacheConfig.maxAge > 0) {
      const cacheControl = [
        `max-age=${cacheConfig.maxAge}`,
        cacheConfig.private ? 'private' : 'public',
      ];

      if (cacheConfig.mustRevalidate) {
        cacheControl.push('must-revalidate');
      }

      this.ctx.set('Cache-Control', cacheControl.join(', '));
    }

    // ETag支持
    if (this.ctx.body && !this.ctx.get('ETag')) {
      const etag = this.generateETag(this.ctx.body);
      this.ctx.set('ETag', etag);
    }

    // Last-Modified
    if (!this.ctx.get('Last-Modified')) {
      this.ctx.set('Last-Modified', new Date().toUTCString());
    }
  }

  /**
   * 响应压缩
   */
  async compressResponse() {
    const { body } = this.ctx;
    if (!body) { return; }

    const bodyString = typeof body === 'string' ? body : JSON.stringify(body);
    const bodySize = Buffer.byteLength(bodyString);

    // 检查是否需要压缩
    if (bodySize < this.config.compression.threshold) {
      return;
    }

    // 检查客户端支持的压缩算法
    const acceptEncoding = this.ctx.get('Accept-Encoding') || '';
    const supportedAlgorithms = this.config.compression.algorithms;

    let algorithm = null;
    let compressedBody = null;

    // 选择最佳压缩算法
    try {
      if (acceptEncoding.includes('br') && supportedAlgorithms.includes('br')) {
        algorithm = 'br';
        compressedBody = await this.brotliCompress(bodyString);
      } else if (acceptEncoding.includes('gzip') && supportedAlgorithms.includes('gzip')) {
        algorithm = 'gzip';
        compressedBody = await this.gzipCompress(bodyString);
      } else if (acceptEncoding.includes('deflate') && supportedAlgorithms.includes('deflate')) {
        algorithm = 'deflate';
        compressedBody = await this.deflateCompress(bodyString);
      }
    } catch (compressionError) {
      this.ctx.logger.warn('Compression failed, using uncompressed response:', compressionError.message);
      // 压缩失败时使用原始响应
      return;
    }

    if (algorithm && compressedBody) {
      const compressionRatio = compressedBody.length / bodySize;

      // 只有压缩效果显著时才使用压缩
      if (compressionRatio < 0.9) {
        this.ctx.set('Content-Encoding', algorithm);
        this.ctx.set('Content-Length', compressedBody.length);
        this.ctx.body = compressedBody;

        // 记录压缩统计
        this.recordCompressionStats(bodySize, compressedBody.length, algorithm);
      }
    }
  }

  /**
   * Gzip压缩
   * @param {string} data - 数据
   * @return {Promise<Buffer>} 压缩后的数据
   */
  async gzipCompress(data) {
    const gzipAsync = promisify(zlib.gzip);
    return await gzipAsync(Buffer.from(data), {
      level: this.config.compression.level,
      chunkSize: this.config.compression.chunkSize,
    });
  }

  /**
   * Deflate压缩
   * @param {string} data - 数据
   * @return {Promise<Buffer>} 压缩后的数据
   */
  async deflateCompress(data) {
    const deflateAsync = promisify(zlib.deflate);
    return await deflateAsync(Buffer.from(data), {
      level: this.config.compression.level,
      chunkSize: this.config.compression.chunkSize,
    });
  }

  /**
   * Brotli压缩
   * @param {string} data - 数据
   * @return {Promise<Buffer>} 压缩后的数据
   */
  async brotliCompress(data) {
    const brotliCompressAsync = promisify(zlib.brotliCompress);

    // 确保压缩级别是有效的数字
    const compressionLevel = this.config.compression?.level || 6;
    const validLevel = Math.max(1, Math.min(11, compressionLevel)); // Brotli质量范围是1-11

    return await brotliCompressAsync(Buffer.from(data), {
      params: {
        [zlib.constants.BROTLI_PARAM_QUALITY]: validLevel,
      },
    });
  }

  /**
   * 生成ETag
   * @param {*} body - 响应体
   * @return {string} ETag值
   */
  generateETag(body) {
    const crypto = require('crypto');
    const content = typeof body === 'string' ? body : JSON.stringify(body);
    return `"${crypto.createHash('md5').update(content).digest('hex')}"`;
  }

  /**
   * 性能监控
   * @param {number} startTime - 开始时间
   */
  monitorPerformance(startTime) {
    const duration = Date.now() - startTime;
    const responseSize = this.getResponseSize();

    // 记录响应时间
    this.ctx.set('X-Response-Time', `${duration}ms`);

    // 检查慢响应
    if (duration > this.config.monitoring.slowResponseThreshold) {
      this.ctx.logger.warn(`Slow response: ${duration}ms for ${this.ctx.method} ${this.ctx.path}`);
      this.recordSlowResponse(duration, responseSize);
    }

    // 检查大响应
    if (responseSize > this.config.monitoring.largeResponseThreshold) {
      this.ctx.logger.warn(`Large response: ${Math.round(responseSize / 1024)}KB for ${this.ctx.method} ${this.ctx.path}`);
      this.recordLargeResponse(responseSize, duration);
    }

    // 记录性能指标
    this.recordPerformanceMetrics(duration, responseSize);
  }

  /**
   * 获取响应大小
   * @return {number} 响应大小（字节）
   */
  getResponseSize() {
    if (!this.ctx.body) { return 0; }

    if (Buffer.isBuffer(this.ctx.body)) {
      return this.ctx.body.length;
    }

    const bodyString = typeof this.ctx.body === 'string' ?
      this.ctx.body : JSON.stringify(this.ctx.body);
    return Buffer.byteLength(bodyString);
  }

  /**
   * 记录压缩统计
   * @param {number} originalSize - 原始大小
   * @param {number} compressedSize - 压缩后大小
   * @param {string} algorithm - 压缩算法
   */
  recordCompressionStats(originalSize, compressedSize, algorithm) {
    this.ctx.app.emit('compression-stats', {
      originalSize,
      compressedSize,
      algorithm,
      ratio: compressedSize / originalSize,
      savings: originalSize - compressedSize,
      path: this.ctx.path,
      timestamp: new Date(),
    });
  }

  /**
   * 记录慢响应
   * @param {number} duration - 响应时间
   * @param {number} size - 响应大小
   */
  recordSlowResponse(duration, size) {
    this.ctx.app.emit('slow-response', {
      duration,
      size,
      path: this.ctx.path,
      method: this.ctx.method,
      status: this.ctx.status,
      traceId: this.ctx.traceId,
      timestamp: new Date(),
    });
  }

  /**
   * 记录大响应
   * @param {number} size - 响应大小
   * @param {number} duration - 响应时间
   */
  recordLargeResponse(size, duration) {
    this.ctx.app.emit('large-response', {
      size,
      duration,
      path: this.ctx.path,
      method: this.ctx.method,
      status: this.ctx.status,
      traceId: this.ctx.traceId,
      timestamp: new Date(),
    });
  }

  /**
   * 记录性能指标
   * @param {number} duration - 响应时间
   * @param {number} size - 响应大小
   */
  recordPerformanceMetrics(duration, size) {
    this.ctx.app.emit('response-metrics', {
      duration,
      size,
      path: this.ctx.path,
      method: this.ctx.method,
      status: this.ctx.status,
      timestamp: new Date(),
    });
  }
}

module.exports.ResponseOptimizer = ResponseOptimizer;
