'use strict';

/**
 * 增强安全中间件
 * 整合多个安全功能到一个中间件中，简化配置
 */

const crypto = require('crypto');

module.exports = (options = {}) => {
  const defaultOptions = {
    // XSS防护
    enableXssFilter: true,
    
    // SQL注入防护
    enableSqlInjectionFilter: true,
    
    // 文件名过滤
    enableFilenameFilter: true,
    
    // URL验证
    enableUrlValidation: true,
    
    // 请求体大小限制
    maxBodySize: 10 * 1024 * 1024, // 10MB
    
    // 需要过滤的字段
    filterFields: ['content', 'description', 'comment', 'message', 'text'],
    
    // 跳过的路径
    skipPaths: ['/api/health', '/api/health/*'],
    
    // 限流配置
    rateLimit: {
      enabled: true,
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000, // 默认限制
      
      // 特殊端点限制
      endpoints: {
        'POST /login': { max: 5, windowMs: 15 * 60 * 1000 },
        'POST /register': { max: 3, windowMs: 60 * 60 * 1000 },
        'POST /resetpwd': { max: 3, windowMs: 60 * 60 * 1000 },
        'GET /smscode': { max: 5, windowMs: 60 * 1000 },
      },
    },
    
    ...options,
  };

  // 内存存储限流数据
  const rateLimitStore = new Map();

  return async function enhancedSecurity(ctx, next) {
    const startTime = Date.now();

    try {
      // 1. 检查是否跳过安全检查
      if (shouldSkipSecurity(ctx.path, defaultOptions.skipPaths)) {
        return await next();
      }

      // 2. 限流检查
      if (defaultOptions.rateLimit.enabled) {
        const rateLimitResult = await checkRateLimit(ctx, defaultOptions.rateLimit, rateLimitStore);
        if (!rateLimitResult.allowed) {
          ctx.status = 429;
          ctx.body = {
            success: false,
            message: rateLimitResult.message || 'Too many requests',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: rateLimitResult.retryAfter,
          };
          return;
        }
      }

      // 3. 请求体大小检查
      if (ctx.request.body && JSON.stringify(ctx.request.body).length > defaultOptions.maxBodySize) {
        ctx.status = 413;
        ctx.body = {
          success: false,
          message: 'Request body too large',
          code: 'PAYLOAD_TOO_LARGE',
        };
        return;
      }

      // 4. 安全过滤
      if (ctx.request.body) {
        ctx.request.body = sanitizeObject(ctx.request.body, defaultOptions);
      }

      if (ctx.query) {
        ctx.query = sanitizeObject(ctx.query, defaultOptions);
      }

      // 5. 设置安全响应头
      setSecurityHeaders(ctx);

      // 6. 记录安全日志
      logSecurityEvent(ctx, {
        type: 'request_processed',
        ip: ctx.ip,
        userAgent: ctx.get('User-Agent'),
        path: ctx.path,
        method: ctx.method,
        processingTime: Date.now() - startTime,
      });

      await next();

    } catch (error) {
      // 记录安全错误
      logSecurityEvent(ctx, {
        type: 'security_error',
        error: error.message,
        ip: ctx.ip,
        path: ctx.path,
        method: ctx.method,
      });
      throw error;
    }
  };
};

/**
 * 检查是否跳过安全检查
 */
function shouldSkipSecurity(path, skipPaths) {
  return skipPaths.some(pattern => {
    if (pattern.endsWith('*')) {
      return path.startsWith(pattern.slice(0, -1));
    }
    return path === pattern;
  });
}

/**
 * 限流检查
 */
async function checkRateLimit(ctx, rateLimitConfig, store) {
  const key = generateRateLimitKey(ctx);
  const now = Date.now();
  
  // 获取端点特定配置
  const endpointKey = `${ctx.method} ${ctx.path}`;
  const endpointConfig = rateLimitConfig.endpoints[endpointKey] || {
    max: rateLimitConfig.max,
    windowMs: rateLimitConfig.windowMs,
  };

  // 获取或创建限流记录
  let record = store.get(key);
  if (!record || now - record.resetTime >= endpointConfig.windowMs) {
    record = {
      count: 0,
      resetTime: now + endpointConfig.windowMs,
    };
  }

  record.count++;
  store.set(key, record);

  // 清理过期记录
  if (Math.random() < 0.01) { // 1%概率清理
    for (const [k, v] of store.entries()) {
      if (now >= v.resetTime) {
        store.delete(k);
      }
    }
  }

  // 检查是否超过限制
  if (record.count > endpointConfig.max) {
    return {
      allowed: false,
      message: `Too many requests for ${endpointKey}`,
      retryAfter: Math.ceil((record.resetTime - now) / 1000),
    };
  }

  return { allowed: true };
}

/**
 * 生成限流键
 */
function generateRateLimitKey(ctx) {
  const ip = ctx.ip;
  const userId = ctx.user?.id || 'anonymous';
  const endpoint = `${ctx.method}:${ctx.path}`;
  return `rate_limit:${ip}:${userId}:${endpoint}`;
}

/**
 * 清理对象中的危险内容
 */
function sanitizeObject(obj, options) {
  if (!obj || typeof obj !== 'object') {
    return sanitizeString(obj, options);
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, options));
  }

  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    // 检查字段名是否安全
    if (options.enableFilenameFilter && !isValidFieldName(key)) {
      continue; // 跳过不安全的字段名
    }

    sanitized[key] = sanitizeObject(value, options);
  }

  return sanitized;
}

/**
 * 清理字符串
 */
function sanitizeString(str, options) {
  if (typeof str !== 'string') {
    return str;
  }

  let sanitized = str;

  // XSS过滤
  if (options.enableXssFilter) {
    sanitized = sanitized
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  // SQL注入过滤
  if (options.enableSqlInjectionFilter) {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(;|\||&|\$|\+|'|"|`)/g,
      /(-{2}|\/\*|\*\/)/g,
    ];

    for (const pattern of sqlPatterns) {
      sanitized = sanitized.replace(pattern, '');
    }
  }

  // URL验证
  if (options.enableUrlValidation && str.includes('://')) {
    try {
      const url = new URL(str);
      if (!['http:', 'https:'].includes(url.protocol)) {
        return '[FILTERED_URL]';
      }
    } catch (error) {
      return '[INVALID_URL]';
    }
  }

  return sanitized.trim();
}

/**
 * 验证字段名是否安全
 */
function isValidFieldName(fieldName) {
  // 检查是否包含危险字符
  const dangerousChars = /[<>'";&|$`]/;
  if (dangerousChars.test(fieldName)) {
    return false;
  }

  // 检查是否为系统保留字段
  const reservedFields = ['__proto__', 'constructor', 'prototype'];
  if (reservedFields.includes(fieldName)) {
    return false;
  }

  return true;
}

/**
 * 设置安全响应头
 */
function setSecurityHeaders(ctx) {
  ctx.set('X-Content-Type-Options', 'nosniff');
  ctx.set('X-Frame-Options', 'DENY');
  ctx.set('X-XSS-Protection', '1; mode=block');
  ctx.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  ctx.set('X-Download-Options', 'noopen');
  ctx.set('X-Permitted-Cross-Domain-Policies', 'none');
}

/**
 * 记录安全事件
 */
function logSecurityEvent(ctx, event) {
  const logData = {
    timestamp: new Date().toISOString(),
    traceId: ctx.traceId || generateTraceId(),
    ...event,
  };

  if (event.type === 'security_error' || event.type === 'rate_limit_exceeded') {
    ctx.logger.warn('Security event', logData);
  } else {
    ctx.logger.debug('Security event', logData);
  }
}

/**
 * 生成追踪ID
 */
function generateTraceId() {
  return crypto.randomBytes(8).toString('hex');
}
