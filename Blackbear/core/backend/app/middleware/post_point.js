'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 文章积分门槛验证中间件
 * 验证用户是否有足够积分查看文章
 *
 * 权限规则：
 * 1. 管理员和合伙人拥有所有权限
 * 2. 文章作者可以查看自己的文章
 * 3. 普通用户需要足够积分才能查看
 * 4. 只针对设置了积分门槛的文章
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function postPoint(ctx, next) {
    const { helper } = ctx;

    // 管理员和合伙人有操作所有文章的权限
    if (helper.hasRole([ 'admin.top', 'partner' ])) {
      await next();
      return;
    }

    const postId = ctx.params.id;
    if (postId && postId.length > 0) {
      try {
        const post = await ctx.model.Post.findByPk(postId);

        // 文章不存在
        if (!post) {
          unifiedResponse.setResponse(ctx, null, '文章不存在', 404);
          return;
        }

        // 文章需要积分门槛
        if (post.point > 0) {
          // 提取token
          const authHeader = ctx.request.headers.authorization;
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            unifiedResponse.setResponse(ctx, null, '需要登录才能查看此文章！', 401);
            return;
          }

          const token = authHeader.split('Bearer ')[1];
          try {
            const userInfo = await ctx.app.jwt.verify(token, ctx.app.config.jwt.secret);

            // 用户信息验证
            if (!userInfo?.id) {
              unifiedResponse.setResponse(ctx, null, '用户信息无效！', 401);
              return;
            }

            // 文章作者可以查看自己的文章
            if (userInfo.id === post.userId) {
              await next();
              return;
            }

            // 检查积分是否足够
            const userPoint = userInfo.point || 0;
            if (userPoint < post.point) {
              unifiedResponse.setResponse(
                ctx,
                null,
                `积分不够，需要${post.point}积分才能查看！当前积分：${userPoint}`,
                403
              );
              return;
            }
          } catch (jwtError) {
            console.error('JWT verification error:', jwtError);
            unifiedResponse.setResponse(ctx, null, '登录状态无效！', 401);
            return;
          }
        }
      } catch (error) {
        // 数据库查询错误
        console.error('Post point check error:', error);
        unifiedResponse.setResponse(ctx, null, '文章积分检查失败', 500);
        return;
      }
    }

    await next();
  };
};
