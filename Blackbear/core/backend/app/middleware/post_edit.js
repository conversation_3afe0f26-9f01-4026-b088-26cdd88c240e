'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 文章编辑权限验证中间件
 * 验证用户是否有权限查看和修改文章
 *
 * 权限规则：
 * 1. 管理员和合伙人有操作所有文章的权限
 * 2. 普通用户只能查看已发布的文章和修改自己的文章
 * 3. 更新文章时禁止将状态设为published（只有管理员才有审核权限）
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function postEdit(ctx, next) {
    const { method, helper } = ctx;
    const { user } = ctx.state;

    // GET请求或管理员和合伙人有操作所有文章的权限
    if (method === 'GET' || helper.hasRole([ 'admin.top', 'partner' ])) {
      await next();
      return;
    }

    const postId = ctx.params.id;
    if (postId && postId.length > 0) {
      try {
        const post = await ctx.model.Post.findByPk(postId);

        if (!post) {
          unifiedResponse.setResponse(ctx, null, '文章不存在', 404);
          return;
        }

        // 检查权限：非已发布文章且不是文章作者
        if (post.status !== 'published' && user?.id !== post.userId) {
          unifiedResponse.setResponse(ctx, null, '您没有权限查看和修改文章！', 403);
          return;
        }
      } catch (error) {
        console.error('Post edit permission check error:', error);
        unifiedResponse.setResponse(ctx, null, '文章权限验证失败', 500);
        return;
      }
    }

    await next();
  };
};
