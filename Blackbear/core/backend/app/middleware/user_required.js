'use strict';

/**
 * 用户身份验证中间件
 * 解析JWT token并设置用户信息到ctx.userInfo
 * 仅对/api/v1/路径生效
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function userRequired(ctx, next) {
    // 只对API路径进行处理
    if (!/^\/api\/v1\//.test(ctx.request.url)) {
      await next();
      return;
    }

    try {
      // 提取Authorization头中的token
      const authHeader = ctx.request.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split('Bearer ')[1];

        // 验证JWT token并设置用户信息
        const userInfo = await ctx.app.jwt.verify(token, ctx.app.config.jwt.secret);
        ctx.userInfo = userInfo;
      }
    } catch (error) {
      // JWT验证失败，继续执行但不设置userInfo
      console.warn('JWT verification failed:', error.message);
    }

    await next();
  };
};
