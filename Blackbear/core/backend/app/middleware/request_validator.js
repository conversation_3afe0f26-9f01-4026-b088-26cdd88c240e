'use strict';

/**
 * 请求验证中间件
 * 统一的参数验证和数据清理
 */

const Joi = require('joi');

module.exports = (schema, options = {}) => {
  const defaultOptions = {
    // 验证选项
    allowUnknown: false,
    stripUnknown: true,
    abortEarly: false,
    
    // 验证目标
    validateBody: true,
    validateQuery: true,
    validateParams: true,
    validateHeaders: false,
    
    // 错误处理
    customErrorMessages: true,
    
    ...options,
  };

  // 编译验证模式
  const compiledSchema = compileSchema(schema);

  return async function requestValidator(ctx, next) {
    const errors = [];

    try {
      // 验证请求体
      if (defaultOptions.validateBody && compiledSchema.body) {
        const { error, value } = compiledSchema.body.validate(
          ctx.request.body,
          defaultOptions
        );
        
        if (error) {
          errors.push(...formatJoiErrors(error, 'body'));
        } else {
          ctx.request.body = value;
        }
      }

      // 验证查询参数
      if (defaultOptions.validateQuery && compiledSchema.query) {
        const { error, value } = compiledSchema.query.validate(
          ctx.query,
          defaultOptions
        );
        
        if (error) {
          errors.push(...formatJoiErrors(error, 'query'));
        } else {
          ctx.query = value;
        }
      }

      // 验证路径参数
      if (defaultOptions.validateParams && compiledSchema.params) {
        const { error, value } = compiledSchema.params.validate(
          ctx.params,
          defaultOptions
        );
        
        if (error) {
          errors.push(...formatJoiErrors(error, 'params'));
        } else {
          ctx.params = value;
        }
      }

      // 验证请求头
      if (defaultOptions.validateHeaders && compiledSchema.headers) {
        const { error, value } = compiledSchema.headers.validate(
          ctx.headers,
          defaultOptions
        );
        
        if (error) {
          errors.push(...formatJoiErrors(error, 'headers'));
        }
      }

      // 如果有验证错误，返回400
      if (errors.length > 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          errors,
        };
        return;
      }

      await next();
    } catch (error) {
      ctx.logger.error('Request validation error:', error);
      throw error;
    }
  };
};

/**
 * 编译验证模式
 */
function compileSchema(schema) {
  const compiled = {};

  if (schema.body) {
    compiled.body = Joi.compile(schema.body);
  }

  if (schema.query) {
    compiled.query = Joi.compile(schema.query);
  }

  if (schema.params) {
    compiled.params = Joi.compile(schema.params);
  }

  if (schema.headers) {
    compiled.headers = Joi.compile(schema.headers);
  }

  return compiled;
}

/**
 * 格式化Joi验证错误
 */
function formatJoiErrors(joiError, location) {
  return joiError.details.map(detail => ({
    field: `${location}.${detail.path.join('.')}`,
    message: detail.message,
    value: detail.context.value,
    type: detail.type,
  }));
}

// 导出常用的验证规则
module.exports.schemas = {
  // 用户相关
  user: {
    register: {
      body: Joi.object({
        username: Joi.string().alphanum().min(3).max(30).required()
          .messages({
            'string.alphanum': '用户名只能包含字母和数字',
            'string.min': '用户名至少3个字符',
            'string.max': '用户名最多30个字符',
            'any.required': '用户名是必填项',
          }),
        password: Joi.string().min(8).max(128).required()
          .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
          .messages({
            'string.min': '密码至少8个字符',
            'string.max': '密码最多128个字符',
            'string.pattern.base': '密码必须包含大小写字母、数字和特殊字符',
            'any.required': '密码是必填项',
          }),
        email: Joi.string().email().required()
          .messages({
            'string.email': '请输入有效的邮箱地址',
            'any.required': '邮箱是必填项',
          }),
        nickname: Joi.string().max(50).optional(),
      }),
    },
    
    login: {
      body: Joi.object({
        username: Joi.string().required(),
        password: Joi.string().required(),
        captcha: Joi.string().length(4).optional(),
      }),
    },
    
    profile: {
      body: Joi.object({
        nickname: Joi.string().max(50).optional(),
        bio: Joi.string().max(500).optional(),
        avatar: Joi.string().uri().optional(),
      }),
    },
  },

  // 分页参数
  pagination: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sort: Joi.string().valid('id', 'createdAt', 'updatedAt', 'name').default('id'),
      order: Joi.string().valid('ASC', 'DESC').default('DESC'),
    }),
  },

  // ID参数
  id: {
    params: Joi.object({
      id: Joi.number().integer().positive().required(),
    }),
  },

  // 搜索参数
  search: {
    query: Joi.object({
      keyword: Joi.string().min(1).max(100).required(),
      category: Joi.string().optional(),
      tags: Joi.array().items(Joi.string()).optional(),
    }),
  },

  // 股票相关
  stock: {
    create: {
      body: Joi.object({
        code: Joi.string().pattern(/^[A-Z0-9]{6}$/).required()
          .messages({
            'string.pattern.base': '股票代码必须是6位字母或数字',
          }),
        name: Joi.string().min(1).max(100).required(),
        market: Joi.string().valid('SH', 'SZ', 'HK', 'US').required(),
        industry: Joi.string().max(50).optional(),
        sector: Joi.string().max(50).optional(),
      }),
    },
    
    update: {
      body: Joi.object({
        name: Joi.string().min(1).max(100).optional(),
        industry: Joi.string().max(50).optional(),
        sector: Joi.string().max(50).optional(),
        status: Joi.number().valid(0, 1).optional(),
      }),
    },
  },

  // 新闻相关
  news: {
    create: {
      body: Joi.object({
        title: Joi.string().min(1).max(200).required(),
        content: Joi.string().min(1).required(),
        summary: Joi.string().max(500).optional(),
        source: Joi.string().max(100).required(),
        author: Joi.string().max(50).optional(),
        url: Joi.string().uri().required(),
        publishTime: Joi.date().iso().optional(),
        tags: Joi.array().items(Joi.string()).optional(),
      }),
    },
    
    update: {
      body: Joi.object({
        title: Joi.string().min(1).max(200).optional(),
        content: Joi.string().min(1).optional(),
        summary: Joi.string().max(500).optional(),
        author: Joi.string().max(50).optional(),
        status: Joi.number().valid(0, 1, 2).optional(),
        tags: Joi.array().items(Joi.string()).optional(),
      }),
    },
  },

  // 文件上传
  upload: {
    body: Joi.object({
      type: Joi.string().valid('image', 'document', 'video', 'audio').required(),
      category: Joi.string().max(50).optional(),
      description: Joi.string().max(200).optional(),
    }),
  },

  // 批量操作
  batch: {
    body: Joi.object({
      ids: Joi.array().items(Joi.number().integer().positive()).min(1).max(100).required(),
      action: Joi.string().valid('delete', 'activate', 'deactivate').required(),
    }),
  },
};

// 导出验证装饰器
module.exports.validate = (schema) => {
  return module.exports(schema);
};

// 导出常用验证规则组合
module.exports.common = {
  // 带分页的列表查询
  listWithPagination: (additionalQuery = {}) => ({
    query: Joi.object({
      ...module.exports.schemas.pagination.query.describe().keys,
      ...additionalQuery,
    }),
  }),

  // 带ID的详情查询
  detailById: module.exports.schemas.id,

  // 带ID的更新操作
  updateById: (bodySchema) => ({
    params: module.exports.schemas.id.params,
    body: bodySchema,
  }),

  // 带ID的删除操作
  deleteById: module.exports.schemas.id,
};
