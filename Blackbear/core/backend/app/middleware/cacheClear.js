'use strict';

/**
 * 数据库操作后自动清除缓存中间件
 * 监听数据库操作并自动清除相关缓存
 * 适配BaseCacheService的缓存机制
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function cacheClear(ctx, next) {
    // 记录原始请求信息
    const originalBody = ctx.request.body;
    const originalMethod = ctx.method;
    const originalUrl = ctx.url;

    // 执行后续中间件和控制器
    await next();

    // 根据请求类型判断是否需要清除缓存
    if (shouldClearCache(originalMethod, originalUrl)) {
      try {
        await clearRelatedCache(ctx, originalMethod, originalUrl, originalBody, ctx.body);
      } catch (error) {
        ctx.logger.error('Error clearing cache after database operation', error);
      }
    }
  };
};

/**
 * 判断是否需要清除缓存
 * @param method
 * @param url
 */
function shouldClearCache(method, url) {
  // 只处理修改数据的请求
  if (![ 'POST', 'PUT', 'DELETE', 'PATCH' ].includes(method)) {
    return false;
  }

  // 定义需要清除缓存的路径模式
  const cacheClearPatterns = [
    /\/api\/v1\/stocks/,
    /\/api\/v1\/news/,
    /\/api\/v1\/reports/,
    /\/api\/v1\/researchs/,
    /\/api\/v1\/posts/,
    /\/api\/v1\/events/,
    /\/api\/v1\/tags/,
    /\/api\/v1\/groups/,
    /\/api\/v1\/quants/,
    /\/api\/v1\/messages/,
    /\/api\/v1\/users/,
    /\/api\/v1\/funds/,
    /\/api\/v1\/positions/,
    /\/api\/v1\/points/,
    /\/api\/v1\/medias/,
    /\/api\/v1\/calendars/,
    /\/api\/v1\/worths/,
    /\/api\/v1\/sorts/,
    /\/api\/v1\/configurations/,
    /\/api\/v1\/devices/,
    /\/api\/v1\/dictionary/,
    /\/api\/v1\/material/,
    /\/api\/v1\/sources/,
  ];

  return cacheClearPatterns.some(pattern => pattern.test(url));
}

/**
 * 清除相关缓存
 * 适配BaseCacheService的缓存机制
 * @param ctx
 * @param method
 * @param url
 * @param requestBody
 * @param responseBody
 */
async function clearRelatedCache(ctx, method, url, requestBody, responseBody) {
  try {
    // 解析URL获取资源类型和ID
    const urlParts = url.split('/');
    const resourceType = urlParts[urlParts.length - 2]; // 例如: stocks, news
    const resourceId = urlParts[urlParts.length - 1]; // 资源ID

    // 根据资源类型和操作类型清除相关缓存
    switch (resourceType) {
      case 'stocks':
        await clearStockCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'news':
        await clearNewsCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'reports':
        await clearReportCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'researchs':
        await clearResearchCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'posts':
        await clearPostCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'events':
        await clearEventCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'tags':
        await clearTagCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'groups':
        await clearGroupCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'quants':
        await clearQuantsCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'messages':
        await clearMessagesCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'users':
        await clearUserCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'funds':
        await clearFundCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'positions':
        await clearPositionCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'points':
        await clearPointCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'medias':
        await clearMediaCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'calendars':
        await clearCalendarCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'worths':
        await clearWorthCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'sorts':
        await clearSortCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'configurations':
        await clearConfigCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'devices':
        await clearDeviceCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'dictionary':
        await clearDictionaryCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'material':
        await clearMaterialCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'sources':
        await clearSourcesCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'menus':
        await clearMenusCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'permissions':
        await clearPermissionsCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      case 'operate':
        await clearOperateCache(ctx, method, resourceId, requestBody, responseBody);
        break;
      default:
        // 对于未知资源类型，清除所有缓存以确保数据一致性
        await ctx.service.cacheManager.clearAllCache();
    }

    ctx.logger.info('Cache cleared after database operation', {
      method,
      url,
      resourceType,
      resourceId,
    });
  } catch (error) {
    ctx.logger.error('Error clearing related cache', error);
  }
}

// 各种资源类型的缓存清除函数
async function clearStockCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    // 删除股票时，清除该股票的所有缓存
    await ctx.service.stocks.clearRelatedCache('stocks', { code: responseBody?.code });
  } else {
    // 更新股票时，清除该股票的相关缓存
    const code = requestBody?.code || responseBody?.code;
    await ctx.service.stocks.clearRelatedCache('stocks', { code });
  }
}

async function clearNewsCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.cacheManager.clearNewsCache({ newsId: resourceId });
  } else {
    const newsId = requestBody?.newsID || responseBody?.newsID || resourceId;
    const code = requestBody?.code || responseBody?.code;
    await ctx.service.cacheManager.clearNewsCache({ newsId, code });
  }
}

async function clearReportCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.cacheManager.clearReportCache({ announcementId: resourceId });
  } else {
    const announcementId = requestBody?.announcementId || responseBody?.announcementId || resourceId;
    const code = requestBody?.secCode || responseBody?.secCode;
    await ctx.service.cacheManager.clearReportCache({ announcementId, code });
  }
}

async function clearResearchCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.cacheManager.clearResearchCache({ researchId: resourceId });
  } else {
    const researchId = requestBody?.id || responseBody?.id || resourceId;
    const code = requestBody?.code || responseBody?.code;
    await ctx.service.cacheManager.clearResearchCache({ researchId, code });
  }
}

async function clearPostCache(ctx, method, resourceId, requestBody, responseBody) {
  // 帖子可能影响股票热度，清除股票相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearEventCache(ctx, method, resourceId, requestBody, responseBody) {
  // 事件可能影响股票，清除股票相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearTagCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.tags.clearRelatedCache('tags', { tagId: resourceId });
  } else {
    const tagId = requestBody?.id || responseBody?.id || resourceId;
    const userId = requestBody?.userId || responseBody?.userId;
    const code = requestBody?.code || responseBody?.code;
    await ctx.service.tags.clearRelatedCache('tags', { tagId, userId, code });
  }
}

async function clearGroupCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.groups.clearRelatedCache('groups', { id: resourceId });
  } else {
    const id = requestBody?.id || responseBody?.id || resourceId;
    const code = requestBody?.code || responseBody?.code;
    await ctx.service.groups.clearRelatedCache('groups', { id, code });
  }
}

async function clearQuantsCache(ctx, method, resourceId, requestBody, responseBody) {
  // 量化数据变化可能影响所有量化相关缓存，清除量化数据缓存
  await ctx.service.quants.clearRelatedCache('quants');
}

async function clearMessagesCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.messages.clearRelatedCache('messages', { messageId: resourceId });
  } else {
    const userId = requestBody?.userId || responseBody?.userId;
    const type = requestBody?.type || responseBody?.type;
    await ctx.service.messages.clearRelatedCache('messages', { userId, type });
  }
}

async function clearUserCache(ctx, method, resourceId, requestBody, responseBody) {
  // 用户变化可能影响权限，清除所有缓存以确保安全
  await ctx.service.cacheManager.clearAllCache();
}

async function clearFundCache(ctx, method, resourceId, requestBody, responseBody) {
  // 基金变化可能影响股票关联，清除股票相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearPositionCache(ctx, method, resourceId, requestBody, responseBody) {
  // 职位变化可能影响权限，清除所有缓存
  await ctx.service.cacheManager.clearAllCache();
}

async function clearPointCache(ctx, method, resourceId, requestBody, responseBody) {
  // 积分变化可能影响用户权限，清除所有缓存
  await ctx.service.cacheManager.clearAllCache();
}

async function clearMediaCache(ctx, method, resourceId, requestBody, responseBody) {
  // 媒体变化可能影响内容展示，清除相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearCalendarCache(ctx, method, resourceId, requestBody, responseBody) {
  // 日历变化可能影响事件，清除相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearWorthCache(ctx, method, resourceId, requestBody, responseBody) {
  // 净值变化可能影响股票估值，清除股票相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearSortCache(ctx, method, resourceId, requestBody, responseBody) {
  // 分类变化可能影响股票分类，清除股票相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearMessageCache(ctx, method, resourceId, requestBody, responseBody) {
  // 消息变化可能影响用户通知，清除相关缓存
  await ctx.service.cacheManager.clearAllCache();
}

async function clearConfigCache(ctx, method, resourceId, requestBody, responseBody) {
  // 配置变化可能影响系统行为，清除所有缓存
  await ctx.service.cacheManager.clearAllCache();
}

async function clearDeviceCache(ctx, method, resourceId, requestBody, responseBody) {
  // 设备变化可能影响用户认证，清除所有缓存
  await ctx.service.cacheManager.clearAllCache();
}

async function clearDictionaryCache(ctx, method, resourceId, requestBody, responseBody) {
  // 字典变化可能影响基础数据，清除所有缓存
  await ctx.service.cacheManager.clearAllCache();
}

async function clearMaterialCache(ctx, method, resourceId, requestBody, responseBody) {
  // 素材变化可能影响内容展示，清除相关缓存
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearSourcesCache(ctx, method, resourceId, requestBody, responseBody) {
  // 数据源变化可能影响股票数据，清除相关缓存
  await ctx.service.sources.clearRelatedCache('sources');
  await ctx.service.stocks.clearRelatedCache('stocks');
}

async function clearMenusCache(ctx, method, resourceId, requestBody, responseBody) {
  await ctx.service.menus.clearRelatedCache('menus');
}

async function clearPermissionsCache(ctx, method, resourceId, requestBody, responseBody) {
  await ctx.service.permissions.clearRelatedCache('permissions');
}

async function clearOperateCache(ctx, method, resourceId, requestBody, responseBody) {
  await ctx.service.operate.clearRelatedCache('operate');
}

async function clearTagsCache(ctx, method, resourceId, requestBody, responseBody) {
  if (method === 'DELETE') {
    await ctx.service.tags.clearRelatedCache('tags', { tagId: resourceId });
  } else {
    const tagId = requestBody?.id || responseBody?.id || resourceId;
    const userId = requestBody?.userId || responseBody?.userId;
    const code = requestBody?.code || responseBody?.code;
    await ctx.service.tags.clearRelatedCache('tags', { tagId, userId, code });
  }
}
