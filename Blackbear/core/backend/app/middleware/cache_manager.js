'use strict';

/**
 * 缓存管理中间件
 * 提供缓存预热、自动失效、性能监控等功能
 * @param options
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  const defaultOptions = {
    // 缓存预热配置
    warmup: {
      enabled: true,
      onStartup: true,
      schedule: '0 0 * * *', // 每天凌晨预热
    },
    // 自动失效配置
    autoInvalidation: {
      enabled: true,
      patterns: {
        'user_data:*': [ 'user.update', 'user.delete' ],
        'stock_data:*': [ 'stock.update', 'stock.delete' ],
        'news_data:*': [ 'news.create', 'news.update', 'news.delete' ],
      },
    },
    // 性能监控
    monitoring: {
      enabled: true,
      slowCacheThreshold: 100, // 100ms
      reportInterval: 60000, // 1分钟
    },
    // 缓存压缩
    compression: {
      enabled: true,
      threshold: 1024, // 1KB以上启用压缩
    },
  };

  const config = { ...defaultOptions, ...options };

  return async function cacheManager(ctx, next) {
    const startTime = Date.now();

    // 添加缓存管理器到上下文
    ctx.cacheManager = new CacheManager(ctx, config);

    // 初始化缓存统计
    ctx.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      totalTime: 0,
    };

    try {
      await next();

      // 记录缓存性能
      const totalTime = Date.now() - startTime;
      ctx.cacheStats.totalTime = totalTime;

      // 检查缓存性能
      if (config.monitoring.enabled && totalTime > config.monitoring.slowCacheThreshold) {
        ctx.logger.warn(`Slow cache operation detected: ${totalTime}ms for ${ctx.path}`);
      }

    } finally {
      // 发送缓存统计
      if (config.monitoring.enabled) {
        ctx.app.emit('cache-stats', {
          ...ctx.cacheStats,
          path: ctx.path,
          method: ctx.method,
          timestamp: new Date(),
        });
      }
    }
  };
};

/**
 * 缓存管理器类
 */
class CacheManager {
  constructor(ctx, config) {
    this.ctx = ctx;
    this.config = config;
    this.app = ctx.app;
    this.redis = ctx.app.redis;
  }

  /**
   * 智能缓存获取
   * @param {string} key - 缓存键
   * @param {Function} fetcher - 数据获取函数
   * @param {Object} options - 选项
   * @return {Promise} 缓存数据
   */
  async get(key, fetcher, options = {}) {
    const {
      ttl = 300,
      compress = this.config.compression.enabled,
      forceRefresh = false,
      category = 'default',
    } = options;

    const fullKey = this.buildKey(key, category);
    const startTime = Date.now();

    try {
      // 强制刷新时跳过缓存
      if (forceRefresh) {
        const data = await fetcher();
        await this.set(fullKey, data, { ttl, compress });
        return data;
      }

      // 尝试从缓存获取
      const cached = await this.getFromCache(fullKey, compress);
      if (cached !== null) {
        this.ctx.cacheStats.hits++;
        this.recordCacheHit(fullKey, Date.now() - startTime);
        return cached;
      }

      // 缓存未命中，获取数据
      this.ctx.cacheStats.misses++;
      const data = await fetcher();

      // 异步缓存数据
      setImmediate(() => {
        this.set(fullKey, data, { ttl, compress });
      });

      this.recordCacheMiss(fullKey, Date.now() - startTime);
      return data;

    } catch (error) {
      this.ctx.logger.error(`Cache get error for key ${fullKey}:`, error);
      // 缓存失败时直接返回数据
      return await fetcher();
    }
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} data - 数据
   * @param {Object} options - 选项
   */
  async set(key, data, options = {}) {
    const { ttl = 300, compress = false } = options;

    try {
      let serializedData = JSON.stringify(data);

      // 数据压缩
      if (compress && serializedData.length > this.config.compression.threshold) {
        serializedData = await this.compressData(serializedData);
        key = `${key}:compressed`;
      }

      await this.redis.setex(key, ttl, serializedData);
      this.ctx.cacheStats.sets++;

    } catch (error) {
      this.ctx.logger.error(`Cache set error for key ${key}:`, error);
    }
  }

  /**
   * 从缓存获取数据
   * @param {string} key - 缓存键
   * @param {boolean} compressed - 是否压缩
   * @return {Promise} 缓存数据
   */
  async getFromCache(key, compressed) {
    try {
      // 先尝试获取压缩版本
      if (compressed) {
        const compressedData = await this.redis.get(`${key}:compressed`);
        if (compressedData) {
          const decompressed = await this.decompressData(compressedData);
          return JSON.parse(decompressed);
        }
      }

      // 获取未压缩版本
      const data = await this.redis.get(key);
      return data ? JSON.parse(data) : null;

    } catch (error) {
      this.ctx.logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string|Array} keys - 缓存键
   */
  async delete(keys) {
    try {
      const keysArray = Array.isArray(keys) ? keys : [ keys ];
      const fullKeys = keysArray.map(key => this.buildKey(key));

      if (fullKeys.length > 0) {
        await this.redis.del(...fullKeys);
        this.ctx.cacheStats.deletes += fullKeys.length;
      }

    } catch (error) {
      this.ctx.logger.error('Cache delete error:', error);
    }
  }

  /**
   * 模式匹配删除
   * @param {string} pattern - 匹配模式
   */
  async deleteByPattern(pattern) {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        this.ctx.cacheStats.deletes += keys.length;
        this.ctx.logger.info(`Deleted ${keys.length} cache keys matching pattern: ${pattern}`);
      }
    } catch (error) {
      this.ctx.logger.error(`Cache pattern delete error for ${pattern}:`, error);
    }
  }

  /**
   * 缓存预热
   * @param {Array} warmupTasks - 预热任务
   */
  async warmup(warmupTasks) {
    if (!this.config.warmup.enabled) { return; }

    this.ctx.logger.info(`Starting cache warmup with ${warmupTasks.length} tasks`);

    const results = await Promise.allSettled(
      warmupTasks.map(async task => {
        const { key, fetcher, options = {} } = task;
        return await this.get(key, fetcher, { ...options, forceRefresh: true });
      })
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    this.ctx.logger.info(`Cache warmup completed: ${successful}/${warmupTasks.length} successful`);

    return results;
  }

  /**
   * 构建缓存键
   * @param {string} key - 原始键
   * @param {string} category - 分类
   * @return {string} 完整键
   */
  buildKey(key, category = 'default') {
    const prefix = `cache:${category}:`;
    return `${prefix}${key}`;
  }

  /**
   * 数据压缩
   * @param {string} data - 原始数据
   * @return {Promise<string>} 压缩后数据
   */
  async compressData(data) {
    const zlib = require('zlib');
    const { promisify } = require('util');
    const gzip = promisify(zlib.gzip);

    const compressed = await gzip(Buffer.from(data));
    return compressed.toString('base64');
  }

  /**
   * 数据解压缩
   * @param {string} compressedData - 压缩数据
   * @return {Promise<string>} 解压后数据
   */
  async decompressData(compressedData) {
    const zlib = require('zlib');
    const { promisify } = require('util');
    const gunzip = promisify(zlib.gunzip);

    const buffer = Buffer.from(compressedData, 'base64');
    const decompressed = await gunzip(buffer);
    return decompressed.toString();
  }

  /**
   * 记录缓存命中
   * @param {string} key - 缓存键
   * @param {number} time - 耗时
   */
  recordCacheHit(key, time) {
    this.app.emit('cache-hit', {
      key,
      time,
      timestamp: new Date(),
      path: this.ctx.path,
    });
  }

  /**
   * 记录缓存未命中
   * @param {string} key - 缓存键
   * @param {number} time - 耗时
   */
  recordCacheMiss(key, time) {
    this.app.emit('cache-miss', {
      key,
      time,
      timestamp: new Date(),
      path: this.ctx.path,
    });
  }

  /**
   * 获取缓存统计
   * @return {Promise<Object>} 统计信息
   */
  async getStats() {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');

      return {
        memory: this.parseRedisInfo(info),
        keyspace: this.parseRedisInfo(keyspace),
        stats: this.ctx.cacheStats,
        timestamp: new Date(),
      };
    } catch (error) {
      this.ctx.logger.error('Failed to get cache stats:', error);
      return { error: error.message };
    }
  }

  /**
   * 解析Redis信息
   * @param {string} info - Redis info输出
   * @return {Object} 解析后的信息
   */
  parseRedisInfo(info) {
    const result = {};
    const lines = info.split('\r\n');

    for (const line of lines) {
      if (line.includes(':')) {
        const [ key, value ] = line.split(':');
        result[key] = isNaN(value) ? value : Number(value);
      }
    }

    return result;
  }

  /**
   * 健康检查
   * @return {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const start = Date.now();
      await this.redis.ping();
      const latency = Date.now() - start;

      const stats = await this.getStats();

      return {
        status: 'healthy',
        latency,
        memory: stats.memory,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date(),
      };
    }
  }
}
