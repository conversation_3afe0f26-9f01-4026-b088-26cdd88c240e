'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 文章公开访问控制中间件
 * 确保只有已发布的文章可以被公开访问
 *
 * 功能：
 * 1. 检查指定文章是否为已发布状态
 * 2. 强制设置查询参数status为published
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function postOpen(ctx, next) {
    const postId = ctx.params.id;

    // 如果有指定文章ID，检查文章状态
    if (postId && postId.length > 0) {
      try {
        const post = await ctx.model.Post.findByPk(postId);

        // 文章不存在
        if (!post) {
          unifiedResponse.setResponse(ctx, null, '文章不存在', 404);
          return;
        }

        // 文章未发布
        if (post.status !== 'published') {
          unifiedResponse.setResponse(ctx, null, '该文章尚未发布，无法访问', 403);
          return;
        }
      } catch (error) {
        // 数据库查询错误
        console.error('Post access check error:', error);
        unifiedResponse.setResponse(ctx, null, '文章访问检查失败', 500);
        return;
      }
    }

    // 强制设置查询状态为已发布（确保列表查询只返回已发布文章）
    if (ctx.request.query) {
      ctx.request.query.status = 'published';
    }

    await next();
  };
};
