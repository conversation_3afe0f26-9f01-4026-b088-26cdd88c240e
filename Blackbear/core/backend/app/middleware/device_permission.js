'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 设备权限验证中间件
 * 验证设备是否有权限访问特定API
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function devicePermission(ctx, next) {
    // 只对特定的API路径进行设备权限检查
    if (!/^\/api\/web\/message/.test(ctx.request.url)) {
      await next();
      return;
    }

    // 获取客户端设备ID
    const clientId = ctx.request.headers.clientid;
    if (!clientId) {
      unifiedResponse.setResponse(ctx, 'no permission', '缺少设备标识', 403);
      return;
    }

    try {
      const device = await ctx.model.Device.findOne({
        where: { clientId },
      });

      // 检查设备是否存在且激活
      if (!device) {
        unifiedResponse.setResponse(ctx, 'no permission', '设备未注册', 403);
        return;
      }

      if (!device.active) {
        unifiedResponse.setResponse(ctx, 'no permission', '当前设备已经被禁用！', 403);
        return;
      }

      // 设备验证通过，继续执行
      await next();
    } catch (error) {
      // 数据库查询错误
      console.error('Device permission check error:', error);
      unifiedResponse.setResponse(ctx, 'no permission', '设备权限验证失败', 500);
    }
  };
};
