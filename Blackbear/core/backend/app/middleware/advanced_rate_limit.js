'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 高级限流中间件
 * 提供基于IP、用户、API端点的多维度限流功能
 * @param options
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  const defaultOptions = {
    // 时间窗口（毫秒）
    windowMs: 15 * 60 * 1000, // 15分钟
    // 默认最大请求数
    max: 100,
    // 是否跳过成功请求
    skipSuccessfulRequests: false,
    // 是否跳过失败请求
    skipFailedRequests: false,
    // Redis键前缀
    keyPrefix: 'rate_limit:',
    // 自定义键生成器
    keyGenerator: null,
    // 跳过限流的条件
    skip: null,
    // 不同端点的限流配置
    endpointLimits: {
      '/api/auth/login': { max: 5, windowMs: 15 * 60 * 1000 }, // 登录限制更严格
      '/api/auth/register': { max: 3, windowMs: 60 * 60 * 1000 }, // 注册限制
      '/api/upload': { max: 10, windowMs: 60 * 1000 }, // 上传限制
      '/api/search': { max: 50, windowMs: 60 * 1000 }, // 搜索限制
    },
    // 用户级别限流
    userLimits: {
      admin: { max: 1000, windowMs: 60 * 1000 },
      vip: { max: 500, windowMs: 60 * 1000 },
      normal: { max: 100, windowMs: 60 * 1000 },
    },
  };

  const config = { ...defaultOptions, ...options };

  return async function advancedRateLimit(ctx, next) {
    try {
      // 检查是否跳过限流
      if (config.skip && await config.skip(ctx)) {
        await next();
        return;
      }

      // 健康检查接口跳过限流
      if (ctx.path.includes('/health') || ctx.path.includes('/ping')) {
        await next();
        return;
      }

      // 获取限流配置
      const limitConfig = getLimitConfig(ctx, config);

      // 生成限流键
      const key = generateRateLimitKey(ctx, config);

      // 检查限流
      const rateLimitResult = await checkRateLimit(ctx, key, limitConfig);

      if (rateLimitResult.exceeded) {
        // 设置响应头
        ctx.set('X-RateLimit-Limit', limitConfig.max);
        ctx.set('X-RateLimit-Remaining', Math.max(0, limitConfig.max - rateLimitResult.current));
        ctx.set('X-RateLimit-Reset', new Date(Date.now() + limitConfig.windowMs).toISOString());

        // 记录限流日志
        ctx.logger.warn('Rate limit exceeded:', {
          ip: ctx.ip,
          userId: ctx.userInfo?.id,
          path: ctx.path,
          method: ctx.method,
          key,
          current: rateLimitResult.current,
          limit: limitConfig.max,
        });

        ctx.body = unifiedResponse.error('请求过于频繁，请稍后再试', 429, {
          retryAfter: Math.ceil(limitConfig.windowMs / 1000),
        });
        ctx.status = 429;
        return;
      }

      // 设置响应头
      ctx.set('X-RateLimit-Limit', limitConfig.max);
      ctx.set('X-RateLimit-Remaining', Math.max(0, limitConfig.max - rateLimitResult.current));
      ctx.set('X-RateLimit-Reset', new Date(Date.now() + limitConfig.windowMs).toISOString());

      await next();

      // 根据响应状态决定是否计入限流
      const shouldCount = shouldCountRequest(ctx, config);
      if (!shouldCount) {
        // 如果不应该计入，则减少计数
        await decrementRateLimit(ctx, key);
      }

    } catch (error) {
      ctx.logger.error('Rate limit middleware error:', error);
      // 限流中间件出错时不阻止请求
      await next();
    }
  };
};

/**
 * 获取限流配置
 * @param {Object} ctx - Koa上下文
 * @param {Object} config - 配置选项
 * @return {Object} 限流配置
 */
function getLimitConfig(ctx, config) {
  // 优先级：端点配置 > 用户级别配置 > 默认配置

  // 检查端点特定配置
  const endpointConfig = config.endpointLimits[ctx.path];
  if (endpointConfig) {
    return { ...config, ...endpointConfig };
  }

  // 检查用户级别配置
  if (ctx.userInfo?.role) {
    const userConfig = config.userLimits[ctx.userInfo.role];
    if (userConfig) {
      return { ...config, ...userConfig };
    }
  }

  return config;
}

/**
 * 生成限流键
 * @param {Object} ctx - Koa上下文
 * @param {Object} config - 配置选项
 * @return {string} 限流键
 */
function generateRateLimitKey(ctx, config) {
  if (config.keyGenerator) {
    return config.keyGenerator(ctx);
  }

  const parts = [ config.keyPrefix ];

  // 添加IP地址
  parts.push('ip', ctx.ip);

  // 如果有用户信息，添加用户ID
  if (ctx.userInfo?.id) {
    parts.push('user', ctx.userInfo.id);
  }

  // 添加路径（用于端点级别限流）
  parts.push('path', ctx.path.replace(/[^a-zA-Z0-9]/g, '_'));

  return parts.join(':');
}

/**
 * 检查限流
 * @param {Object} ctx - Koa上下文
 * @param {string} key - 限流键
 * @param {Object} config - 限流配置
 * @return {Object} 检查结果
 */
async function checkRateLimit(ctx, key, config) {
  const { redis } = ctx.app;
  const { windowMs } = config;
  const { max } = config;

  try {
    // 使用Redis的滑动窗口算法
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const windowKey = `${key}:${window}`;

    // 原子操作：增加计数并设置过期时间
    const pipeline = redis.pipeline();
    pipeline.incr(windowKey);
    pipeline.expire(windowKey, Math.ceil(windowMs / 1000));

    const results = await pipeline.exec();
    const current = results[0][1];

    return {
      exceeded: current > max,
      current,
      remaining: Math.max(0, max - current),
      resetTime: (window + 1) * windowMs,
    };

  } catch (error) {
    ctx.logger.error('Rate limit check error:', error);
    // Redis错误时不限流
    return {
      exceeded: false,
      current: 0,
      remaining: config.max,
      resetTime: Date.now() + config.windowMs,
    };
  }
}

/**
 * 减少限流计数
 * @param {Object} ctx - Koa上下文
 * @param {string} key - 限流键
 */
async function decrementRateLimit(ctx, key) {
  try {
    const { redis } = ctx.app;
    const now = Date.now();
    const window = Math.floor(now / 15 * 60 * 1000); // 使用默认窗口
    const windowKey = `${key}:${window}`;

    const current = await redis.get(windowKey);
    if (current && parseInt(current) > 0) {
      await redis.decr(windowKey);
    }
  } catch (error) {
    ctx.logger.error('Rate limit decrement error:', error);
  }
}

/**
 * 判断是否应该计入限流
 * @param {Object} ctx - Koa上下文
 * @param {Object} config - 配置选项
 * @return {boolean} 是否计入
 */
function shouldCountRequest(ctx, config) {
  const { status } = ctx;

  // 跳过成功请求
  if (config.skipSuccessfulRequests && status >= 200 && status < 400) {
    return false;
  }

  // 跳过失败请求
  if (config.skipFailedRequests && status >= 400) {
    return false;
  }

  return true;
}
