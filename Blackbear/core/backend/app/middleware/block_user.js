'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 用户屏蔽检查中间件
 * 检查用户是否被管理员屏蔽
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function blockUser(ctx, next) {
    // 登出操作跳过检查
    if (ctx.path === '/logout') {
      await next();
      return;
    }

    const user = ctx.state?.user;

    // 检查用户是否被屏蔽
    if (user?.isBlock) {
      unifiedResponse.setResponse(ctx, null, '您已被管理员屏蔽了。有疑问请联系管理员', 403);
      return;
    }

    await next();
  };
};
