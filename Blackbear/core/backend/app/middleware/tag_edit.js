'use strict';

const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 标签编辑权限验证中间件
 * 验证用户是否有权限修改标签/题材
 *
 * 权限规则：
 * 1. 管理员有修改所有标签的权限
 * 2. 普通用户只能修改分配给自己的标签
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function tagEdit(ctx, next) {
    const { method, helper } = ctx;
    const { user } = ctx.state;

    // GET请求或管理员跳过权限检查
    if (method === 'GET' || helper.isAdmin()) {
      await next();
      return;
    }

    // 检查用户是否已登录
    if (!user?.id) {
      unifiedResponse.setResponse(ctx, null, '需要登录才能修改标签', 401);
      return;
    }

    const tagId = ctx.params.id;
    if (tagId && tagId.length > 0) {
      try {
        // 查询用户是否有权限修改该标签
        const tagUser = await ctx.model.TagUser.findOne({
          where: {
            tagId,
            userId: user.id,
          },
        });

        if (!tagUser) {
          unifiedResponse.setResponse(ctx, null, '您没有权限修改该题材！', 403);
          return;
        }
      } catch (error) {
        // 数据库查询错误
        console.error('Tag permission check error:', error);
        unifiedResponse.setResponse(ctx, null, '权限验证失败', 500);
        return;
      }
    }

    await next();
  };
};
