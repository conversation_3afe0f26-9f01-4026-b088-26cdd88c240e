'use strict';

const SecurityValidator = require('../utils/security-validator');
const { unifiedResponse } = require('../utils/unified-response-system');

/**
 * 安全过滤中间件
 * 提供输入验证、XSS防护、SQL注入防护等安全功能
 * @param options
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  const defaultOptions = {
    // 是否启用XSS过滤
    enableXssFilter: true,
    // 是否启用SQL注入防护
    enableSqlInjectionFilter: true,
    // 是否启用文件名过滤
    enableFilenameFilter: true,
    // 是否启用URL验证
    enableUrlValidation: true,
    // 最大请求体大小（字节）
    maxBodySize: 10 * 1024 * 1024, // 10MB
    // 需要过滤的字段
    filterFields: [ 'content', 'description', 'comment', 'message', 'text' ],
    // 跳过过滤的路径
    skipPaths: [ '/api/upload', '/api/file' ],
  };

  const config = { ...defaultOptions, ...options };

  return async function securityFilter(ctx, next) {
    try {
      // 检查请求体大小
      if (ctx.request.length > config.maxBodySize) {
        ctx.body = unifiedResponse.error('请求体过大', 413);
        ctx.status = 413;
        return;
      }

      // 检查是否跳过过滤
      if (config.skipPaths.some(path => ctx.path.startsWith(path))) {
        await next();
        return;
      }

      // 验证请求头
      if (!validateHeaders(ctx)) {
        ctx.body = unifiedResponse.error('无效的请求头', 400);
        ctx.status = 400;
        return;
      }

      // 过滤请求参数
      if (ctx.request.body) {
        ctx.request.body = filterRequestData(ctx.request.body, config);
      }

      if (ctx.query) {
        ctx.query = filterRequestData(ctx.query, config);
      }

      // 验证文件上传
      if (ctx.request.files) {
        const fileValidation = validateUploadedFiles(ctx.request.files);
        if (!fileValidation.isValid) {
          ctx.body = unifiedResponse.error(fileValidation.message, 400);
          ctx.status = 400;
          return;
        }
      }

      await next();

    } catch (error) {
      ctx.logger.error('Security filter error:', error);
      ctx.body = unifiedResponse.error('安全过滤器错误', 500);
      ctx.status = 500;
    }
  };
};

/**
 * 验证请求头
 * @param {Object} ctx - Koa上下文
 * @return {boolean} 是否有效
 */
function validateHeaders(ctx) {
  const userAgent = ctx.get('user-agent');
  const contentType = ctx.get('content-type');

  // 检查User-Agent
  if (!userAgent || userAgent.length > 500) {
    return false;
  }

  // 检查可疑的User-Agent
  const suspiciousPatterns = [
    /sqlmap/i,
    /nikto/i,
    /nessus/i,
    /burp/i,
    /nmap/i,
    /<script/i,
  ];

  if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
    return false;
  }

  // 检查Content-Type
  if (contentType && contentType.includes('..')) {
    return false;
  }

  return true;
}

/**
 * 过滤请求数据
 * @param {Object} data - 请求数据
 * @param {Object} config - 配置选项
 * @return {Object} 过滤后的数据
 */
function filterRequestData(data, config) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const filtered = Array.isArray(data) ? [] : {};

  for (const [ key, value ] of Object.entries(data)) {
    if (typeof value === 'string') {
      let filteredValue = value;

      // XSS过滤
      if (config.enableXssFilter && config.filterFields.includes(key)) {
        filteredValue = SecurityValidator.sanitizeHtmlInput(filteredValue);
      }

      // SQL注入防护
      if (config.enableSqlInjectionFilter) {
        filteredValue = SecurityValidator.sanitizeSqlInput(filteredValue);
      }

      // 文件名过滤
      if (config.enableFilenameFilter && key.toLowerCase().includes('filename')) {
        filteredValue = SecurityValidator.sanitizeFilename(filteredValue);
      }

      // URL验证
      if (config.enableUrlValidation && key.toLowerCase().includes('url')) {
        if (filteredValue && !SecurityValidator.isValidUrl(filteredValue)) {
          // 如果URL无效，记录日志但不阻止请求
          console.warn(`Invalid URL detected: ${key} = ${filteredValue}`);
        }
      }

      filtered[key] = filteredValue;

    } else if (typeof value === 'object' && value !== null) {
      // 递归过滤嵌套对象
      filtered[key] = filterRequestData(value, config);
    } else {
      filtered[key] = value;
    }
  }

  return filtered;
}

/**
 * 验证上传的文件
 * @param {Object} files - 上传的文件
 * @return {Object} 验证结果
 */
function validateUploadedFiles(files) {
  const result = {
    isValid: true,
    message: '',
  };

  // 危险文件扩展名
  const dangerousExtensions = [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.php', '.asp', '.aspx', '.jsp', '.sh', '.py', '.rb', '.pl',
  ];

  // 允许的文件类型
  const allowedMimeTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'text/plain', 'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];

  for (const file of Object.values(files)) {
    const filename = file.filename || file.name || '';
    const mimetype = file.mimetype || file.type || '';

    // 检查文件名
    if (!filename) {
      result.isValid = false;
      result.message = '文件名不能为空';
      break;
    }

    // 检查危险扩展名
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    if (dangerousExtensions.includes(extension)) {
      result.isValid = false;
      result.message = `不允许上传 ${extension} 类型的文件`;
      break;
    }

    // 检查MIME类型
    if (mimetype && !allowedMimeTypes.includes(mimetype)) {
      result.isValid = false;
      result.message = `不支持的文件类型: ${mimetype}`;
      break;
    }

    // 检查文件大小（如果有的话）
    if (file.size && file.size > 50 * 1024 * 1024) { // 50MB
      result.isValid = false;
      result.message = '文件大小不能超过50MB';
      break;
    }

    // 检查文件名中的危险字符
    if (/[<>:"/\\|?*\x00-\x1f]/.test(filename)) {
      result.isValid = false;
      result.message = '文件名包含非法字符';
      break;
    }
  }

  return result;
}
