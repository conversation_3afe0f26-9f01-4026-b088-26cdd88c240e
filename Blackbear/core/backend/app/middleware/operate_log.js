'use strict';

/**
 * 操作日志记录中间件
 * 在生产环境中记录非GET请求的操作日志
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  return async function operateLog(ctx, next) {
    try {
      await next();

      // 只在生产环境记录非GET请求的操作日志
      const shouldLog = ctx.app.config.env === 'prod' &&
                       ctx.request.method.toUpperCase() !== 'GET' &&
                       ctx.userInfo;

      if (shouldLog) {
        try {
          const logData = {
            userId: ctx.userInfo.id,
            username: ctx.userInfo.username,
            method: ctx.request.method,
            url: ctx.request.url.split('?')[0], // 移除查询参数
            ip: ctx.request.ip,
            status: ctx.response.status,
            taskId: ctx.params.id || '',
            params: JSON.stringify(ctx.request.body || {}),
            timestamp: new Date(),
          };

          // 异步记录日志，不阻塞响应
          ctx.service.operate.createLog(logData).catch(error => {
            console.error('Failed to create operate log:', error);
          });
        } catch (logError) {
          console.error('Operate log middleware error:', logError);
        }
      }
    } catch (error) {
      // 重新抛出错误，让全局错误处理中间件处理
      throw error;
    }
  };
};
