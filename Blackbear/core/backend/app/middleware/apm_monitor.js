'use strict';

const { v4: uuidv4 } = require('uuid');

/**
 * APM性能监控中间件
 * 提供请求追踪、性能指标收集、链路追踪等功能
 * @param options
 */
/**
 * Middleware function
 * @param {Object} options - Configuration options
 * @return {Function} Middleware function
 */
module.exports = (options = {}) => {
  const defaultOptions = {
    // 性能阈值配置
    thresholds: {
      slow: 1000, // 慢请求阈值（毫秒）
      critical: 5000, // 严重慢请求阈值（毫秒）
      memory: 100 * 1024 * 1024, // 内存使用阈值（100MB）
    },
    // 采样配置
    sampling: {
      enabled: true,
      rate: 0.1, // 10% 采样率
      slowRequestRate: 1.0, // 慢请求100%采样
    },
    // 指标收集配置
    metrics: {
      enabled: true,
      collectInterval: 30000, // 30秒收集一次
      retentionDays: 7, // 保留7天数据
    },
    // 链路追踪配置
    tracing: {
      enabled: true,
      maxSpans: 100, // 最大span数量
      includeHeaders: [ 'user-agent', 'x-forwarded-for' ],
    },
    // 错误监控配置
    errorTracking: {
      enabled: true,
      captureStackTrace: true,
      maxStackFrames: 50,
    },
  };

  const config = { ...defaultOptions, ...options };
  const performanceCollector = new PerformanceCollector(config);

  return async function apmMonitor(ctx, next) {
    const traceId = ctx.get('x-trace-id') || uuidv4();
    const spanId = uuidv4();
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    // 设置追踪信息
    ctx.traceId = traceId;
    ctx.spanId = spanId;
    ctx.set('x-trace-id', traceId);
    ctx.set('x-span-id', spanId);

    // 创建性能上下文
    ctx.performance = {
      traceId,
      spanId,
      startTime,
      startMemory,
      spans: [],
      metrics: {},
    };

    // 添加性能监控方法到上下文
    ctx.startSpan = (name, tags = {}) => performanceCollector.startSpan(ctx, name, tags);
    ctx.finishSpan = (spanId, tags = {}) => performanceCollector.finishSpan(ctx, spanId, tags);
    ctx.addMetric = (name, value, tags = {}) => performanceCollector.addMetric(ctx, name, value, tags);

    try {
      // 开始根span
      const rootSpan = ctx.startSpan('http_request', {
        'http.method': ctx.method,
        'http.url': ctx.url,
        'http.user_agent': ctx.get('user-agent'),
        'user.id': ctx.userInfo?.id,
      });

      await next();

      // 完成根span
      ctx.finishSpan(rootSpan.id, {
        'http.status_code': ctx.status,
        'http.response_size': ctx.length || 0,
      });

      // 收集性能指标
      await performanceCollector.collectRequestMetrics(ctx, startTime, startMemory);

    } catch (error) {
      // 记录错误信息
      await performanceCollector.recordError(ctx, error, startTime);
      throw error;

    } finally {
      const duration = Date.now() - startTime;

      // 判断是否需要采样
      const shouldSample = performanceCollector.shouldSample(ctx, duration, config);

      if (shouldSample) {
        // 发送性能数据
        setImmediate(() => {
          performanceCollector.sendPerformanceData(ctx, duration);
        });
      }

      // 检查性能阈值
      performanceCollector.checkThresholds(ctx, duration, config);
    }
  };
};

/**
 * 性能数据收集器
 */
class PerformanceCollector {
  constructor(config) {
    this.config = config;
    this.activeSpans = new Map();
    this.metricsBuffer = [];
    this.errorBuffer = [];

    // 启动定期指标收集
    if (config.metrics.enabled) {
      this.startMetricsCollection();
    }
  }

  /**
   * 开始一个新的span
   * @param {Object} ctx - Koa上下文
   * @param {string} name - span名称
   * @param {Object} tags - 标签
   * @return {Object} span对象
   */
  startSpan(ctx, name, tags = {}) {
    const span = {
      id: uuidv4(),
      traceId: ctx.traceId,
      parentId: ctx.spanId,
      name,
      startTime: Date.now(),
      tags: { ...tags },
      logs: [],
    };

    this.activeSpans.set(span.id, span);
    ctx.performance.spans.push(span);

    return span;
  }

  /**
   * 完成一个span
   * @param {Object} ctx - Koa上下文
   * @param {string} spanId - span ID
   * @param {Object} tags - 额外标签
   */
  finishSpan(ctx, spanId, tags = {}) {
    const span = this.activeSpans.get(spanId);
    if (!span) { return; }

    span.duration = Date.now() - span.startTime;
    span.tags = { ...span.tags, ...tags };
    span.finishTime = Date.now();

    this.activeSpans.delete(spanId);
  }

  /**
   * 添加指标
   * @param {Object} ctx - Koa上下文
   * @param {string} name - 指标名称
   * @param {number} value - 指标值
   * @param {Object} tags - 标签
   */
  addMetric(ctx, name, value, tags = {}) {
    ctx.performance.metrics[name] = {
      value,
      tags,
      timestamp: Date.now(),
    };
  }

  /**
   * 收集请求指标
   * @param {Object} ctx - Koa上下文
   * @param {number} startTime - 开始时间
   * @param {Object} startMemory - 开始内存
   */
  async collectRequestMetrics(ctx, startTime, startMemory) {
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;

    // 基础指标
    ctx.addMetric('request.duration', duration, {
      method: ctx.method,
      status: ctx.status,
      path: this.normalizePath(ctx.path),
    });

    ctx.addMetric('request.memory_delta', endMemory.heapUsed - startMemory.heapUsed, {
      method: ctx.method,
      path: this.normalizePath(ctx.path),
    });

    // 响应大小
    if (ctx.length) {
      ctx.addMetric('response.size', ctx.length, {
        method: ctx.method,
        status: ctx.status,
      });
    }

    // 数据库查询指标（如果有）
    if (ctx.dbQueries) {
      ctx.addMetric('db.query_count', ctx.dbQueries.length);
      const totalDbTime = ctx.dbQueries.reduce((sum, q) => sum + q.duration, 0);
      ctx.addMetric('db.total_time', totalDbTime);
    }

    // 缓存指标（如果有）
    if (ctx.cacheStats) {
      ctx.addMetric('cache.hits', ctx.cacheStats.hits);
      ctx.addMetric('cache.misses', ctx.cacheStats.misses);
      ctx.addMetric('cache.hit_rate',
        ctx.cacheStats.hits / (ctx.cacheStats.hits + ctx.cacheStats.misses) * 100
      );
    }
  }

  /**
   * 记录错误信息
   * @param {Object} ctx - Koa上下文
   * @param {Error} error - 错误对象
   * @param {number} startTime - 开始时间
   */
  async recordError(ctx, error, startTime) {
    if (!this.config.errorTracking.enabled) { return; }

    const errorData = {
      traceId: ctx.traceId,
      spanId: ctx.spanId,
      timestamp: new Date(),
      duration: Date.now() - startTime,
      error: {
        name: error.name,
        message: error.message,
        stack: this.config.errorTracking.captureStackTrace ?
          this.truncateStack(error.stack) : undefined,
      },
      request: {
        method: ctx.method,
        url: ctx.url,
        headers: this.filterHeaders(ctx.headers),
        userAgent: ctx.get('user-agent'),
        ip: ctx.ip,
      },
      user: {
        id: ctx.userInfo?.id,
        role: ctx.userInfo?.role,
      },
    };

    this.errorBuffer.push(errorData);

    // 立即发送严重错误
    if (this.isCriticalError(error)) {
      setImmediate(() => {
        this.sendErrorData([ errorData ]);
      });
    }
  }

  /**
   * 判断是否需要采样
   * @param {Object} ctx - Koa上下文
   * @param {number} duration - 请求时长
   * @param {Object} config - 配置
   * @return {boolean} 是否采样
   */
  shouldSample(ctx, duration, config) {
    if (!config.sampling.enabled) { return false; }

    // 慢请求总是采样
    if (duration > config.thresholds.slow) {
      return Math.random() < config.sampling.slowRequestRate;
    }

    // 错误请求总是采样
    if (ctx.status >= 400) {
      return true;
    }

    // 正常请求按配置采样
    return Math.random() < config.sampling.rate;
  }

  /**
   * 检查性能阈值
   * @param {Object} ctx - Koa上下文
   * @param {number} duration - 请求时长
   * @param {Object} config - 配置
   */
  checkThresholds(ctx, duration, config) {
    // 检查慢请求
    if (duration > config.thresholds.critical) {
      ctx.logger.error(`Critical slow request: ${duration}ms for ${ctx.method} ${ctx.path}`);
      this.sendAlert('critical_slow_request', { duration, path: ctx.path, traceId: ctx.traceId });
    } else if (duration > config.thresholds.slow) {
      ctx.logger.warn(`Slow request: ${duration}ms for ${ctx.method} ${ctx.path}`);
    }

    // 检查内存使用
    const memoryUsage = process.memoryUsage();
    if (memoryUsage.heapUsed > config.thresholds.memory) {
      ctx.logger.warn(`High memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);
      this.sendAlert('high_memory_usage', { memoryUsage: memoryUsage.heapUsed });
    }
  }

  /**
   * 发送性能数据
   * @param {Object} ctx - Koa上下文
   * @param {number} duration - 请求时长
   */
  async sendPerformanceData(ctx, duration) {
    try {
      const performanceData = {
        traceId: ctx.traceId,
        timestamp: new Date(),
        duration,
        request: {
          method: ctx.method,
          path: this.normalizePath(ctx.path),
          status: ctx.status,
          userAgent: ctx.get('user-agent'),
          ip: ctx.ip,
        },
        spans: ctx.performance.spans,
        metrics: ctx.performance.metrics,
        user: {
          id: ctx.userInfo?.id,
          role: ctx.userInfo?.role,
        },
      };

      // 发送到APM系统（这里可以集成具体的APM服务）
      ctx.app.emit('apm-data', performanceData);

    } catch (error) {
      ctx.logger.error('Failed to send performance data:', error);
    }
  }

  /**
   * 发送错误数据
   * @param {Array} errors - 错误数组
   */
  async sendErrorData(errors) {
    try {
      // 发送到错误监控系统
      this.app?.emit('error-tracking', errors);
    } catch (error) {
      console.error('Failed to send error data:', error);
    }
  }

  /**
   * 发送告警
   * @param {string} type - 告警类型
   * @param {Object} data - 告警数据
   */
  sendAlert(type, data) {
    // 发送到告警系统
    this.app?.emit('alert', { type, data, timestamp: new Date() });
  }

  /**
   * 启动指标收集
   */
  startMetricsCollection() {
    setInterval(() => {
      this.collectSystemMetrics();
      this.flushBuffers();
    }, this.config.metrics.collectInterval);
  }

  /**
   * 收集系统指标
   */
  collectSystemMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    this.metricsBuffer.push({
      timestamp: new Date(),
      type: 'system',
      metrics: {
        memory: memoryUsage,
        cpu: cpuUsage,
        uptime: process.uptime(),
      },
    });
  }

  /**
   * 刷新缓冲区
   */
  async flushBuffers() {
    if (this.metricsBuffer.length > 0) {
      const metrics = [ ...this.metricsBuffer ];
      this.metricsBuffer = [];
      // 发送指标数据
      this.app?.emit('metrics-batch', metrics);
    }

    if (this.errorBuffer.length > 0) {
      const errors = [ ...this.errorBuffer ];
      this.errorBuffer = [];
      await this.sendErrorData(errors);
    }
  }

  /**
   * 工具方法
   * @param path
   */
  normalizePath(path) {
    // 将动态路径参数标准化，如 /api/users/123 -> /api/users/:id
    return path.replace(/\/\d+/g, '/:id').replace(/\/[a-f0-9-]{36}/g, '/:uuid');
  }

  filterHeaders(headers) {
    const filtered = { ...headers };
    // 移除敏感头信息
    delete filtered.authorization;
    delete filtered.cookie;
    delete filtered['x-api-key'];
    return filtered;
  }

  truncateStack(stack) {
    if (!stack) { return undefined; }
    const lines = stack.split('\n');
    return lines.slice(0, this.config.errorTracking.maxStackFrames).join('\n');
  }

  isCriticalError(error) {
    // 定义严重错误的判断逻辑
    const criticalErrors = [ 'TypeError', 'ReferenceError', 'SyntaxError' ];
    return criticalErrors.includes(error.name) || error.message.includes('ECONNREFUSED');
  }
}

module.exports.PerformanceCollector = PerformanceCollector;
