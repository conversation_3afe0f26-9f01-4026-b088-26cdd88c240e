'use strict';

const { Controller } = require('egg');

/**
 * CacheController
 * Handles HTTP requests for specific resource
 */
class CacheController extends Controller {

  // 清除所有缓存
  async clearAll() {
    const { ctx } = this;

    try {
      await ctx.service.cacheManager.clearAllCache();
      ctx.body = {
        success: true,
        message: '所有缓存已清除',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear all cache', error);
      ctx.body = {
        success: false,
        message: '清除缓存失败',
        error: error.message,
      };
    }
  }

  // 清除股票相关缓存
  async clearStock() {
    const { ctx } = this;
    const { code, method, type } = ctx.query;

    try {
      await ctx.service.cacheManager.clearStockCache({ code, method, type });
      ctx.body = {
        success: true,
        message: '股票缓存已清除',
        params: { code, method, type },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear stock cache', error);
      ctx.body = {
        success: false,
        message: '清除股票缓存失败',
        error: error.message,
      };
    }
  }

  // 清除新闻相关缓存
  async clearNews() {
    const { ctx } = this;
    const { newsId, code } = ctx.query;

    try {
      await ctx.service.cacheManager.clearNewsCache({ newsId, code });
      ctx.body = {
        success: true,
        message: '新闻缓存已清除',
        params: { newsId, code },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear news cache', error);
      ctx.body = {
        success: false,
        message: '清除新闻缓存失败',
        error: error.message,
      };
    }
  }

  // 清除报告相关缓存
  async clearReport() {
    const { ctx } = this;
    const { announcementId, code } = ctx.query;

    try {
      await ctx.service.cacheManager.clearReportCache({ announcementId, code });
      ctx.body = {
        success: true,
        message: '报告缓存已清除',
        params: { announcementId, code },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear report cache', error);
      ctx.body = {
        success: false,
        message: '清除报告缓存失败',
        error: error.message,
      };
    }
  }

  // 清除研究相关缓存
  async clearResearch() {
    const { ctx } = this;
    const { researchId, code } = ctx.query;

    try {
      await ctx.service.cacheManager.clearResearchCache({ researchId, code });
      ctx.body = {
        success: true,
        message: '研究缓存已清除',
        params: { researchId, code },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear research cache', error);
      ctx.body = {
        success: false,
        message: '清除研究缓存失败',
        error: error.message,
      };
    }
  }

  // 清除行业相关缓存
  async clearIndustry() {
    const { ctx } = this;

    try {
      await ctx.service.cacheManager.clearIndustryCache();
      ctx.body = {
        success: true,
        message: '行业缓存已清除',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear industry cache', error);
      ctx.body = {
        success: false,
        message: '清除行业缓存失败',
        error: error.message,
      };
    }
  }

  // 清除统计相关缓存
  async clearStatistics() {
    const { ctx } = this;

    try {
      await ctx.service.cacheManager.clearStatisticsCache();
      ctx.body = {
        success: true,
        message: '统计缓存已清除',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear statistics cache', error);
      ctx.body = {
        success: false,
        message: '清除统计缓存失败',
        error: error.message,
      };
    }
  }

  // 清除数据源相关缓存
  async clearSources() {
    const { ctx } = this;
    const { method, code } = ctx.query;

    try {
      await ctx.service.cacheManager.clearSourcesCache({ method, code });
      ctx.body = {
        success: true,
        message: '数据源缓存已清除',
        params: { method, code },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear sources cache', error);
      ctx.body = {
        success: false,
        message: '清除数据源缓存失败',
        error: error.message,
      };
    }
  }

  // 清除标签相关缓存
  async clearTags() {
    const { ctx } = this;
    const { tagId, userId, code, type, method } = ctx.query;

    try {
      await ctx.service.cacheManager.clearTagsCache({ tagId, userId, code, type, method });
      ctx.body = {
        success: true,
        message: '标签缓存已清除',
        params: { tagId, userId, code, type, method },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear tags cache', error);
      ctx.body = {
        success: false,
        message: '清除标签缓存失败',
        error: error.message,
      };
    }
  }

  // 清除分组相关缓存
  async clearGroups() {
    const { ctx } = this;
    const { id, code, type, method } = ctx.query;

    try {
      await ctx.service.cacheManager.clearGroupsCache({ id, code, type, method });
      ctx.body = {
        success: true,
        message: '分组缓存已清除',
        params: { id, code, type, method },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear groups cache', error);
      ctx.body = {
        success: false,
        message: '清除分组缓存失败',
        error: error.message,
      };
    }
  }

  // 清除量化数据相关缓存
  async clearQuants() {
    const { ctx } = this;
    const { method, type, limit } = ctx.query;

    try {
      await ctx.service.cacheManager.clearQuantsCache({ method, type, limit });
      ctx.body = {
        success: true,
        message: '量化数据缓存已清除',
        params: { method, type, limit },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear quants cache', error);
      ctx.body = {
        success: false,
        message: '清除量化数据缓存失败',
        error: error.message,
      };
    }
  }

  // 清除消息相关缓存
  async clearMessages() {
    const { ctx } = this;
    const { userId, type, messageId } = ctx.query;

    try {
      await ctx.service.cacheManager.clearMessagesCache({ userId, type, messageId });
      ctx.body = {
        success: true,
        message: '消息缓存已清除',
        params: { userId, type, messageId },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear messages cache', error);
      ctx.body = {
        success: false,
        message: '清除消息缓存失败',
        error: error.message,
      };
    }
  }

  // 根据数据库操作清除缓存
  async clearByOperation() {
    const { ctx } = this;
    const { table, action, record } = ctx.request.body;

    try {
      await ctx.service.cacheManager.clearCacheByOperation({
        table,
        action,
        record,
      });
      ctx.body = {
        success: true,
        message: '根据操作清除缓存成功',
        operation: { table, action, record },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to clear cache by operation', error);
      ctx.body = {
        success: false,
        message: '根据操作清除缓存失败',
        error: error.message,
      };
    }
  }

  // 获取缓存统计信息
  async getStats() {
    const { ctx } = this;

    try {
      const cacheStats = await ctx.service.cacheManager.getCacheStats();
      const changeStats = await ctx.service.databaseWatcher.getChangeStats();

      ctx.body = {
        success: true,
        data: {
          cache: cacheStats,
          changes: changeStats,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      ctx.logger.error('Failed to get cache stats', error);
      ctx.body = {
        success: false,
        message: '获取缓存统计失败',
        error: error.message,
      };
    }
  }

  // 手动触发数据库变化检查
  async checkChanges() {
    const { ctx } = this;

    try {
      await ctx.service.databaseWatcher.checkForChanges();
      ctx.body = {
        success: true,
        message: '数据库变化检查完成',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to check database changes', error);
      ctx.body = {
        success: false,
        message: '数据库变化检查失败',
        error: error.message,
      };
    }
  }

  // 设置缓存过期时间
  async setExpiration() {
    const { ctx } = this;
    const { pattern, ttl } = ctx.request.body;

    try {
      await ctx.service.cacheManager.setCacheExpiration(pattern, ttl);
      ctx.body = {
        success: true,
        message: '缓存过期时间设置成功',
        params: { pattern, ttl },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Failed to set cache expiration', error);
      ctx.body = {
        success: false,
        message: '设置缓存过期时间失败',
        error: error.message,
      };
    }
  }
}

module.exports = CacheController;
