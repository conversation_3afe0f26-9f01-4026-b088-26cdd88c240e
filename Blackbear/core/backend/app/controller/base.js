'use strict';

const { Controller } = require('egg');
const validationRules = require('../utils/validation-rules');
const { unifiedResponse, CustomErrors } = require('../utils/unified-response-system');

/**
 * BaseController - 统一的Controller基类
 * 提供统一的错误处理、响应格式化、参数验证等通用方法
 * 使用新的统一响应系统
 */
/**
 * BaseController
 * Handles HTTP requests for specific resource
 */
class BaseController extends Controller {
  // ==================== 响应方法 (使用统一响应系统) ====================

  /**
   * 统一的成功响应格式
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 响应代码
   */
  success(data = null, message = '', code = 200) {
    const { ctx } = this;
    ctx.body = unifiedResponse.success(data, message, code);
    ctx.status = 200;
  }

  /**
   * 统一的错误响应格式
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   * @param {*} data - 错误数据
   */
  error(message = '', code = 400, data = null) {
    const { ctx } = this;
    ctx.body = unifiedResponse.error(message, code, data);
    ctx.status = code >= 400 && code < 600 ? code : 400;
  }

  /**
   * 分页响应格式
   * @param {Array} rows - 数据列表
   * @param {number} total - 总数量
   * @param {number} pageIndex - 当前页码
   * @param {number} pageSize - 每页数量
   * @param {string} message - 响应消息
   */
  successWithPagination(rows = [], total = 0, pageIndex = 1, pageSize = 10, message = '查询成功') {
    const { ctx } = this;
    ctx.body = unifiedResponse.pagination(rows, total, pageIndex, pageSize, message);
    ctx.status = 200;
  }

  /**
   * 列表响应格式
   * @param {Array} list - 数据列表
   * @param {string} message - 响应消息
   */
  successWithList(list = [], message = '查询成功') {
    const { ctx } = this;
    ctx.body = unifiedResponse.list(list, message);
    ctx.status = 200;
  }

  /**
   * 详情响应格式
   * @param {*} item - 详情数据
   * @param {string} message - 响应消息
   */
  successWithDetail(item = null, message = '查询成功') {
    const { ctx } = this;
    ctx.body = unifiedResponse.detail(item, message);
    ctx.status = 200;
  }

  /**
   * 操作结果响应格式
   * @param {boolean} success - 操作是否成功
   * @param {string} message - 响应消息
   * @param {*} data - 附加数据
   */
  operationResult(success = true, message = '操作成功', data = null) {
    const { ctx } = this;
    ctx.body = unifiedResponse.operation(success, message, data);
    ctx.status = success ? 200 : 400;
  }

  /**
   * 直接设置响应 (使用统一响应系统)
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 响应代码
   */
  setResponse(data = null, message = '', code = 200) {
    const { ctx } = this;
    unifiedResponse.setResponse(ctx, data, message, code);
  }

  /**
   * 抛出自定义错误
   * @param {string} type - 错误类型
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   */
  throwError(type, message, code) {
    unifiedResponse.throwError(type, message, code);
  }

  /**
   * 统一的参数验证
   * @param {Object} rules - 验证规则
   * @param {Object} data - 待验证数据
   * @param {string} source - 数据源 ('query' | 'body' | 'params')
   */
  validateParams(rules, data = null, source = 'body') {
    const { ctx } = this;
    let targetData;

    if (data) {
      targetData = data;
    } else {
      switch (source) {
        case 'query':
          targetData = ctx.request.query;
          break;
        case 'params':
          targetData = ctx.params;
          break;
        case 'body':
        default:
          targetData = ctx.request.body;
          break;
      }
    }

    try {
      ctx.validate(rules, targetData);
      return true;
    } catch (error) {
      this.error(error.message, 422);
      return false;
    }
  }

  /**
   * 统一的分页参数验证
   * @param {string} source - 数据源 ('query' | 'body')
   */
  validatePagination(source = 'query') {
    return this.validateParams(validationRules.paginationRules, null, source);
  }

  /**
   * 使用预定义验证规则进行验证
   * @param {string} ruleName - 验证规则名称
   * @param {Object} data - 待验证数据
   * @param {string} source - 数据源 ('query' | 'body' | 'params')
   */
  validateWithRule(ruleName, data = null, source = 'body') {
    const rules = validationRules[ruleName];
    if (!rules) {
      this.error(`未找到验证规则: ${ruleName}`, 500);
      return false;
    }

    return this.validateParams(rules, data, source);
  }

  /**
   * 批量验证多个规则
   * @param {Array} ruleNames - 验证规则名称数组
   * @param {Object} data - 待验证数据
   * @param {string} source - 数据源
   */
  validateWithRules(ruleNames, data = null, source = 'body') {
    for (const ruleName of ruleNames) {
      if (!this.validateWithRule(ruleName, data, source)) {
        return false;
      }
    }
    return true;
  }

  /**
   * 统一的ID参数验证
   * @param {string} paramName - 参数名称，默认为'id'
   * @param {boolean} allowString - 是否允许字符串类型，默认为false
   */
  validateId(paramName = 'id', allowString = false) {
    const { ctx } = this;
    const id = ctx.params[paramName];

    if (!id) {
      this.error(`缺少必要参数: ${paramName}`, 400);
      return false;
    }

    // 如果允许字符串，直接返回字符串值
    if (allowString) {
      return id;
    }

    // 否则按原来的逻辑，要求必须是数字
    const numId = ctx.helper.toInt(id);
    if (numId <= 0) {
      this.error(`无效的${paramName}参数`, 400);
      return false;
    }

    return numId;
  }

  /**
   * 统一的异步方法执行包装器
   * 提供统一的错误处理和日志记录
   * @param {Function} asyncFn - 异步函数
   * @param {string} operation - 操作描述
   */
  async executeWithErrorHandling(asyncFn, operation = '操作') {
    const { ctx, logger } = this;

    try {
      const result = await asyncFn();
      logger.info(`${operation}成功`, {
        userId: ctx.state.user?.id,
        operation,
        timestamp: new Date().toISOString(),
      });
      return result;
    } catch (error) {
      logger.error(`${operation}失败`, {
        error: error.message,
        stack: error.stack,
        userId: ctx.state.user?.id,
        operation,
        timestamp: new Date().toISOString(),
      });

      // 根据错误类型返回不同的错误码 (使用统一响应系统)
      if (error instanceof CustomErrors.ValidationError) {
        this.error(error.message, 422);
      } else if (error instanceof CustomErrors.UnauthorizedError) {
        this.error(error.message, 401);
      } else if (error instanceof CustomErrors.ForbiddenError) {
        this.error(error.message, 403);
      } else if (error instanceof CustomErrors.NotFoundError) {
        this.error(error.message, 404);
      } else if (error instanceof CustomErrors.BusinessError) {
        this.error(error.message, error.code);
      } else if (error instanceof CustomErrors.BadRequestError) {
        this.error(error.message, 400);
      } else if (error instanceof CustomErrors.TimeoutError) {
        this.error(error.message, 408);
      } else if (error instanceof CustomErrors.ConflictError) {
        this.error(error.message, 409);
      } else if (error instanceof CustomErrors.RateLimitError) {
        this.error(error.message, 429);
      } else if (error instanceof CustomErrors.ServiceUnavailableError) {
        this.error(error.message, 503);
      } else if (error.name === 'ValidationError') {
        this.error(error.message, 422);
      } else if (error.name === 'UnauthorizedError') {
        this.error('未授权访问', 401);
      } else if (error.name === 'ForbiddenError') {
        this.error('禁止访问', 403);
      } else if (error.name === 'NotFoundError') {
        this.error('资源不存在', 404);
      } else {
        this.error('服务器内部错误', 500);
      }

      return null;
    }
  }

  /**
   * 权限检查
   * @param {Array|string} roles - 允许的角色
   */
  checkPermission(roles) {
    const { ctx } = this;

    if (!ctx.state.user) {
      this.error('请先登录', 401);
      return false;
    }

    if (roles) {
      const userRoles = Array.isArray(roles) ? roles : [ roles ];
      if (!ctx.helper.hasRole(userRoles)) {
        this.error('权限不足', 403);
        return false;
      }
    }

    return true;
  }

  /**
   * 管理员权限检查
   */
  checkAdminPermission() {
    const { ctx } = this;

    if (!ctx.helper.isAdmin()) {
      this.error('需要管理员权限', 403);
      return false;
    }

    return true;
  }

  /**
   * 标准的CRUD操作 - 列表查询
   * @param {string} serviceName - 服务名称
   * @param {string} methodName - 方法名称，默认为'findAll'
   * @param {boolean} needPagination - 是否需要分页验证，默认为true
   */
  async index(serviceName, methodName = 'findAll', needPagination = true) {
    const { ctx } = this;

    if (needPagination && !this.validatePagination('query')) {
      return;
    }

    const result = await this.executeWithErrorHandling(
      () => ctx.service[serviceName][methodName](ctx.request.query),
      `查询${serviceName}列表`
    );

    if (result !== null) {
      this.success(result);
    }
  }

  /**
   * 标准的CRUD操作 - 详情查询
   * @param {string} serviceName - 服务名称
   * @param {string} methodName - 方法名称，默认为'show'
   * @param {string} paramName - 参数名称，默认为'id'
   */
  async show(serviceName, methodName = 'show', paramName = 'id') {
    const { ctx } = this;

    const id = this.validateId(paramName);
    if (!id) { return; }

    const result = await this.executeWithErrorHandling(
      () => ctx.service[serviceName][methodName](id),
      `查询${serviceName}详情`
    );

    if (result !== null) {
      this.success(result);
    }
  }

  /**
   * 标准的CRUD操作 - 创建
   * @param {string} serviceName - 服务名称
   * @param {Object} validationRules - 验证规则
   * @param {string} methodName - 方法名称，默认为'create'
   */
  async create(serviceName, validationRules = {}, methodName = 'create') {
    const { ctx } = this;

    if (Object.keys(validationRules).length > 0 && !this.validateParams(validationRules)) {
      return;
    }

    const result = await this.executeWithErrorHandling(
      () => ctx.service[serviceName][methodName](ctx.request.body),
      `创建${serviceName}`
    );

    if (result !== null) {
      this.success(result, '创建成功', 201);
    }
  }

  /**
   * 标准的CRUD操作 - 更新
   * @param {string} serviceName - 服务名称
   * @param {Object} validationRules - 验证规则
   * @param {string} methodName - 方法名称，默认为'update'
   * @param {string} paramName - 参数名称，默认为'id'
   */
  async update(serviceName, validationRules = {}, methodName = 'update', paramName = 'id') {
    const { ctx } = this;

    const id = this.validateId(paramName);
    if (!id) { return; }

    if (Object.keys(validationRules).length > 0 && !this.validateParams(validationRules)) {
      return;
    }

    const result = await this.executeWithErrorHandling(
      () => ctx.service[serviceName][methodName](id, ctx.request.body),
      `更新${serviceName}`
    );

    if (result !== null) {
      this.success(result, '更新成功');
    }
  }

  /**
   * 标准的CRUD操作 - 删除
   * @param {string} serviceName - 服务名称
   * @param {string} methodName - 方法名称，默认为'destroy'
   * @param {string} paramName - 参数名称，默认为'id'
   */
  async destroy(serviceName, methodName = 'destroy', paramName = 'id') {
    const { ctx } = this;

    const id = this.validateId(paramName);
    if (!id) { return; }

    const result = await this.executeWithErrorHandling(
      () => ctx.service[serviceName][methodName](id),
      `删除${serviceName}`
    );

    if (result !== null) {
      this.success(result, '删除成功');
    }
  }

  // ==================== 扩展工具方法 ====================

  /**
   * 检查资源所有权
   * @param {string} resourceUserId - 资源所属用户ID
   */
  checkOwnership(resourceUserId) {
    const { ctx } = this;
    if (!ctx.state.user) {
      this.error('未登录', 401);
      return false;
    }

    // 管理员可以访问所有资源
    if (ctx.state.user.roleId === 'admin') {
      return true;
    }

    // 检查是否为资源所有者
    if (ctx.state.user.id !== resourceUserId) {
      this.error('无权访问此资源', 403);
      return false;
    }

    return true;
  }

  /**
   * 记录操作日志
   * @param {string} action - 操作类型
   * @param {string} resource - 资源类型
   * @param {string} resourceId - 资源ID
   * @param {Object} details - 详细信息
   */
  async logOperation(action, resource, resourceId = null, details = {}) {
    const { ctx } = this;
    try {
      if (ctx.service.operate && ctx.service.operate.log) {
        await ctx.service.operate.log({
          userId: ctx.state.user?.id,
          action,
          resource,
          resourceId,
          details,
          ip: ctx.ip,
          userAgent: ctx.get('user-agent'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      ctx.logger.error('记录操作日志失败:', error);
    }
  }

  /**
   * 获取客户端信息
   */
  getClientInfo() {
    const { ctx } = this;
    return {
      ip: ctx.ip,
      userAgent: ctx.get('user-agent'),
      referer: ctx.get('referer'),
      clientId: ctx.get('clientid'),
    };
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   * @param {number} ttl - 过期时间（秒）
   */
  async setCache(key, value, ttl = 3600) {
    const { ctx } = this;
    try {
      if (ctx.service.cache) {
        await ctx.service.cache.setex(key, value, ttl);
      }
    } catch (error) {
      ctx.logger.error('设置缓存失败:', error);
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   */
  async getCache(key) {
    const { ctx } = this;
    try {
      if (ctx.service.cache) {
        return await ctx.service.cache.get(key);
      }
      return null;
    } catch (error) {
      ctx.logger.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  async deleteCache(key) {
    const { ctx } = this;
    try {
      if (ctx.service.cache) {
        await ctx.service.cache.del(key);
      }
    } catch (error) {
      ctx.logger.error('删除缓存失败:', error);
    }
  }

  /**
   * 格式化查询参数
   * @param {Object} query - 原始查询参数
   * @param {Array} allowedFields - 允许的字段
   */
  formatQuery(query, allowedFields = []) {
    const formatted = {};

    for (const field of allowedFields) {
      if (query[field] !== undefined && query[field] !== '') {
        formatted[field] = query[field];
      }
    }

    return formatted;
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return require('uuid').v4();
  }

  /**
   * 安全的JSON解析
   * @param {string} jsonString - JSON字符串
   * @param {*} defaultValue - 默认值
   */
  safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      this.ctx.logger.warn('JSON解析失败:', error.message);
      return defaultValue;
    }
  }
}

module.exports = BaseController;
