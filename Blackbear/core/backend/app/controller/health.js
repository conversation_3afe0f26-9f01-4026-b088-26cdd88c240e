              'use strict';

const { Controller } = require('egg');

/**
 * 健康检查控制器
 * 提供应用健康状态检查和监控指标
 */
class HealthController extends Controller {

  /**
   * 基础健康检查
   * GET /api/health
   */
  async index() {
    const { ctx } = this;
    const startTime = Date.now();

    try {
      // 检查基础服务状态
      const healthStatus = await this.checkSystemHealth();
      const responseTime = Date.now() - startTime;

      ctx.status = healthStatus.status === 'healthy' ? 200 : 503;
      ctx.body = {
        status: healthStatus.status,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: `${responseTime}ms`,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: healthStatus.checks,
      };

    } catch (error) {
      ctx.logger.error('Health check failed:', error);
      ctx.status = 503;
      ctx.body = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 详细健康检查
   * GET /api/health/detailed
   */
  async detailed() {
    const { ctx } = this;
    const startTime = Date.now();

    try {
      const [
        systemHealth,
        databaseHealth,
        redisHealth,
        memoryHealth,
        diskHealth,
      ] = await Promise.all([
        this.checkSystemHealth(),
        this.checkDatabaseHealth(),
        this.checkRedisHealth(),
        this.checkMemoryHealth(),
        this.checkDiskHealth(),
      ]);

      const allChecks = {
        ...systemHealth.checks,
        database: databaseHealth,
        redis: redisHealth,
        memory: memoryHealth,
        disk: diskHealth,
      };

      const overallStatus = Object.values(allChecks).every(check => check.status === 'healthy') 
        ? 'healthy' : 'unhealthy';

      const responseTime = Date.now() - startTime;

      ctx.status = overallStatus === 'healthy' ? 200 : 503;
      ctx.body = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        responseTime: `${responseTime}ms`,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: allChecks,
        system: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
          pid: process.pid,
        },
      };

    } catch (error) {
      ctx.logger.error('Detailed health check failed:', error);
      ctx.status = 503;
      ctx.body = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 就绪检查
   * GET /api/health/ready
   */
  async ready() {
    const { ctx } = this;

    try {
      // 检查关键依赖是否就绪
      const [databaseReady, redisReady] = await Promise.all([
        this.isDatabaseReady(),
        this.isRedisReady(),
      ]);

      const isReady = databaseReady && redisReady;

      ctx.status = isReady ? 200 : 503;
      ctx.body = {
        ready: isReady,
        timestamp: new Date().toISOString(),
        checks: {
          database: databaseReady,
          redis: redisReady,
        },
      };

    } catch (error) {
      ctx.logger.error('Readiness check failed:', error);
      ctx.status = 503;
      ctx.body = {
        ready: false,
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 存活检查
   * GET /api/health/live
   */
  async live() {
    const { ctx } = this;

    // 简单的存活检查
    ctx.status = 200;
    ctx.body = {
      alive: true,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  /**
   * 性能指标
   * GET /api/health/metrics
   */
  async metrics() {
    const { ctx } = this;

    try {
      const metrics = await this.collectMetrics();

      ctx.body = {
        timestamp: new Date().toISOString(),
        metrics,
      };

    } catch (error) {
      ctx.logger.error('Metrics collection failed:', error);
      ctx.status = 500;
      ctx.body = {
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 检查系统健康状态
   */
  async checkSystemHealth() {
    const checks = {};

    // 检查内存使用
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    checks.memory = {
      status: memoryUsagePercent < 90 ? 'healthy' : 'unhealthy',
      usage: `${memoryUsagePercent.toFixed(2)}%`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
    };

    // 检查CPU使用（简化）
    const cpuUsage = process.cpuUsage();
    checks.cpu = {
      status: 'healthy',
      user: cpuUsage.user,
      system: cpuUsage.system,
    };

    // 检查运行时间
    const uptime = process.uptime();
    checks.uptime = {
      status: 'healthy',
      seconds: uptime,
      human: this.formatUptime(uptime),
    };

    const overallStatus = Object.values(checks).every(check => check.status === 'healthy') 
      ? 'healthy' : 'unhealthy';

    return { status: overallStatus, checks };
  }

  /**
   * 检查数据库健康状态
   */
  async checkDatabaseHealth() {
    try {
      const startTime = Date.now();
      await this.app.model.query('SELECT 1');
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        connection: 'active',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        connection: 'failed',
      };
    }
  }

  /**
   * 检查Redis健康状态
   */
  async checkRedisHealth() {
    try {
      const startTime = Date.now();
      await this.app.redis.ping();
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        connection: 'active',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        connection: 'failed',
      };
    }
  }

  /**
   * 检查内存健康状态
   */
  async checkMemoryHealth() {
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    return {
      status: memoryUsagePercent < 90 ? 'healthy' : 'unhealthy',
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss,
      usagePercent: memoryUsagePercent.toFixed(2),
    };
  }

  /**
   * 检查磁盘健康状态
   */
  async checkDiskHealth() {
    try {
      const fs = require('fs');
      const stats = fs.statSync('./');
      
      return {
        status: 'healthy',
        accessible: true,
        stats: {
          size: stats.size,
          modified: stats.mtime,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        accessible: false,
        error: error.message,
      };
    }
  }

  /**
   * 检查数据库是否就绪
   */
  async isDatabaseReady() {
    try {
      await this.app.model.query('SELECT 1');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查Redis是否就绪
   */
  async isRedisReady() {
    try {
      await this.app.redis.ping();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 收集性能指标
   */
  async collectMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      process: {
        uptime: process.uptime(),
        pid: process.pid,
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      eventLoop: {
        // 可以添加事件循环延迟监控
      },
    };
  }

  /**
   * 格式化运行时间
   */
  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }
}

module.exports = HealthController;
