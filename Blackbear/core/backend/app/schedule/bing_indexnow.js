'use strict';

const BaseSchedule = require('../utils/base-schedule');

/**
 * Bing IndexNow推送任务
 * 定期向Bing搜索引擎推送最新的文章和标签链接
 */
class BingIndexNowSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: 'Bing IndexNow推送任务',
      configKey: 'scheduleBingIndex',
      skipLocal: true,
      notifyOnError: true,
      requiredServices: [ 'service.posts', 'service.tags', 'service.bing' ],
      maxRetries: 3,
      retryDelay: 3000,
    });
  }

  async run(ctx) {
    const stats = {
      postsCount: 0,
      tagsCount: 0,
      totalUrls: 0,
      pushedUrls: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行Bing IndexNow推送');

      // 检查必要的配置
      const { webURL } = ctx.app.config;
      if (!webURL) {
        throw new Error('缺少webURL配置');
      }

      // 获取最新文章URLs
      const postUrls = await this.getPostUrls(ctx, stats);

      // 获取最新标签URLs
      const tagUrls = await this.getTagUrls(ctx, stats);

      // 合并URLs，Bing IndexNow一次最多推送10个URL
      const allUrls = [ ...postUrls, ...tagUrls ];
      const urlsToSubmit = allUrls.slice(0, 10);

      stats.totalUrls = urlsToSubmit.length;

      if (urlsToSubmit.length === 0) {
        ctx.logger.info('没有找到需要推送的URL');
        return stats;
      }

      ctx.logger.info(`准备推送 ${urlsToSubmit.length} 个URL到Bing`);

      // 执行推送
      const pushResult = await this.executeWithRetry(
        async () => {
          return await ctx.service.bing.pushUrls(urlsToSubmit);
        },
        'Bing IndexNow推送',
        ctx
      );

      if (pushResult) {
        stats.pushedUrls = pushResult.successCount || urlsToSubmit.length;
        ctx.logger.info(`Bing IndexNow推送完成: ${stats.pushedUrls} 个URL推送成功`);
      }

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('Bing IndexNow推送任务执行失败:', error);

      await this.sendNotification(
        ctx,
        'Bing IndexNow推送失败',
        `Bing IndexNow推送任务执行失败: ${error.message}`,
        'error'
      );

      throw error;
    }
  }

  /**
   * 获取文章URLs
   * @param ctx
   * @param stats
   */
  async getPostUrls(ctx, stats) {
    try {
      const posts = await ctx.service.posts.getAll(5);
      if (!posts || !Array.isArray(posts)) {
        ctx.logger.warn('获取文章列表失败或为空');
        return [];
      }

      stats.postsCount = posts.length;
      const { webURL } = ctx.app.config;

      const postUrls = posts
        .filter(post => post && post.id)
        .map(post => `${webURL}/post/${post.id}`);

      ctx.logger.info(`获取到 ${postUrls.length} 个文章URL`);
      return postUrls;

    } catch (error) {
      ctx.logger.error('获取文章URLs失败:', error);
      return [];
    }
  }

  /**
   * 获取标签URLs
   * @param ctx
   * @param stats
   */
  async getTagUrls(ctx, stats) {
    try {
      const tags = await ctx.service.tags.getAll(5);
      if (!tags || !Array.isArray(tags)) {
        ctx.logger.warn('获取标签列表失败或为空');
        return [];
      }

      stats.tagsCount = tags.length;
      const { webURL } = ctx.app.config;

      const tagUrls = tags
        .filter(tag => tag && tag.id)
        .map(tag => `${webURL}/tag/${tag.id}`);

      ctx.logger.info(`获取到 ${tagUrls.length} 个标签URL`);
      return tagUrls;

    } catch (error) {
      ctx.logger.error('获取标签URLs失败:', error);
      return [];
    }
  }

  /**
   * 验证URL格式
   * @param urls
   */
  validateUrls(urls) {
    return urls.filter(url => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    });
  }
}

// 创建任务实例
const bingIndexNowTask = new BingIndexNowSchedule();

module.exports = {
  schedule: {
    cron: '0 30 04 * * *', // 每天凌晨4:30执行
    type: 'worker',
    immediate: false,
  },
  async task(ctx) {
    return await bingIndexNowTask.execute(ctx);
  },
};
