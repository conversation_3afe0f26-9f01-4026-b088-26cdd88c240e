'use strict';

const BaseSchedule = require('../utils/base-schedule');
const { Op } = require('sequelize');

/**
 * 标签锁定清理任务
 * 定期清理过期的标签锁定状态，防止标签被永久锁定
 */
class ClearTagLockerSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '标签锁定清理任务',
      configKey: 'scheduleClearTagLocker',
      skipLocal: false, // 本地也需要执行
      notifyOnError: false, // 这是维护任务，不需要错误通知
      requiredServices: [ 'model.Tag' ],
      maxRetries: 2,
    });
  }

  async run(ctx) {
    const stats = {
      totalChecked: 0,
      clearedCount: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行标签锁定清理');

      // 获取当前时间
      const currentTime = this.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss');

      // 先查询需要清理的标签数量
      const lockedTags = await this.getExpiredLockedTags(ctx, currentTime);
      stats.totalChecked = lockedTags.length;

      if (stats.totalChecked === 0) {
        ctx.logger.info('没有找到过期的锁定标签');
        return stats;
      }

      ctx.logger.info(`找到 ${stats.totalChecked} 个过期锁定的标签`);

      // 清理过期的锁定
      stats.clearedCount = await this.clearExpiredLocks(ctx, currentTime);

      ctx.logger.info(`标签锁定清理完成: 清理了 ${stats.clearedCount} 个过期锁定`);

      // 记录详细的清理信息
      if (stats.clearedCount > 0) {
        await this.logClearDetails(ctx, lockedTags.slice(0, stats.clearedCount));
      }

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('标签锁定清理任务执行失败:', error);
      throw error;
    }
  }

  /**
   * 获取过期锁定的标签
   * @param ctx
   * @param currentTime
   */
  async getExpiredLockedTags(ctx, currentTime) {
    try {
      const tags = await ctx.model.Tag.findAll({
        where: {
          lockTime: {
            [Op.lt]: currentTime,
          },
          lockUser: {
            [Op.ne]: null,
          },
        },
        attributes: [ 'id', 'name', 'lockTime', 'lockUser' ],
        limit: 1000, // 限制查询数量，防止内存溢出
      });

      return tags || [];

    } catch (error) {
      ctx.logger.error('获取过期锁定标签失败:', error);
      return [];
    }
  }

  /**
   * 清理过期的锁定
   * @param ctx
   * @param currentTime
   */
  async clearExpiredLocks(ctx, currentTime) {
    try {
      const result = await ctx.model.Tag.update(
        {
          lockTime: null,
          lockUser: null,
        },
        {
          where: {
            lockTime: {
              [Op.lt]: currentTime,
            },
          },
        }
      );

      // result是一个数组，第一个元素是受影响的行数
      return Array.isArray(result) ? result[0] : result;

    } catch (error) {
      ctx.logger.error('清理过期锁定失败:', error);
      throw error;
    }
  }

  /**
   * 记录清理详情
   * @param ctx
   * @param clearedTags
   */
  async logClearDetails(ctx, clearedTags) {
    try {
      const details = clearedTags.map(tag => ({
        id: tag.get('id'),
        name: tag.get('name'),
        lockUser: tag.get('lockUser'),
        lockTime: tag.get('lockTime'),
      }));

      ctx.logger.info('清理的标签详情:', details);

      // 可以扩展为存储到审计日志表
      // await ctx.model.AuditLog.create({
      //   action: 'clear_tag_lock',
      //   details: JSON.stringify(details),
      //   timestamp: new Date(),
      // });

    } catch (error) {
      ctx.logger.error('记录清理详情失败:', error);
    }
  }

  /**
   * 获取锁定统计信息
   * @param ctx
   */
  async getLockStatistics(ctx) {
    try {
      const currentTime = this.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss');

      const [ totalLocked, expiredLocked ] = await Promise.all([
        // 总锁定数量
        ctx.model.Tag.count({
          where: {
            lockUser: {
              [Op.ne]: null,
            },
          },
        }),
        // 过期锁定数量
        ctx.model.Tag.count({
          where: {
            lockTime: {
              [Op.lt]: currentTime,
            },
            lockUser: {
              [Op.ne]: null,
            },
          },
        }),
      ]);

      return {
        totalLocked,
        expiredLocked,
        activeLocked: totalLocked - expiredLocked,
      };

    } catch (error) {
      ctx.logger.error('获取锁定统计失败:', error);
      return null;
    }
  }
}

// 创建任务实例
const clearTagLockerTask = new ClearTagLockerSchedule();

module.exports = {
  schedule: {
    interval: 1000 * 60 * 10, // 每10分钟执行一次
    type: 'worker',
    disable: false,
    immediate: true,
  },
  async task(ctx) {
    return await clearTagLockerTask.execute(ctx);
  },
};
