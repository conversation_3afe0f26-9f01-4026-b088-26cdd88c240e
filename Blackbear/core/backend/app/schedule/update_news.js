'use strict';


module.exports = {
  schedule: {
    // cron: '0 5 15 * * *',
    interval: '1m',
    type: 'worker',
    immediate: true,
  },
  async task(ctx) {
    try {
      ctx.logger.info('开始执行新闻更新任务');

      // 匹配stock
      const news = await ctx.model.News.findAll({
        order: [[ 'newsID', 'DESC' ]],
        where: {
          star: false,
        },
        limit: 100,
      });

      ctx.logger.info(`找到 ${news.length} 条待处理新闻`);

      // 直接录入
      for (let j = 0; j < news.length; j++) {
        const element = news[j];
        const newsId = element.get('newsID');

        try {
          // 安全获取keywords字段，处理null值
          const keywords = element.get('keywords');
          if (!keywords || typeof keywords !== 'string') {
            ctx.logger.warn(`新闻 ${newsId} 的keywords字段为空或无效，跳过处理`);
            // 仍然标记为已处理，避免重复处理
            await element.update({ star: true });
            continue;
          }

          const tags = keywords.split(',').filter(tag => tag && tag.trim().length > 0);

          if (tags.length === 0) {
            ctx.logger.warn(`新闻 ${newsId} 没有有效的标签，跳过处理`);
            await element.update({ star: true });
            continue;
          }

          for (let index = 0; index < tags.length; index++) {
            const tag = tags[index].trim();

            if (!tag) { continue; }

            try {
              await ctx.model.transaction(async transaction => {
                const stock = await ctx.model.Stock.findByPk(tag, { transaction });

                if (stock && stock.get('code')) {
                  // 更新新闻的股票代码
                  await element.update({
                    code: tag,
                  }, { transaction });

                  // 创建新闻-股票关联
                  await ctx.model.NewsStock.findOrCreate({
                    where: {
                      code: tag,
                      newsId,
                    },
                    transaction,
                  });

                  ctx.logger.debug(`新闻 ${newsId} 关联股票 ${tag} 成功`);
                }
              });
            } catch (transactionError) {
              ctx.logger.error(`处理新闻 ${newsId} 标签 ${tag} 时发生错误:`, transactionError);
              // 继续处理下一个标签，不中断整个流程
            }
          }

          // 标记新闻为已处理
          await element.update({ star: true });
          ctx.logger.debug(`新闻 ${newsId} 处理完成`);

        } catch (newsError) {
          ctx.logger.error(`处理新闻 ${newsId} 时发生错误:`, newsError);
          // 标记为已处理，避免重复处理有问题的新闻
          await element.update({ star: true });
        }
      }

      ctx.logger.info('新闻更新任务执行完成');

    } catch (error) {
      ctx.logger.error('新闻更新任务执行失败:', error);
      throw error;
    }
  },
};
