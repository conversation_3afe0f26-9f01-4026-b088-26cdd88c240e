'use strict';

const moment = require('moment');

module.exports = {
  schedule: {
    cron: '0 30 15 * * *',
    type: 'worker',
    immediate: false,
  },
  async task(ctx) {
    const config = await ctx.service.configurations.getConfig('schedule');

    if (config && config.scheduleStatistics === false) {
      return;
    }

    if (ctx.app.config.env === 'local') {
      return;
    }
    const [ tags, daliy, beixiang, industryUp, industryDown ] = await Promise.all([
      ctx.service.tags.findAllTags({
        type: 'hot',
        pageSize: 10,
        pageIndex: 1,
      }),
      ctx.service.quants.getToadyDaliy(),
      axios.get('http://push2.eastmoney.com/api/qt/kamt/get?fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f63&ut=b2884a393a59ad64002292a3e90d46a5&_=1599120897799'),
      ctx.service.stocks.getIndustryUp(),
      ctx.service.stocks.getIndustryDown(),
    ]).catch(error => {
      console.log(error);
    });

    if (daliy.isOpen) {

      let str = '股市简报：\n';

      const upOrDown = pct => {
        if (pct >= 0) {
          return `上涨${pct}%`;
        }

        return `下跌${Math.abs(pct)}%`;
      };

      // 涨跌统计
      str = `${str}上证指数${upOrDown(daliy.shClosePct.toFixed(2))}，收于${daliy.shClose}；`;
      str = `${str}深证成指${upOrDown(daliy.szClosePct.toFixed(2))}，收于${daliy.szClose}；`;
      str = `${str}创业板指${upOrDown(daliy.cybClosePct.toFixed(2))}，收于${daliy.cybClose}；`;

      // 成交量
      str = `${str}总成交量${(daliy.volume / 100000000).toFixed(2)}亿。\n`;


      // 计算北向资金
      const { hk2sh } = beixiang.data.data;
      const { hk2sz } = beixiang.data.data;
      let hk2cn = 0;
      if (hk2sh.status !== 4 && hk2sz.status !== 4) {
        hk2cn = ((hk2sh.dayNetAmtIn + hk2sz.dayNetAmtIn) / 10000).toFixed(2);
        if (hk2cn >= 0) {
          str = `${str}北向资金净流入${Math.abs(hk2cn)}亿，`;
        } else {
          str = `${str}北向资金净流出${Math.abs(hk2cn)}亿，`;
        }

        str = `${str}其中，沪股通${(hk2sh.dayNetAmtIn / 10000).toFixed(2)}亿，深股通${(hk2sz.dayNetAmtIn / 10000).toFixed(2)}亿。\n`;
      } else {
        str = `${str}北向通道今日关闭。 \n`;
      }

      // 涨幅板块
      const industry_up_name_list = [];
      if (industryUp && Array.isArray(industryUp)) {
        industryUp.forEach(item => {
          if (item && item.name) {
            industry_up_name_list.push(item.name);
          }
        });
      }

      if (industry_up_name_list.length > 0) {
        str = `${str}涨幅居前板块：${industry_up_name_list.join('，')}。`;
      }

      // 下跌板块
      const industry_down_name_list = [];
      if (industryDown && Array.isArray(industryDown)) {
        industryDown.forEach(item => {
          if (item && item.name) {
            industry_down_name_list.push(item.name);
          }
        });
      }

      if (industry_down_name_list.length > 0) {
        str = `${str}下跌板块：${industry_down_name_list.join('，')}。\n`;
      }

      // 活跃题材
      const tag_name_list = [];
      const tag_id_list = [];
      if (tags && Array.isArray(tags)) {
        tags.forEach(element => {
          if (element && element.tag && element.tag.name && element.tag.id) {
            tag_name_list.push(element.tag.name);
            tag_id_list.push(element.tag.id);
          }
        });
      }

      if (tag_name_list.length > 0) {
        str = `${str}跟踪科技题材涨幅居前：${tag_name_list.join('，')}。`;
      }

      const post = await ctx.service.posts.create({
        isAdmin: true,
        userId: ctx.app.config.systemUserId,
        content: str,
        title: `每日复盘${moment().format('YYYYMMDD')}`,
        category: 'review',
        status: 'published',
        time: moment().format('YYYYMMDD'),
        tags: tag_id_list.join(','),
      });

      await ctx.service.messages.sendMessage({
        type: 'post',
        title: '短评',
        content: str,
        id: post.id,
        userId: ctx.app.config.systemUserId,
      });

      str = `#股市# #A股# \n${str}`;

      // await ctx.service.weibo.send_text(str);
    }
  },
};
