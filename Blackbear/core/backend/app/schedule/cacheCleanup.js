'use strict';

const { Subscription } = require('egg');

class CacheCleanupSubscription extends Subscription {
  static get schedule() {
    return {
      interval: '30m', // 每30分钟执行一次
      type: 'worker', // 指定所有的 worker 都需要执行
    };
  }

  async subscribe() {
    const { ctx } = this;

    try {
      ctx.logger.info('Starting scheduled cache cleanup...');

      // 检查数据库变化并清除相关缓存
      await ctx.service.databaseWatcher.checkForChanges();

      // 清除过期的缓存
      await this.clearExpiredCache();

      // 记录缓存统计信息
      await this.logCacheStats();

      ctx.logger.info('Scheduled cache cleanup completed');
    } catch (error) {
      ctx.logger.error('Error in scheduled cache cleanup', error);
    }
  }

  // 清除过期缓存
  async clearExpiredCache() {
    const { ctx } = this;

    try {
      // 获取所有缓存键
      const allKeys = await ctx.app.redis.keys('*');
      let expiredCount = 0;

      for (const key of allKeys) {
        // 检查键是否存在（可能已被其他进程删除）
        const exists = await ctx.app.redis.exists(key);
        if (!exists) {
          expiredCount++;
          continue;
        }

        // 检查TTL
        const ttl = await ctx.app.redis.ttl(key);
        if (ttl === -1) {
          // 没有设置过期时间的键，设置默认过期时间
          await ctx.app.redis.expire(key, 3600); // 1小时
        } else if (ttl === -2) {
          // 键不存在
          expiredCount++;
        }
      }

      if (expiredCount > 0) {
        ctx.logger.info(`Cleaned up ${expiredCount} expired cache keys`);
      }
    } catch (error) {
      ctx.logger.error('Error clearing expired cache', error);
    }
  }

  // 记录缓存统计信息
  async logCacheStats() {
    const { ctx } = this;

    try {
      const stats = await ctx.service.cacheManager.getCacheStats();
      const totalKeys = Object.values(stats).reduce((sum, count) => sum + count, 0);

      ctx.logger.info('Cache statistics', {
        totalKeys,
        details: stats,
        timestamp: new Date().toISOString(),
      });

      // 如果缓存键过多，发出警告
      if (totalKeys > 10000) {
        ctx.logger.warn('Cache keys count is high', { totalKeys });
      }
    } catch (error) {
      ctx.logger.error('Error logging cache stats', error);
    }
  }
}

module.exports = CacheCleanupSubscription;
