'use strict';

const moment = require('moment');

module.exports = {
  schedule: {
    cron: '0 10 0 * * *',
    type: 'worker',
    immediate: false,
  },
  async task(ctx) {
    try {
      ctx.logger.info('开始执行日历消息同步任务');

      const config = await ctx.service.configurations.getConfig('schedule');

      if (config && config.scheduleCalendar === false) {
        ctx.logger.info('日历同步已禁用，跳过执行');
        return;
      }

      if (ctx.app.config.env === 'local') {
        ctx.logger.info('本地环境，跳过执行');
        return;
      }

      const calendar = await ctx.service.calendars.find({
        type: 'currentday',
        date: moment().format('YYYYMMDD'),
      });

      if (calendar.calendarData &&
          calendar.calendarData[0] &&
          calendar.calendarData[0].calendarEvents &&
          calendar.calendarData[0].calendarEvents.length > 0) {

        let str = '';

        calendar.calendarData[0].calendarEvents.forEach((element, index) => {
          try {
            // 安全检查event对象和related字段
            if (element && element.event) {
              const related = element.event.related || 0;
              const content = element.event.content || '';

              // 添加星号标记
              if (typeof related === 'number' && related > 0) {
                for (let j = 0; j < Math.min(related, 5); j++) { // 限制最多5个星号
                  str += '★';
                }
              }

              // 添加内容
              if (content) {
                str += `${content}\n`;
              }
            } else {
              ctx.logger.warn(`日历事件 ${index} 数据格式异常:`, element);
            }
          } catch (eventError) {
            ctx.logger.error(`处理日历事件 ${index} 时发生错误:`, eventError);
          }
        });

        if (str.trim()) {
          await ctx.service.messages.sendMessage({
            type: 'event',
            title: '日历',
            content: str.trim(),
            userId: ctx.app.config.systemUserId,
          });

          ctx.logger.info('日历消息发送成功');
        } else {
          ctx.logger.warn('没有有效的日历内容，跳过消息发送');
        }
      } else {
        ctx.logger.info('今日无日历事件');
      }

    } catch (error) {
      ctx.logger.error('日历消息同步任务执行失败:', error);
      throw error;
    }
  },
};
