'use strict';

const BaseSchedule = require('../utils/base-schedule');

/**
 * 量化数据同步任务
 * 定期获取量化数据并发送到各个平台
 */
class SyncQuantSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '量化数据同步任务',
      configKey: 'scheduleQuant',
      skipLocal: true,
      notifyOnError: true,
      requiredServices: [
        'service.quants',
        'service.messages',
        'model.Configuration',
      ],
      maxRetries: 2,
      retryDelay: 5000,
    });
  }

  async run(ctx) {
    const stats = {
      dataFetched: false,
      marketOpen: false,
      messagesSent: 0,
      platformsSent: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行量化数据同步');

      // 获取量化数据
      const quantData = await this.fetchQuantData(ctx);
      stats.dataFetched = !!quantData;

      if (!quantData) {
        ctx.logger.warn('未获取到量化数据');
        return stats;
      }

      const { csindex, daily, config } = quantData;

      // 检查市场是否开盘
      if (!daily || !daily.isOpen) {
        ctx.logger.info('市场未开盘，跳过量化数据同步');
        return stats;
      }

      stats.marketOpen = true;

      // 生成量化报告内容
      const reportContent = await this.generateQuantReport(daily, config, csindex);

      if (!reportContent) {
        ctx.logger.warn('生成量化报告内容失败');
        return stats;
      }

      // 发送系统消息
      await this.sendSystemMessage(ctx, reportContent, stats);

      // 发送到社交平台
      await this.sendToPlatforms(ctx, reportContent, stats);

      ctx.logger.info(`量化数据同步完成: 消息 ${stats.messagesSent}, 平台 ${stats.platformsSent}`);

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('量化数据同步任务执行失败:', error);

      await this.sendNotification(
        ctx,
        '量化数据同步失败',
        `量化数据同步任务执行失败: ${error.message}`,
        'error'
      );

      throw error;
    }
  }

  /**
   * 获取量化数据
   * @param ctx
   */
  async fetchQuantData(ctx) {
    try {
      const [ csindex, daily, config ] = await Promise.all([
        ctx.service.quants.getTodayCNI(),
        ctx.service.quants.getToadyDaliy(),
        ctx.model.Configuration.findOne({
          where: { name: 'quant' },
        }),
      ]);

      return { csindex, daily, config };

    } catch (error) {
      ctx.logger.error('获取量化数据失败:', error);
      return null;
    }
  }

  /**
   * 生成量化报告内容
   * @param daily
   * @param config
   * @param csindex
   */
  async generateQuantReport(daily, config, csindex) {
    try {
      if (!daily || !config) {
        return null;
      }

      const today = this.formatDate(new Date(), 'YYYY-MM-DD');
      const decisionDate = config.get ? config.get('decisionDate') : config.decisionDate;

      let content = `时间区间: ${decisionDate || '未知'} - ${today}<br><br>`;
      content += '量化数据(阈值): <br>';

      // 添加量化指标
      content += this.addQuantMetrics(daily, config);

      // 添加风格数据
      content += this.addStyleData(daily);

      // 添加中证数据
      content += this.addCSIndexData(csindex);

      return content;

    } catch (error) {
      ctx.logger.error('生成量化报告内容失败:', error);
      return null;
    }
  }

  /**
   * 添加量化指标
   * @param daily
   * @param config
   */
  addQuantMetrics(daily, config) {
    let content = '';

    try {
      const fall3 = this.safeNumber(daily.fall3);
      const rose3 = this.safeNumber(daily.rose3);
      const crowding = this.safeNumber(daily.crowding);

      const fall3Threshold = config.get ? config.get('fall3') : config.fall3;
      const rose3Threshold = config.get ? config.get('rose3') : config.rose3;
      const crowdingThreshold = config.get ? config.get('crowding') : config.crowding;

      if (fall3 && rose3 && crowding) {
        content += `恐慌(${fall3Threshold || 'N/A'}): ${fall3} <br>`;
        content += `贪婪(${rose3Threshold || 'N/A'}): ${rose3} <br>`;
        content += `拥挤度(${crowdingThreshold || 'N/A'}): ${crowding} <br>`;
      }

      const SMB = this.safeNumber(daily.SMB);
      const HML = this.safeNumber(daily.HML);

      if (SMB && HML) {
        content += `SMB: ${SMB}, HML: ${HML} <br>`;
      }

    } catch (error) {
      console.warn('添加量化指标失败:', error);
    }

    return content;
  }

  /**
   * 添加风格数据
   * @param daily
   */
  addStyleData(daily) {
    try {
      if (daily.style) {
        return `今日风格: ${daily.style} <br>`;
      }
    } catch (error) {
      console.warn('添加风格数据失败:', error);
    }
    return '';
  }

  /**
   * 添加中证数据
   * @param csindex
   */
  addCSIndexData(csindex) {
    let content = '';

    try {
      if (csindex && Array.isArray(csindex) && csindex.length > 0) {
        content += '<br>中证数据偏离度前五(包含指数和行业): <br>';

        const csindexList = [];
        for (let i = 0; i < Math.min(5, csindex.length); i++) {
          const item = csindex[i];
          if (item && item.name && item.deviation !== undefined) {
            csindexList.push(`${item.name}(${item.deviation})`);
          }
        }

        if (csindexList.length > 0) {
          content += csindexList.join('、');
        }
      }
    } catch (error) {
      console.warn('添加中证数据失败:', error);
    }

    return content;
  }

  /**
   * 发送系统消息
   * @param ctx
   * @param content
   * @param stats
   */
  async sendSystemMessage(ctx, content, stats) {
    try {
      await ctx.service.messages.sendMessage({
        type: 'quant',
        title: '量化',
        content,
        userId: ctx.app.config.systemUserId,
      });

      stats.messagesSent++;
      ctx.logger.info('系统消息发送成功');

    } catch (error) {
      ctx.logger.error('发送系统消息失败:', error);
      stats.errors++;
    }
  }

  /**
   * 发送到社交平台
   * @param ctx
   * @param content
   * @param stats
   */
  async sendToPlatforms(ctx, content, stats) {
    const platforms = [
      { name: 'xueqiu', service: ctx.service.xueqiu },
      { name: 'weibo', service: ctx.service.weibo },
    ];

    for (const platform of platforms) {
      try {
        if (platform.service && platform.service.send_text) {
          await platform.service.send_text(content);
          stats.platformsSent++;
          ctx.logger.info(`${platform.name}平台发送成功`);
        } else {
          ctx.logger.warn(`${platform.name}服务不可用`);
        }
      } catch (error) {
        ctx.logger.error(`发送到${platform.name}平台失败:`, error);
        stats.errors++;
      }
    }
  }
}

// 创建任务实例
const syncQuantTask = new SyncQuantSchedule();

module.exports = {
  schedule: {
    cron: '0 0 21 * * *', // 每天晚上9点执行
    type: 'worker',
    immediate: false,
  },
  async task(ctx) {
    return await syncQuantTask.execute(ctx);
  },
};
