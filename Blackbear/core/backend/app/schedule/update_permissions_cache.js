'use strict';

const BaseSchedule = require('../utils/base-schedule');
const { permissionsToRedis } = require('../../app-boot-hook-do');

/**
 * 权限缓存更新任务
 * 定期将权限数据同步到Redis缓存，提升权限检查性能
 */
class UpdatePermissionsCacheSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '权限缓存更新任务',
      configKey: 'scheduleUpdatePermissionsCache',
      skipLocal: false, // 本地也需要执行
      notifyOnError: true, // 权限缓存重要，需要错误通知
      requiredServices: [ 'app.redis' ],
      maxRetries: 3,
      retryDelay: 2000,
    });
  }

  async run(ctx) {
    const stats = {
      cacheUpdated: false,
      permissionsCount: 0,
      rolesCount: 0,
      cacheSize: 0,
      updateDuration: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行权限缓存更新任务');

      const startTime = Date.now();

      // 检查Redis连接
      await this.checkRedisConnection(ctx);

      // 获取更新前的统计信息
      const beforeStats = await this.getCacheStats(ctx);

      // 执行权限缓存更新
      await this.executeWithRetry(
        async () => {
          await permissionsToRedis(ctx.app);
        },
        '权限缓存更新',
        ctx
      );

      stats.cacheUpdated = true;
      stats.updateDuration = Date.now() - startTime;

      // 获取更新后的统计信息
      const afterStats = await this.getCacheStats(ctx);
      if (afterStats) {
        stats.permissionsCount = afterStats.permissionsCount;
        stats.rolesCount = afterStats.rolesCount;
        stats.cacheSize = afterStats.cacheSize;
      }

      ctx.logger.info(`权限缓存更新完成: 耗时 ${stats.updateDuration}ms, 权限 ${stats.permissionsCount}, 角色 ${stats.rolesCount}`);

      // 验证缓存完整性
      await this.validateCacheIntegrity(ctx, stats);

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('权限缓存更新任务执行失败:', error);

      await this.sendNotification(
        ctx,
        '权限缓存更新失败',
        `权限缓存更新任务执行失败: ${error.message}`,
        'error'
      );

      throw error;
    }
  }

  /**
   * 检查Redis连接
   * @param ctx
   */
  async checkRedisConnection(ctx) {
    try {
      if (!ctx.app.redis) {
        throw new Error('Redis连接不可用');
      }

      // 测试Redis连接
      await ctx.app.redis.ping();
      ctx.logger.debug('Redis连接正常');

    } catch (error) {
      ctx.logger.error('Redis连接检查失败:', error);
      throw new Error(`Redis连接失败: ${error.message}`);
    }
  }

  /**
   * 获取缓存统计信息
   * @param ctx
   */
  async getCacheStats(ctx) {
    try {
      if (!ctx.app.redis) {
        return null;
      }

      // 获取权限相关的缓存键
      const permissionKeys = await ctx.app.redis.keys('permission:*');
      const roleKeys = await ctx.app.redis.keys('role:*');
      const userPermissionKeys = await ctx.app.redis.keys('user_permissions:*');

      // 计算缓存大小（估算）
      let totalSize = 0;
      const sampleKeys = [ ...permissionKeys, ...roleKeys, ...userPermissionKeys ].slice(0, 10);

      for (const key of sampleKeys) {
        try {
          const value = await ctx.app.redis.get(key);
          if (value) {
            totalSize += Buffer.byteLength(value, 'utf8');
          }
        } catch (error) {
          // 忽略单个键的错误
        }
      }

      // 估算总大小
      const avgKeySize = sampleKeys.length > 0 ? totalSize / sampleKeys.length : 0;
      const estimatedTotalSize = Math.round(avgKeySize * (permissionKeys.length + roleKeys.length + userPermissionKeys.length));

      return {
        permissionsCount: permissionKeys.length,
        rolesCount: roleKeys.length,
        userPermissionsCount: userPermissionKeys.length,
        cacheSize: estimatedTotalSize,
      };

    } catch (error) {
      ctx.logger.error('获取缓存统计失败:', error);
      return null;
    }
  }

  /**
   * 验证缓存完整性
   * @param ctx
   * @param stats
   */
  async validateCacheIntegrity(ctx, stats) {
    try {
      // 检查基本的权限缓存是否存在
      const basicChecks = [
        'permission:list',
        'role:list',
      ];

      for (const key of basicChecks) {
        const exists = await ctx.app.redis.exists(key);
        if (!exists) {
          ctx.logger.warn(`关键缓存键 ${key} 不存在`);
          stats.errors++;
        }
      }

      // 检查权限数据的基本结构
      const permissionList = await ctx.app.redis.get('permission:list');
      if (permissionList) {
        try {
          const permissions = JSON.parse(permissionList);
          if (!Array.isArray(permissions)) {
            ctx.logger.warn('权限列表缓存格式异常');
            stats.errors++;
          }
        } catch (parseError) {
          ctx.logger.warn('权限列表缓存解析失败:', parseError);
          stats.errors++;
        }
      }

      if (stats.errors === 0) {
        ctx.logger.info('权限缓存完整性验证通过');
      } else {
        ctx.logger.warn(`权限缓存完整性验证发现 ${stats.errors} 个问题`);
      }

    } catch (error) {
      ctx.logger.error('验证缓存完整性失败:', error);
      stats.errors++;
    }
  }

  /**
   * 清理过期的权限缓存
   * @param ctx
   */
  async cleanupExpiredCache(ctx) {
    try {
      // 获取所有权限相关的缓存键
      const allKeys = await ctx.app.redis.keys('*permission*');
      let cleanedCount = 0;

      for (const key of allKeys) {
        try {
          const ttl = await ctx.app.redis.ttl(key);
          // 如果TTL为-1（永不过期）但键名包含时间戳，可能是过期的缓存
          if (ttl === -1 && /\d{13}/.test(key)) {
            const timestamp = key.match(/\d{13}/)[0];
            const keyTime = new Date(parseInt(timestamp));
            const now = new Date();

            // 如果缓存超过24小时，删除它
            if (now - keyTime > 24 * 60 * 60 * 1000) {
              await ctx.app.redis.del(key);
              cleanedCount++;
            }
          }
        } catch (error) {
          // 忽略单个键的错误
        }
      }

      if (cleanedCount > 0) {
        ctx.logger.info(`清理了 ${cleanedCount} 个过期的权限缓存`);
      }

      return cleanedCount;

    } catch (error) {
      ctx.logger.error('清理过期缓存失败:', error);
      return 0;
    }
  }

  /**
   * 获取缓存性能指标
   * @param ctx
   */
  async getCachePerformanceMetrics(ctx) {
    try {
      const info = await ctx.app.redis.info('memory');
      const lines = info.split('\r\n');
      const metrics = {};

      lines.forEach(line => {
        if (line.includes(':')) {
          const [ key, value ] = line.split(':');
          if (key.includes('memory') || key.includes('keys')) {
            metrics[key] = value;
          }
        }
      });

      return metrics;

    } catch (error) {
      ctx.logger.error('获取缓存性能指标失败:', error);
      return null;
    }
  }
}

// 创建任务实例
const updatePermissionsCacheTask = new UpdatePermissionsCacheSchedule();

module.exports = {
  schedule: {
    interval: 1000 * 60 * 10, // 每10分钟执行一次
    type: 'worker',
    disable: false,
    immediate: true,
  },
  async task(ctx) {
    return await updatePermissionsCacheTask.execute(ctx);
  },
};
