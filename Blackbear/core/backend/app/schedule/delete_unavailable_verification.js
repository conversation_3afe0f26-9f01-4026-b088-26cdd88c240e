'use strict';

const BaseSchedule = require('../utils/base-schedule');
const { Op } = require('sequelize');

/**
 * 删除无效验证码任务
 * 定期清理过期和不可用的验证码记录，释放存储空间
 */
class DeleteUnavailableVerificationSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '删除无效验证码任务',
      configKey: 'scheduleCleanVerification',
      skipLocal: false, // 本地也需要执行清理
      notifyOnError: false, // 清理任务不需要错误通知
      requiredServices: [ 'model.Verification' ],
      maxRetries: 2,
    });
  }

  async run(ctx) {
    const stats = {
      totalChecked: 0,
      expiredCount: 0,
      unavailableCount: 0,
      deletedCount: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行验证码清理任务');

      // 获取当前时间
      const currentTime = this.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss');

      // 先统计需要清理的验证码数量
      await this.getCleanupStats(ctx, currentTime, stats);

      if (stats.totalChecked === 0) {
        ctx.logger.info('没有找到需要清理的验证码');
        return stats;
      }

      ctx.logger.info(`找到 ${stats.totalChecked} 条需要清理的验证码记录`);

      // 执行清理操作
      stats.deletedCount = await this.cleanupVerifications(ctx, currentTime);

      ctx.logger.info(`验证码清理完成: 删除了 ${stats.deletedCount} 条记录`);

      // 记录清理详情
      await this.logCleanupDetails(ctx, stats);

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('验证码清理任务执行失败:', error);
      throw error;
    }
  }

  /**
   * 获取清理统计信息
   * @param ctx
   * @param currentTime
   * @param stats
   */
  async getCleanupStats(ctx, currentTime, stats) {
    try {
      const [ expiredCount, unavailableCount ] = await Promise.all([
        // 过期的验证码数量
        ctx.model.Verification.count({
          where: {
            expirationTime: { [Op.lt]: currentTime },
          },
        }),
        // 不可用的验证码数量
        ctx.model.Verification.count({
          where: {
            available: 0,
          },
        }),
      ]);

      stats.expiredCount = expiredCount;
      stats.unavailableCount = unavailableCount;

      // 总数可能有重叠，所以需要查询实际的OR条件结果
      stats.totalChecked = await ctx.model.Verification.count({
        where: {
          [Op.or]: [
            { expirationTime: { [Op.lt]: currentTime } },
            { available: 0 },
          ],
        },
      });

    } catch (error) {
      ctx.logger.error('获取清理统计失败:', error);
      stats.totalChecked = 0;
    }
  }

  /**
   * 执行验证码清理
   * @param ctx
   * @param currentTime
   */
  async cleanupVerifications(ctx, currentTime) {
    try {
      const result = await ctx.model.Verification.destroy({
        force: true,
        where: {
          [Op.or]: [
            {
              expirationTime: { [Op.lt]: currentTime },
            },
            {
              available: 0,
            },
          ],
        },
      });

      return result || 0;

    } catch (error) {
      ctx.logger.error('清理验证码失败:', error);
      throw error;
    }
  }

  /**
   * 记录清理详情
   * @param ctx
   * @param stats
   */
  async logCleanupDetails(ctx, stats) {
    try {
      const details = {
        cleanupTime: new Date(),
        expiredCount: stats.expiredCount,
        unavailableCount: stats.unavailableCount,
        totalDeleted: stats.deletedCount,
        efficiency: stats.totalChecked > 0 ?
          Math.round((stats.deletedCount / stats.totalChecked) * 100) : 0,
      };

      ctx.logger.info('验证码清理详情:', details);

      // 可以扩展为存储到清理日志表
      // await ctx.model.CleanupLog.create({
      //   type: 'verification_cleanup',
      //   details: JSON.stringify(details),
      //   timestamp: new Date(),
      // });

    } catch (error) {
      ctx.logger.error('记录清理详情失败:', error);
    }
  }

  /**
   * 获取验证码存储统计
   * @param ctx
   */
  async getStorageStats(ctx) {
    try {
      const [ totalCount, availableCount, expiredCount ] = await Promise.all([
        ctx.model.Verification.count(),
        ctx.model.Verification.count({ where: { available: 1 } }),
        ctx.model.Verification.count({
          where: {
            expirationTime: { [Op.lt]: this.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss') },
          },
        }),
      ]);

      return {
        total: totalCount,
        available: availableCount,
        expired: expiredCount,
        unavailable: totalCount - availableCount,
      };

    } catch (error) {
      ctx.logger.error('获取存储统计失败:', error);
      return null;
    }
  }

  /**
   * 检查是否需要清理
   * @param ctx
   */
  async shouldCleanup(ctx) {
    try {
      const stats = await this.getStorageStats(ctx);
      if (!stats) { return true; } // 无法获取统计时默认执行清理

      // 如果过期或不可用的验证码超过总数的20%，建议清理
      const cleanupThreshold = Math.max(100, stats.total * 0.2);
      const needCleanup = (stats.expired + stats.unavailable) >= cleanupThreshold;

      if (needCleanup) {
        ctx.logger.info(`验证码清理建议: 总计 ${stats.total}, 需清理 ${stats.expired + stats.unavailable}`);
      }

      return needCleanup;

    } catch (error) {
      ctx.logger.error('检查清理需求失败:', error);
      return true;
    }
  }
}

// 创建任务实例
const deleteUnavailableVerificationTask = new DeleteUnavailableVerificationSchedule();

module.exports = {
  schedule: {
    cron: '0 30 23 * * *', // 每天晚上23:30执行
    type: 'worker',
    disable: false,
    immediate: true,
  },
  async task(ctx) {
    return await deleteUnavailableVerificationTask.execute(ctx);
  },
};
