'use strict';

const BaseSchedule = require('../utils/base-schedule');
const lodash = require('lodash');

/**
 * Socket消息重发任务
 * 定期检查未确认的Socket消息并进行重发，确保消息的可靠传输
 */
class ResendNotAckSocketSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: 'Socket消息重发任务',
      configKey: 'scheduleResendSocket',
      skipLocal: false, // 本地也需要执行Socket重发
      notifyOnError: true, // Socket重发失败需要通知
      requiredServices: [ 'app.redis', 'app.io' ],
      maxRetries: 2,
      retryDelay: 1000,
    });
  }

  async run(ctx) {
    const stats = {
      totalSocketKeys: 0,
      pendingMessages: 0,
      resentMessages: 0,
      failedResends: 0,
      connectedClients: 0,
      errors: 0,
    };

    try {
      ctx.logger.debug('开始执行Socket消息重发任务');

      // 检查必需的服务
      if (!this.checkRequiredServices(ctx)) {
        throw new Error('Socket服务或Redis服务不可用');
      }

      // 获取当前Socket键列表
      const currentSocketKeys = await this.getCurrentSocketKeys(ctx);
      stats.totalSocketKeys = currentSocketKeys.length;

      if (stats.totalSocketKeys === 0) {
        ctx.logger.debug('没有找到待处理的Socket消息');
        return stats;
      }

      // 获取需要重发的消息
      const pendingMessages = await this.getPendingMessages(ctx, currentSocketKeys);
      stats.pendingMessages = pendingMessages.length;

      if (stats.pendingMessages === 0) {
        ctx.logger.debug('没有需要重发的Socket消息');
        // 更新应用中的Socket键缓存
        this.updateSocketKeysCache(ctx, currentSocketKeys);
        return stats;
      }

      ctx.logger.debug(`找到 ${stats.pendingMessages} 条需要重发的Socket消息`);

      // 获取连接的客户端统计
      stats.connectedClients = await this.getConnectedClientsCount(ctx);

      // 执行消息重发
      const resendResults = await this.resendMessages(ctx, pendingMessages);
      stats.resentMessages = resendResults.success;
      stats.failedResends = resendResults.failed;

      // 更新Socket键缓存
      this.updateSocketKeysCache(ctx, currentSocketKeys);

      ctx.logger.debug(`Socket消息重发完成: 重发 ${stats.resentMessages}, 失败 ${stats.failedResends}`);

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('Socket消息重发任务执行失败:', error);
      throw error;
    }
  }

  /**
   * 检查必需的服务
   * @param ctx
   */
  checkRequiredServices(ctx) {
    const { app: { redis, io } } = ctx;

    if (!redis) {
      ctx.logger.error('Redis服务不可用');
      return false;
    }

    if (!io) {
      ctx.logger.error('Socket.IO服务不可用');
      return false;
    }

    return true;
  }

  /**
   * 获取当前Socket键列表
   * @param ctx
   */
  async getCurrentSocketKeys(ctx) {
    try {
      const { app: { redis }, helper } = ctx;
      const socketKeys = await redis.keys(helper.redisKeys.socketBaseSocketId('*'));
      return socketKeys || [];
    } catch (error) {
      ctx.logger.error('获取Socket键列表失败:', error);
      return [];
    }
  }

  /**
   * 获取需要重发的消息
   * @param ctx
   * @param currentSocketKeys
   */
  async getPendingMessages(ctx, currentSocketKeys) {
    try {
      // 获取上次的Socket键列表
      const previousSocketKeys = ctx.app.socketIdOnRedisKeys || [];

      // 找出交集：存在于两次检查中的键，表示至少2秒未收到ACK
      const intersection = lodash.intersection(previousSocketKeys, currentSocketKeys);

      ctx.logger.debug(`上次键数: ${previousSocketKeys.length}, 当前键数: ${currentSocketKeys.length}, 交集: ${intersection.length}`);

      return intersection;
    } catch (error) {
      ctx.logger.error('获取待重发消息失败:', error);
      return [];
    }
  }

  /**
   * 获取连接的客户端数量
   * @param ctx
   */
  async getConnectedClientsCount(ctx) {
    try {
      const { app: { io } } = ctx;
      const nsp = io.of('/');

      return new Promise(resolve => {
        nsp.clients((error, clients) => {
          if (error) {
            ctx.logger.warn('获取客户端列表失败:', error);
            resolve(0);
          } else {
            resolve(clients ? clients.length : 0);
          }
        });
      });
    } catch (error) {
      ctx.logger.error('获取连接客户端数量失败:', error);
      return 0;
    }
  }

  /**
   * 重发消息
   * @param ctx
   * @param pendingMessageKeys
   */
  async resendMessages(ctx, pendingMessageKeys) {
    const results = {
      success: 0,
      failed: 0,
      details: [],
    };

    const { app: { redis, io } } = ctx;
    const nsp = io.of('/');

    for (const messageKey of pendingMessageKeys) {
      try {
        const messageData = await this.getMessageData(redis, messageKey);

        if (!messageData) {
          results.failed++;
          continue;
        }

        // 解析消息数据
        const emitData = this.parseMessageData(messageData);
        if (!emitData) {
          results.failed++;
          continue;
        }

        // 发送消息
        await this.sendMessage(nsp, emitData);
        results.success++;

        // 记录重发详情
        results.details.push({
          messageKey,
          clientId: emitData[1]?.clientId,
          event: emitData[0],
          timestamp: new Date(),
        });

      } catch (error) {
        results.failed++;
        ctx.logger.error(`重发消息失败 ${messageKey}:`, error);
      }
    }

    return results;
  }

  /**
   * 获取消息数据
   * @param redis
   * @param messageKey
   */
  async getMessageData(redis, messageKey) {
    try {
      const data = await redis.get(messageKey);
      return data;
    } catch (error) {
      this.logger.error(`获取消息数据失败 ${messageKey}:`, error);
      return null;
    }
  }

  /**
   * 解析消息数据
   * @param messageData
   */
  parseMessageData(messageData) {
    try {
      const emitData = JSON.parse(messageData);

      // 验证数据格式
      if (!Array.isArray(emitData) || emitData.length < 2) {
        this.logger.warn('消息数据格式无效:', messageData);
        return null;
      }

      // 验证客户端ID
      if (!emitData[1] || !emitData[1].clientId) {
        this.logger.warn('消息缺少客户端ID:', messageData);
        return null;
      }

      return emitData;
    } catch (error) {
      this.logger.error('解析消息数据失败:', error);
      return null;
    }
  }

  /**
   * 发送消息
   * @param nsp
   * @param emitData
   */
  async sendMessage(nsp, emitData) {
    try {
      const { clientId } = emitData[1];
      const socket = nsp.to(clientId);

      // 使用spread操作符发送消息
      socket.emit(...emitData);

      this.logger.debug(`消息已重发到客户端 ${clientId}:`, emitData[0]);
    } catch (error) {
      this.logger.error('发送消息失败:', error);
      throw error;
    }
  }

  /**
   * 更新Socket键缓存
   * @param ctx
   * @param currentSocketKeys
   */
  updateSocketKeysCache(ctx, currentSocketKeys) {
    try {
      ctx.app.socketIdOnRedisKeys = currentSocketKeys;
      this.logger.debug(`Socket键缓存已更新，当前数量: ${currentSocketKeys.length}`);
    } catch (error) {
      this.logger.error('更新Socket键缓存失败:', error);
    }
  }

  /**
   * 获取Socket统计信息
   * @param ctx
   */
  async getSocketStats(ctx) {
    try {
      const { app: { redis, io } } = ctx;

      // 获取Redis中的Socket键数量
      const socketKeys = await this.getCurrentSocketKeys(ctx);

      // 获取连接的客户端数量
      const connectedClients = await this.getConnectedClientsCount(ctx);

      // 获取命名空间信息
      const nsp = io.of('/');
      const rooms = Object.keys(nsp.adapter.rooms || {});

      return {
        pendingMessages: socketKeys.length,
        connectedClients,
        activeRooms: rooms.length,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取Socket统计失败:', error);
      return null;
    }
  }

  /**
   * 清理过期的Socket消息
   * @param ctx
   * @param maxAge
   */
  async cleanupExpiredMessages(ctx, maxAge = 300000) { // 5分钟
    try {
      const { app: { redis }, helper } = ctx;
      const socketKeys = await redis.keys(helper.redisKeys.socketBaseSocketId('*'));
      let cleanedCount = 0;

      for (const key of socketKeys) {
        try {
          const ttl = await redis.ttl(key);
          if (ttl === -1) { // 没有过期时间的键
            await redis.expire(key, 300); // 设置5分钟过期
            cleanedCount++;
          }
        } catch (error) {
          // 忽略单个键的错误
        }
      }

      if (cleanedCount > 0) {
        ctx.logger.info(`清理了 ${cleanedCount} 个Socket消息的过期时间`);
      }

      return cleanedCount;
    } catch (error) {
      ctx.logger.error('清理过期Socket消息失败:', error);
      return 0;
    }
  }
}

// 创建任务实例
const resendNotAckSocketTask = new ResendNotAckSocketSchedule();

module.exports = {
  schedule: {
    interval: 1000 * 2, // 2秒间隔
    type: 'worker',
    disable: false,
    immediate: false,
  },
  async task(ctx) {
    // return await resendNotAckSocketTask.execute(ctx);
  },
};
