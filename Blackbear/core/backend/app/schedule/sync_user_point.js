'use strict';

const BaseSchedule = require('../utils/base-schedule');
const { Op } = require('sequelize');
const Sequelize = require('sequelize');

/**
 * 用户积分同步任务
 * 定期校准用户的积分总数，确保积分数据的准确性
 */
class SyncUserPointSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '用户积分同步任务',
      configKey: 'scheduleSyncUserPoint',
      skipLocal: false, // 本地也需要执行
      notifyOnError: true, // 积分数据重要，需要错误通知
      requiredServices: [ 'model.Point', 'model.User' ],
      batchSize: 50, // 批量处理用户
      maxRetries: 3,
    });
  }

  async run(ctx) {
    const stats = {
      totalUsers: 0,
      updatedUsers: 0,
      skippedUsers: 0,
      totalPointsProcessed: 0,
      errors: 0,
      inconsistencies: [],
    };

    try {
      ctx.logger.info('开始执行用户积分同步任务');

      // 获取用户积分汇总数据
      const pointSummary = await this.getUserPointSummary(ctx);
      stats.totalUsers = pointSummary.length;

      if (stats.totalUsers === 0) {
        ctx.logger.info('没有找到需要同步的用户积分数据');
        return stats;
      }

      ctx.logger.info(`找到 ${stats.totalUsers} 个用户的积分数据需要同步`);

      // 批量处理用户积分同步
      const { results, errors } = await this.batchProcess(
        pointSummary,
        async pointData => {
          return await this.syncUserPoint(ctx, pointData, stats);
        },
        ctx
      );

      stats.updatedUsers = results.filter(r => r === true).length;
      stats.skippedUsers = results.filter(r => r === false).length;
      stats.errors = errors.length;

      // 检查积分一致性
      await this.checkPointConsistency(ctx, stats);

      ctx.logger.info(`用户积分同步完成: 更新 ${stats.updatedUsers}, 跳过 ${stats.skippedUsers}, 错误 ${stats.errors}`);

      // 如果有不一致的数据，发送通知
      if (stats.inconsistencies.length > 0) {
        await this.notifyInconsistencies(ctx, stats);
      }

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('用户积分同步任务执行失败:', error);

      await this.sendNotification(
        ctx,
        '用户积分同步失败',
        `用户积分同步任务执行失败: ${error.message}`,
        'error'
      );

      throw error;
    }
  }

  /**
   * 获取用户积分汇总数据
   * @param ctx
   */
  async getUserPointSummary(ctx) {
    try {
      const pointSummary = await ctx.model.Point.findAll({
        attributes: [
          'userId',
          [ Sequelize.fn('SUM', Sequelize.col('score')), 'totalPoint' ],
          [ Sequelize.fn('COUNT', Sequelize.col('id')), 'pointCount' ],
        ],
        group: 'userId',
        having: Sequelize.where(
          Sequelize.fn('SUM', Sequelize.col('score')),
          Op.gt,
          0
        ),
        raw: true,
      });

      return pointSummary || [];

    } catch (error) {
      ctx.logger.error('获取用户积分汇总失败:', error);
      return [];
    }
  }

  /**
   * 同步单个用户的积分
   * @param ctx
   * @param pointData
   * @param stats
   */
  async syncUserPoint(ctx, pointData, stats) {
    try {
      const { userId } = pointData;
      const calculatedPoint = this.safeNumber(pointData.totalPoint, 0);
      const pointCount = this.safeNumber(pointData.pointCount, 0);

      if (!userId) {
        ctx.logger.warn('用户ID为空，跳过处理');
        return false;
      }

      // 获取用户当前积分
      const user = await ctx.model.User.findByPk(userId, {
        attributes: [ 'id', 'point', 'username' ],
      });

      if (!user) {
        ctx.logger.warn(`用户 ${userId} 不存在，跳过积分同步`);
        return false;
      }

      const currentPoint = this.safeNumber(user.point, 0);

      // 检查是否需要更新
      if (currentPoint === calculatedPoint) {
        ctx.logger.debug(`用户 ${userId} 积分已是最新 (${currentPoint})`);
        return false;
      }

      // 记录不一致的情况
      if (Math.abs(currentPoint - calculatedPoint) > 0) {
        stats.inconsistencies.push({
          userId,
          username: user.username,
          currentPoint,
          calculatedPoint,
          difference: calculatedPoint - currentPoint,
          pointCount,
        });
      }

      // 更新用户积分
      await user.update({
        point: calculatedPoint,
      });

      stats.totalPointsProcessed += calculatedPoint;

      ctx.logger.debug(`用户 ${userId} 积分已更新: ${currentPoint} -> ${calculatedPoint}`);
      return true;

    } catch (error) {
      ctx.logger.error(`同步用户 ${pointData.userId} 积分失败:`, error);
      throw error;
    }
  }

  /**
   * 检查积分一致性
   * @param ctx
   * @param stats
   */
  async checkPointConsistency(ctx, stats) {
    try {
      // 检查是否有用户积分为负数
      const negativePointUsers = await ctx.model.User.findAll({
        where: {
          point: { [Op.lt]: 0 },
        },
        attributes: [ 'id', 'username', 'point' ],
        limit: 10,
      });

      if (negativePointUsers.length > 0) {
        ctx.logger.warn(`发现 ${negativePointUsers.length} 个用户积分为负数`);
        stats.inconsistencies.push(...negativePointUsers.map(user => ({
          userId: user.id,
          username: user.username,
          issue: 'negative_points',
          currentPoint: user.point,
        })));
      }

      // 检查是否有积分记录但用户积分为0的情况
      const zeroPointUsers = await ctx.model.User.findAll({
        where: {
          point: 0,
        },
        include: [{
          model: ctx.model.Point,
          as: 'points',
          where: {
            score: { [Op.gt]: 0 },
          },
          required: true,
        }],
        attributes: [ 'id', 'username', 'point' ],
        limit: 5,
      });

      if (zeroPointUsers.length > 0) {
        ctx.logger.warn(`发现 ${zeroPointUsers.length} 个用户有积分记录但总积分为0`);
      }

    } catch (error) {
      ctx.logger.error('检查积分一致性失败:', error);
    }
  }

  /**
   * 通知积分不一致情况
   * @param ctx
   * @param stats
   */
  async notifyInconsistencies(ctx, stats) {
    try {
      if (stats.inconsistencies.length === 0) { return; }

      const majorInconsistencies = stats.inconsistencies
        .filter(item => Math.abs(item.difference || 0) > 100)
        .slice(0, 5);

      if (majorInconsistencies.length > 0) {
        const content = `发现 ${stats.inconsistencies.length} 个积分不一致的用户，其中 ${majorInconsistencies.length} 个差异较大：\n${
          majorInconsistencies.map(item =>
            `用户 ${item.username}: ${item.currentPoint} -> ${item.calculatedPoint} (差异: ${item.difference})`
          ).join('\n')}`;

        await this.sendNotification(
          ctx,
          '用户积分不一致警告',
          content,
          'warning'
        );
      }

    } catch (error) {
      ctx.logger.error('发送积分不一致通知失败:', error);
    }
  }

  /**
   * 获取积分统计信息
   * @param ctx
   */
  async getPointStatistics(ctx) {
    try {
      const [ totalUsers, totalPoints, avgPoints ] = await Promise.all([
        ctx.model.User.count({
          where: {
            point: { [Op.gt]: 0 },
          },
        }),
        ctx.model.User.sum('point'),
        ctx.model.User.findOne({
          attributes: [
            [ Sequelize.fn('AVG', Sequelize.col('point')), 'avgPoint' ],
          ],
          raw: true,
        }),
      ]);

      return {
        totalUsers,
        totalPoints: this.safeNumber(totalPoints, 0),
        averagePoints: this.safeNumber(avgPoints?.avgPoint, 0, 2),
      };

    } catch (error) {
      ctx.logger.error('获取积分统计失败:', error);
      return null;
    }
  }
}

// 创建任务实例
const syncUserPointTask = new SyncUserPointSchedule();

module.exports = {
  schedule: {
    cron: '0 30 1 * * *', // 每天凌晨1:30执行
    type: 'worker',
    disable: false,
    immediate: true,
  },
  async task(ctx) {
    return await syncUserPointTask.execute(ctx);
  },
};
