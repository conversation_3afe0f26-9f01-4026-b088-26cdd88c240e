'use strict';

const BaseSchedule = require('../utils/base-schedule');

/**
 * 百度SEO推送任务
 * 定期向百度搜索引擎推送网站链接，提升SEO效果
 */
class BaiduSeoSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '百度SEO推送任务',
      configKey: 'scheduleBaiduSeo',
      skipLocal: true,
      notifyOnError: true,
      requiredServices: [ 'service.baidu' ],
      maxRetries: 3,
      retryDelay: 5000,
    });
  }

  async run(ctx) {
    const stats = {
      totalUrls: 0,
      successUrls: 0,
      failedUrls: 0,
      errors: 0,
    };

    try {
      ctx.logger.info('开始执行百度SEO推送');

      // 检查百度服务是否可用
      if (!ctx.service.baidu || !ctx.service.baidu.pushAll) {
        throw new Error('百度推送服务不可用');
      }

      // 执行推送，带重试机制
      const result = await this.executeWithRetry(
        async () => {
          return await ctx.service.baidu.pushAll();
        },
        '百度SEO推送',
        ctx
      );

      // 处理推送结果
      if (result) {
        stats.totalUrls = result.totalUrls || 0;
        stats.successUrls = result.successUrls || 0;
        stats.failedUrls = result.failedUrls || 0;

        ctx.logger.info(`百度SEO推送完成: 总计 ${stats.totalUrls}, 成功 ${stats.successUrls}, 失败 ${stats.failedUrls}`);

        // 如果有失败的URL，发送通知
        if (stats.failedUrls > 0) {
          await this.sendNotification(
            ctx,
            '百度SEO推送部分失败',
            `推送完成，但有 ${stats.failedUrls} 个URL推送失败，请检查日志。`,
            'warning'
          );
        }
      } else {
        ctx.logger.warn('百度SEO推送返回空结果');
      }

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('百度SEO推送任务执行失败:', error);

      // 发送错误通知
      await this.sendNotification(
        ctx,
        '百度SEO推送任务失败',
        `百度SEO推送任务执行失败: ${error.message}`,
        'error'
      );

      throw error;
    }
  }

  /**
   * 检查推送配置
   * @param ctx
   */
  async checkPushConfig(ctx) {
    try {
      const config = await this.getConfig(ctx, 'baidu');
      if (!config || !config.pushUrl || !config.token) {
        throw new Error('百度推送配置不完整，缺少pushUrl或token');
      }
      return config;
    } catch (error) {
      ctx.logger.error('检查百度推送配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取推送统计
   * @param ctx
   */
  async getPushStats(ctx) {
    try {
      // 可以从数据库或缓存中获取推送统计信息
      const today = this.formatDate(new Date(), 'YYYY-MM-DD');

      // 这里可以扩展为从数据库查询今日推送统计
      return {
        date: today,
        totalPushed: 0,
        lastPushTime: null,
      };
    } catch (error) {
      ctx.logger.error('获取推送统计失败:', error);
      return null;
    }
  }
}

// 创建任务实例
const baiduSeoTask = new BaiduSeoSchedule();

module.exports = {
  schedule: {
    cron: '0 0 01 * * *', // 每天凌晨1点执行
    type: 'worker',
    immediate: false,
  },
  async task(ctx) {
    return await baiduSeoTask.execute(ctx);
  },
};
