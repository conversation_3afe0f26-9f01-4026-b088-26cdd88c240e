'use strict';

const BaseSchedule = require('../utils/base-schedule');
const { Op } = require('sequelize');

/**
 * 聚合数据创建任务
 * 为特定用户创建新闻、媒体、研报的聚合数据
 */
class AggregationCreateSchedule extends BaseSchedule {
  constructor() {
    super({
      taskName: '聚合数据创建任务',
      configKey: 'scheduleAggregation',
      skipLocal: false, // 本地也可以执行
      batchSize: 50,
      requiredServices: [ 'model.Aggregation', 'model.User', 'model.News', 'model.Media', 'model.Research' ],
    });
  }

  async run(ctx) {
    const stats = {
      cleanedCount: 0,
      newsProcessed: 0,
      mediaProcessed: 0,
      researchProcessed: 0,
      aggregationsCreated: 0,
      errors: 0,
    };

    try {
      // 1. 清理已读的聚合数据
      stats.cleanedCount = await this.cleanReadAggregations(ctx);

      // 2. 获取目标用户
      const users = await this.getTargetUsers(ctx);
      if (users.length === 0) {
        ctx.logger.warn('没有找到目标用户，跳过聚合数据创建');
        return stats;
      }

      ctx.logger.info(`找到 ${users.length} 个目标用户`);

      // 3. 处理新闻聚合
      stats.newsProcessed = await this.processNewsAggregation(ctx, users, stats);

      // 4. 处理媒体聚合
      stats.mediaProcessed = await this.processMediaAggregation(ctx, users, stats);

      // 5. 处理研报聚合
      stats.researchProcessed = await this.processResearchAggregation(ctx, users, stats);

      await this.recordStats(ctx, stats);
      return stats;

    } catch (error) {
      stats.errors++;
      ctx.logger.error('聚合数据创建任务执行失败:', error);
      throw error;
    }
  }

  /**
   * 清理已读的聚合数据
   * @param ctx
   */
  async cleanReadAggregations(ctx) {
    try {
      const result = await ctx.model.Aggregation.destroy({
        where: {
          read: true,
        },
        force: true,
      });

      ctx.logger.info(`清理了 ${result} 条已读聚合数据`);
      return result;

    } catch (error) {
      ctx.logger.error('清理已读聚合数据失败:', error);
      return 0;
    }
  }

  /**
   * 获取目标用户
   * @param ctx
   */
  async getTargetUsers(ctx) {
    try {
      const users = await ctx.model.User.findAll({
        where: {
          roleId: {
            [Op.in]: [ 'partner', 'admin.top', 'supervip' ],
          },
          isBlock: false,
        },
        attributes: [ 'id', 'username', 'roleId' ],
        limit: 1000,
      });

      return users;

    } catch (error) {
      ctx.logger.error('获取目标用户失败:', error);
      return [];
    }
  }

  /**
   * 处理新闻聚合
   * @param ctx
   * @param users
   * @param stats
   */
  async processNewsAggregation(ctx, users, stats) {
    return await this.processContentAggregation(ctx, users, {
      modelName: 'News',
      type: 'news',
      idField: 'newsID',
      orderField: 'newsID',
      foreignKey: 'newsId',
      limit: 100,
    }, stats);
  }

  /**
   * 处理媒体聚合
   * @param ctx
   * @param users
   * @param stats
   */
  async processMediaAggregation(ctx, users, stats) {
    return await this.processContentAggregation(ctx, users, {
      modelName: 'Media',
      type: 'media',
      idField: 'id',
      orderField: 'id',
      foreignKey: 'mediaId',
      limit: 100,
    }, stats);
  }

  /**
   * 处理研报聚合
   * @param ctx
   * @param users
   * @param stats
   */
  async processResearchAggregation(ctx, users, stats) {
    return await this.processContentAggregation(ctx, users, {
      modelName: 'Research',
      type: 'research',
      idField: 'id',
      orderField: 'id',
      foreignKey: 'researchId',
      limit: 100,
      extraWhere: { important: true },
    }, stats);
  }

  /**
   * 通用的内容聚合处理
   * @param ctx
   * @param users
   * @param config
   * @param stats
   */
  async processContentAggregation(ctx, users, config, stats) {
    try {
      const { modelName, type, idField, orderField, foreignKey, limit, extraWhere = {} } = config;

      // 获取未聚合的内容
      const contents = await ctx.model[modelName].findAll({
        where: {
          aggregation: false,
          ...extraWhere,
        },
        order: [[ orderField, 'ASC' ]],
        limit,
      });

      if (contents.length === 0) {
        ctx.logger.info(`没有找到待聚合的${type}内容`);
        return 0;
      }

      ctx.logger.info(`开始处理 ${contents.length} 条${type}聚合`);

      // 批量处理内容
      const { results, errors } = await this.batchProcess(
        contents,
        async content => {
          return await this.createContentAggregations(ctx, content, users, {
            type,
            idField,
            foreignKey,
          });
        },
        ctx
      );

      stats.errors += errors.length;
      stats.aggregationsCreated += results.reduce((sum, count) => sum + count, 0);

      ctx.logger.info(`${type}聚合处理完成: 成功 ${results.length}, 失败 ${errors.length}`);
      return contents.length;

    } catch (error) {
      ctx.logger.error(`处理${config.type}聚合失败:`, error);
      stats.errors++;
      return 0;
    }
  }

  /**
   * 为单个内容创建聚合数据
   * @param ctx
   * @param content
   * @param users
   * @param config
   */
  async createContentAggregations(ctx, content, users, config) {
    const { type, idField, foreignKey } = config;
    const contentId = content.get(idField);

    return await this.safeTransaction(ctx, async transaction => {
      let createdCount = 0;

      // 为每个用户创建聚合记录
      for (const user of users) {
        try {
          const aggregationData = {
            type,
            [foreignKey]: contentId,
            userId: user.get('id'),
          };

          await ctx.model.Aggregation.create(aggregationData, { transaction });
          createdCount++;

        } catch (error) {
          // 可能是重复记录，记录警告但继续
          ctx.logger.warn(`为用户 ${user.get('id')} 创建${type}聚合失败:`, error.message);
        }
      }

      // 标记内容为已聚合
      await content.update({ aggregation: true }, { transaction });

      return createdCount;
    }, `创建${type}聚合数据`);
  }
}

// 创建任务实例
const aggregationCreateTask = new AggregationCreateSchedule();

module.exports = {
  schedule: {
    interval: '1m',
    type: 'worker',
    immediate: true,
  },
  async task(ctx) {
    return await aggregationCreateTask.execute(ctx);
  },
};
