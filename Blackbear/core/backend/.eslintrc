{"extends": "eslint-config-egg", "env": {"mocha": true, "node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "script"}, "rules": {"jsdoc/check-tag-names": "off", "no-restricted-modules": "off", "no-case-declarations": "error", "no-unused-vars": ["error", {"vars": "all", "args": "after-used", "ignoreRestSiblings": false, "argsIgnorePattern": "^_"}], "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-arrow-callback": "warn", "arrow-spacing": "error", "no-duplicate-imports": "error", "no-useless-constructor": "error", "no-useless-rename": "error", "rest-spread-spacing": "error", "template-curly-spacing": "error", "yield-star-spacing": "error", "prefer-template": "warn", "prefer-destructuring": ["warn", {"array": false, "object": true}], "no-console": ["warn", {"allow": ["warn", "error", "info"]}], "consistent-return": "error", "curly": ["error", "all"], "eqeqeq": ["error", "always"], "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-script-url": "error", "no-self-compare": "error", "no-sequences": "error", "no-throw-literal": "error", "no-unmodified-loop-condition": "error", "no-unused-expressions": "error", "no-useless-call": "error", "no-useless-concat": "error", "no-void": "error", "radix": "error", "wrap-iife": ["error", "inside"], "yoda": "error"}, "globals": {"app": "readonly", "ctx": "readonly"}}