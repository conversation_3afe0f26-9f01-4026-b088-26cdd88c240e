{"name": "<PERSON>bear-backend", "version": "1.0.0", "description": "秋实系统", "private": true, "egg": {"declarations": true}, "dependencies": {"@alicloud/pop-core": "^1.7.13", "ali-oss": "^6.20.0", "axios": "^1.7.7", "cls-hooked": "^4.2.2", "continuation-local-storage": "^3.2.1", "crypto": "^1.0.1", "debug": "^4.4.1", "egg": "^3.21.0", "egg-cors": "^3.0.1", "egg-healthy": "^1.0.0", "egg-jwt": "^2.0.0", "egg-mailer": "^1.5.0", "egg-minio": "^1.0.5", "egg-mysql": "3.0.0", "egg-oss": "^2.0.0", "egg-pandora-script": "^1.0.0", "egg-passport": "^0.0.1", "egg-passport-local": "^1.2.1", "egg-passport-weibo": "^1.0.0", "egg-redis": "^2.4.0", "egg-rest": "^2.1.0", "egg-router-plus": "^1.3.1", "egg-scripts": "^2.13.0", "egg-sequelize": "^6.0.0", "egg-session": "^3.2.0", "egg-session-redis": "^2.1.0", "egg-socket.io": "^4.1.6", "egg-swagger-doc": "^2.3.2", "egg-validate": "^2.0.2", "egg-wechat-api": "^1.1.0", "engine.io": "^6.6.4", "form-data": "^4.0.0", "generic-pool": "^3.9.0", "joi": "^17.13.3", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "koa2-ratelimit": "^1.1.1", "lodash": "^4.17.21", "markdown-to-txt": "^2.0.1", "marked": "^4.0.12", "md5": "^2.3.0", "moment": "^2.30.1", "mysql2": "^3.11.3", "node-rsa": "^1.1.1", "pandora": "^0.1.28", "pinyin": "^2.9.1", "socket.io": "^4.8.1", "svg-captcha": "^1.4.0", "uuid": "^10.0.0", "validator": "^13.15.15", "weibo": "^0.6.9", "xss": "^1.0.15"}, "devDependencies": {"autod": "^2.4.2", "autod-egg": "^1.1.0", "egg-bin": "^6.13.0", "egg-ci": "^2.1.0", "egg-mock": "^5.15.0", "eslint": "^8.57.0", "eslint-config-egg": "^14.1.0", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --sticky", "stop": "egg-scripts stop --title=egg-server-<PERSON><PERSON><PERSON>", "dev": "egg-bin dev --sticky", "debug": "egg-bin debug", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "lint:fix": "eslint . --fix", "security-audit": "npm audit --audit-level=moderate", "security-fix": "npm audit fix", "security-check": "npm run security-audit && npm run lint", "ci": "npm run security-check && npm run cov", "autod": "autod", "prestart": "npm run security-check", "test:unit": "egg-bin test test/app/service test/app/utils", "test:integration": "egg-bin test test/app/controller", "test:performance": "node test/performance/benchmark.test.js", "fix-quality": "node scripts/fix-code-quality.js", "benchmark": "node scripts/performance-benchmark.js", "health-check": "curl -f http://localhost:7001/api/health || exit 1", "docker:build": "docker build -f Dockerfile.optimized -t blackbear-backend:latest .", "docker:run": "docker-compose -f docker-compose.production.yml up -d", "docker:stop": "docker-compose -f docker-compose.production.yml down", "docker:logs": "docker-compose -f docker-compose.production.yml logs -f blackbear-backend"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "chen", "license": "MIT"}