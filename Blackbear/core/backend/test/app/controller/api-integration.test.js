'use strict';

const { app, assert } = require('egg-mock/bootstrap');

describe('test/app/controller/api-integration.test.js', () => {
  let token;
  let userId;

  before(async () => {
    // 创建测试用户并获取token
    const userResponse = await app.httpRequest()
      .post('/api/auth/register')
      .send({
        username: 'testuser',
        password: 'TestPass123!',
        email: '<EMAIL>',
      })
      .expect(200);

    userId = userResponse.body.data.id;

    // 登录获取token
    const loginResponse = await app.httpRequest()
      .post('/api/auth/login')
      .send({
        username: 'testuser',
        password: 'TestPass123!',
      })
      .expect(200);

    token = loginResponse.body.data.token;
  });

  after(async () => {
    // 清理测试数据
    if (userId) {
      await app.model.User.destroy({ where: { id: userId } });
    }
  });

  describe('Authentication API', () => {
    it('should register new user', async () => {
      const response = await app.httpRequest()
        .post('/api/auth/register')
        .send({
          username: 'newuser',
          password: 'NewPass123!',
          email: '<EMAIL>',
        })
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.id);
      assert(response.body.data.username === 'newuser');

      // 清理
      await app.model.User.destroy({ where: { id: response.body.data.id } });
    });

    it('should login with valid credentials', async () => {
      const response = await app.httpRequest()
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'TestPass123!',
        })
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.token);
      assert(response.body.data.user.username === 'testuser');
    });

    it('should reject invalid credentials', async () => {
      await app.httpRequest()
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword',
        })
        .expect(401);
    });

    it('should validate token', async () => {
      const response = await app.httpRequest()
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.username === 'testuser');
    });
  });

  describe('User Management API', () => {
    it('should get user profile', async () => {
      const response = await app.httpRequest()
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.username === 'testuser');
    });

    it('should update user profile', async () => {
      const response = await app.httpRequest()
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .send({
          nickname: 'Updated Nickname',
          bio: 'Updated bio',
        })
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.nickname === 'Updated Nickname');
    });

    it('should change password', async () => {
      const response = await app.httpRequest()
        .post('/api/users/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          oldPassword: 'TestPass123!',
          newPassword: 'NewTestPass123!',
        })
        .expect(200);

      assert(response.body.success === true);

      // 验证新密码可以登录
      const loginResponse = await app.httpRequest()
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'NewTestPass123!',
        })
        .expect(200);

      assert(loginResponse.body.success === true);

      // 恢复原密码
      await app.httpRequest()
        .post('/api/users/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          oldPassword: 'NewTestPass123!',
          newPassword: 'TestPass123!',
        })
        .expect(200);
    });
  });

  describe('Stock API', () => {
    let stockId;

    before(async () => {
      // 创建测试股票
      const stock = await app.model.Stock.create({
        code: 'TEST001',
        name: '测试股票',
        market: 'SZ',
        industry: '测试行业',
        status: 1,
      });
      stockId = stock.id;
    });

    after(async () => {
      // 清理测试股票
      if (stockId) {
        await app.model.Stock.destroy({ where: { id: stockId } });
      }
    });

    it('should get stock list', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(Array.isArray(response.body.data.list));
      assert(typeof response.body.data.total === 'number');
    });

    it('should get stock detail', async () => {
      const response = await app.httpRequest()
        .get(`/api/stocks/${stockId}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.id === stockId);
      assert(response.body.data.code === 'TEST001');
    });

    it('should search stocks', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks/search')
        .query({ keyword: '测试' })
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(Array.isArray(response.body.data));
    });
  });

  describe('News API', () => {
    let newsId;

    before(async () => {
      // 创建测试新闻
      const news = await app.model.News.create({
        title: '测试新闻标题',
        content: '测试新闻内容',
        source: '测试来源',
        url: 'https://example.com/test-news',
        publishTime: new Date(),
        status: 1,
      });
      newsId = news.id;
    });

    after(async () => {
      // 清理测试新闻
      if (newsId) {
        await app.model.News.destroy({ where: { id: newsId } });
      }
    });

    it('should get news list', async () => {
      const response = await app.httpRequest()
        .get('/api/news')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(Array.isArray(response.body.data.list));
      assert(typeof response.body.data.total === 'number');
    });

    it('should get news detail', async () => {
      const response = await app.httpRequest()
        .get(`/api/news/${newsId}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(response.body.data.id === newsId);
      assert(response.body.data.title === '测试新闻标题');
    });

    it('should search news', async () => {
      const response = await app.httpRequest()
        .get('/api/news/search')
        .query({ keyword: '测试' })
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.body.success === true);
      assert(Array.isArray(response.body.data));
    });
  });

  describe('Performance and Caching', () => {
    it('should have response time headers', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      assert(response.headers['x-response-time']);
      const responseTime = parseInt(response.headers['x-response-time']);
      assert(responseTime > 0);
    });

    it('should have cache headers for cacheable endpoints', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // 检查缓存相关头部
      assert(response.headers['cache-control'] || response.headers['etag']);
    });

    it('should compress large responses', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks')
        .set('Authorization', `Bearer ${token}`)
        .set('Accept-Encoding', 'gzip, deflate')
        .expect(200);

      // 如果响应足够大，应该被压缩
      if (JSON.stringify(response.body).length > 1024) {
        assert(response.headers['content-encoding']);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for non-existent resources', async () => {
      await app.httpRequest()
        .get('/api/stocks/999999')
        .set('Authorization', `Bearer ${token}`)
        .expect(404);
    });

    it('should handle unauthorized access', async () => {
      await app.httpRequest()
        .get('/api/users/profile')
        .expect(401);
    });

    it('should handle invalid token', async () => {
      await app.httpRequest()
        .get('/api/users/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should handle validation errors', async () => {
      const response = await app.httpRequest()
        .post('/api/auth/register')
        .send({
          username: '', // 空用户名
          password: '123', // 弱密码
          email: 'invalid-email', // 无效邮箱
        })
        .expect(400);

      assert(response.body.success === false);
      assert(response.body.message);
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const requests = [];
      
      // 发送大量请求测试限流
      for (let i = 0; i < 10; i++) {
        requests.push(
          app.httpRequest()
            .get('/api/stocks')
            .set('Authorization', `Bearer ${token}`)
        );
      }

      const responses = await Promise.all(requests);
      
      // 检查是否有请求被限流
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      // 根据配置，可能会有一些请求被限流
      assert(rateLimitedResponses.length >= 0);
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // 检查安全头部
      assert(response.headers['x-content-type-options'] === 'nosniff');
      assert(response.headers['x-frame-options'] === 'DENY');
      assert(response.headers['x-xss-protection'] === '1; mode=block');
    });
  });
});
