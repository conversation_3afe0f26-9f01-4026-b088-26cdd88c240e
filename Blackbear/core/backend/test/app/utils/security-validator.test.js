'use strict';

const { app, assert } = require('egg-mock/bootstrap');
const SecurityValidator = require('../../../app/utils/security-validator');

describe('test/app/utils/security-validator.test.js', () => {

  describe('sanitizeSqlInput()', () => {
    it('should remove dangerous SQL characters', () => {
      const input = "'; DROP TABLE users; --";
      const result = SecurityValidator.sanitizeSqlInput(input);
      assert(result === ' DROP TABLE users --');
    });

    it('should handle non-string input', () => {
      assert(SecurityValidator.sanitizeSqlInput(123) === 123);
      assert(SecurityValidator.sanitizeSqlInput(null) === null);
      assert(SecurityValidator.sanitizeSqlInput(undefined) === undefined);
    });

    it('should trim whitespace', () => {
      const input = '  normal input  ';
      const result = SecurityValidator.sanitizeSqlInput(input);
      assert(result === 'normal input');
    });
  });

  describe('sanitizeHtmlInput()', () => {
    it('should remove dangerous HTML tags', () => {
      const input = '<script>alert("xss")</script><p>Safe content</p>';
      const result = SecurityValidator.sanitizeHtmlInput(input);
      assert(!result.includes('<script>'));
      assert(result.includes('<p>Safe content</p>'));
    });

    it('should handle custom options', () => {
      const input = '<div>Test</div><p>Content</p>';
      const options = {
        whiteList: {
          div: [],
        },
      };
      const result = SecurityValidator.sanitizeHtmlInput(input, options);
      assert(result.includes('<div>Test</div>'));
    });

    it('should handle non-string input', () => {
      assert(SecurityValidator.sanitizeHtmlInput(123) === 123);
      assert(SecurityValidator.sanitizeHtmlInput(null) === null);
    });
  });

  describe('maskSensitiveData()', () => {
    it('should mask default sensitive fields', () => {
      const data = {
        username: 'testuser',
        password: 'secret123',
        token: 'abc123def456',
        email: '<EMAIL>',
      };

      const result = SecurityValidator.maskSensitiveData(data);

      assert(result.username === 'testuser');
      assert(result.password === 'se***23');
      assert(result.token === 'ab***56');
      assert(result.email === '<EMAIL>');
    });

    it('should mask custom fields', () => {
      const data = {
        username: 'testuser',
        apiKey: 'secret-api-key',
        publicInfo: 'visible',
      };

      const result = SecurityValidator.maskSensitiveData(data, [ 'apiKey' ]);

      assert(result.username === 'testuser');
      assert(result.apiKey === 'se***ey');
      assert(result.publicInfo === 'visible');
    });

    it('should handle short strings', () => {
      const data = { password: 'abc' };
      const result = SecurityValidator.maskSensitiveData(data);
      assert(result.password === '***');
    });

    it('should handle non-string values', () => {
      const data = {
        password: 123,
        token: null,
        secret: undefined,
      };
      const result = SecurityValidator.maskSensitiveData(data);
      assert(result.password === '***');
      assert(result.token === '***');
      assert(result.secret === '***');
    });

    it('should handle non-object input', () => {
      assert(SecurityValidator.maskSensitiveData(null) === null);
      assert(SecurityValidator.maskSensitiveData('string') === 'string');
      assert(SecurityValidator.maskSensitiveData(123) === 123);
    });
  });

  describe('isValidEmail()', () => {
    it('should validate correct email addresses', () => {
      assert(SecurityValidator.isValidEmail('<EMAIL>') === true);
      assert(SecurityValidator.isValidEmail('<EMAIL>') === true);
    });

    it('should reject invalid email addresses', () => {
      assert(SecurityValidator.isValidEmail('invalid-email') === false);
      assert(SecurityValidator.isValidEmail('@domain.com') === false);
      assert(SecurityValidator.isValidEmail('user@') === false);
    });
  });

  describe('isValidPhone()', () => {
    it('should validate Chinese mobile numbers', () => {
      assert(SecurityValidator.isValidPhone('13812345678') === true);
      assert(SecurityValidator.isValidPhone('15987654321') === true);
      assert(SecurityValidator.isValidPhone('18611112222') === true);
    });

    it('should reject invalid phone numbers', () => {
      assert(SecurityValidator.isValidPhone('12345678901') === false);
      assert(SecurityValidator.isValidPhone('1381234567') === false);
      assert(SecurityValidator.isValidPhone('abc12345678') === false);
    });
  });

  describe('isValidUrl()', () => {
    it('should validate correct URLs', () => {
      assert(SecurityValidator.isValidUrl('https://example.com') === true);
      assert(SecurityValidator.isValidUrl('http://subdomain.example.com/path') === true);
    });

    it('should reject invalid URLs', () => {
      assert(SecurityValidator.isValidUrl('ftp://example.com') === false);
      assert(SecurityValidator.isValidUrl('example.com') === false);
      assert(SecurityValidator.isValidUrl('javascript:alert(1)') === false);
    });
  });

  describe('validatePasswordStrength()', () => {
    it('should validate strong passwords', () => {
      const result = SecurityValidator.validatePasswordStrength('StrongPass123!');
      assert(result.isValid === true);
      assert(result.score === 5);
      assert(result.issues.length === 0);
    });

    it('should identify weak passwords', () => {
      const result = SecurityValidator.validatePasswordStrength('weak');
      assert(result.isValid === false);
      assert(result.score < 3);
      assert(result.issues.length > 0);
    });

    it('should check password length', () => {
      const result = SecurityValidator.validatePasswordStrength('short');
      assert(result.issues.includes('密码长度至少8位'));
    });

    it('should check for numbers', () => {
      const result = SecurityValidator.validatePasswordStrength('NoNumbers');
      assert(result.issues.includes('密码应包含数字'));
    });

    it('should check for lowercase letters', () => {
      const result = SecurityValidator.validatePasswordStrength('NOLOWERCASE123');
      assert(result.issues.includes('密码应包含小写字母'));
    });

    it('should check for uppercase letters', () => {
      const result = SecurityValidator.validatePasswordStrength('nouppercase123');
      assert(result.issues.includes('密码应包含大写字母'));
    });

    it('should check for special characters', () => {
      const result = SecurityValidator.validatePasswordStrength('NoSpecial123');
      assert(result.issues.includes('密码应包含特殊字符'));
    });

    it('should handle null/undefined input', () => {
      const result1 = SecurityValidator.validatePasswordStrength(null);
      const result2 = SecurityValidator.validatePasswordStrength(undefined);

      assert(result1.isValid === false);
      assert(result2.isValid === false);
      assert(result1.issues.includes('密码不能为空'));
      assert(result2.issues.includes('密码不能为空'));
    });
  });

  describe('isValidJwtFormat()', () => {
    it('should validate JWT format', () => {
      const validJwt = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      assert(SecurityValidator.isValidJwtFormat(validJwt) === true);
    });

    it('should reject invalid JWT format', () => {
      assert(SecurityValidator.isValidJwtFormat('invalid.jwt') === false);
      assert(SecurityValidator.isValidJwtFormat('too.many.parts.here') === false);
      assert(SecurityValidator.isValidJwtFormat('') === false);
      assert(SecurityValidator.isValidJwtFormat(null) === false);
    });
  });

  describe('sanitizeFilename()', () => {
    it('should remove dangerous characters from filename', () => {
      const filename = 'file<>:"/\\|?*name.txt';
      const result = SecurityValidator.sanitizeFilename(filename);
      assert(result === 'filename.txt');
    });

    it('should handle empty/null input', () => {
      assert(SecurityValidator.sanitizeFilename('') === '');
      assert(SecurityValidator.sanitizeFilename(null) === '');
      assert(SecurityValidator.sanitizeFilename(undefined) === '');
    });

    it('should trim whitespace', () => {
      const filename = '  normal-file.txt  ';
      const result = SecurityValidator.sanitizeFilename(filename);
      assert(result === 'normal-file.txt');
    });
  });

  describe('isValidIP()', () => {
    it('should validate IPv4 addresses', () => {
      assert(SecurityValidator.isValidIP('***********') === true);
      assert(SecurityValidator.isValidIP('127.0.0.1') === true);
      assert(SecurityValidator.isValidIP('***************') === true);
    });

    it('should validate IPv6 addresses', () => {
      assert(SecurityValidator.isValidIP('2001:0db8:85a3:0000:0000:8a2e:0370:7334') === true);
      assert(SecurityValidator.isValidIP('::1') === true);
    });

    it('should reject invalid IP addresses', () => {
      assert(SecurityValidator.isValidIP('256.256.256.256') === false);
      assert(SecurityValidator.isValidIP('not.an.ip') === false);
      assert(SecurityValidator.isValidIP('') === false);
    });
  });

  describe('isSafeRedirectUrl()', () => {
    it('should allow safe redirect URLs', () => {
      assert(SecurityValidator.isSafeRedirectUrl('https://example.com/path') === true);
      assert(SecurityValidator.isSafeRedirectUrl('http://localhost:3000') === true);
    });

    it('should reject dangerous protocols', () => {
      assert(SecurityValidator.isSafeRedirectUrl('javascript:alert(1)') === false);
      assert(SecurityValidator.isSafeRedirectUrl('data:text/html,<script>alert(1)</script>') === false);
      assert(SecurityValidator.isSafeRedirectUrl('ftp://example.com') === false);
    });

    it('should check domain whitelist', () => {
      const allowedDomains = [ 'example.com', 'trusted.com' ];

      assert(SecurityValidator.isSafeRedirectUrl('https://example.com/path', allowedDomains) === true);
      assert(SecurityValidator.isSafeRedirectUrl('https://trusted.com/path', allowedDomains) === true);
      assert(SecurityValidator.isSafeRedirectUrl('https://malicious.com/path', allowedDomains) === false);
    });

    it('should handle invalid URLs', () => {
      assert(SecurityValidator.isSafeRedirectUrl('not-a-url') === false);
      assert(SecurityValidator.isSafeRedirectUrl('') === false);
      assert(SecurityValidator.isSafeRedirectUrl(null) === false);
    });
  });

  describe('generateSecureRandomString()', () => {
    it('should generate random string of specified length', () => {
      const result1 = SecurityValidator.generateSecureRandomString(16);
      const result2 = SecurityValidator.generateSecureRandomString(16);

      assert(result1.length === 32); // hex encoding doubles length
      assert(result2.length === 32);
      assert(result1 !== result2); // should be different
    });

    it('should use default length', () => {
      const result = SecurityValidator.generateSecureRandomString();
      assert(result.length === 64); // 32 bytes * 2 for hex
    });

    it('should generate different strings each time', () => {
      const results = new Set();
      for (let i = 0; i < 10; i++) {
        results.add(SecurityValidator.generateSecureRandomString(8));
      }
      assert(results.size === 10); // all should be unique
    });
  });
});
