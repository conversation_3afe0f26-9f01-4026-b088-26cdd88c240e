'use strict';

const { app, assert } = require('egg-mock/bootstrap');

describe('test/app/service/base/advanced-cache.test.js', () => {
  let ctx;
  let service;

  beforeEach(() => {
    ctx = app.mockContext();
    service = ctx.service.base.advancedCache;

    // 清理缓存
    service.memoryCache.clear();
    service.memoryCacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
    };
  });

  afterEach(async () => {
    // 清理Redis缓存
    await app.redis.flushdb();
  });

  describe('getWithMultiLevelCache()', () => {
    it('should get data from L1 memory cache', async () => {
      const key = 'test_l1_key';
      const testData = { id: 1, name: 'test' };

      // 预设L1缓存
      service.setToMemoryCache(service.buildCacheKey(key, 'system'), testData, 60);

      let fetcherCalled = false;
      const fetcher = async () => {
        fetcherCalled = true;
        return { id: 2, name: 'fetcher' };
      };

      const result = await service.getWithMultiLevelCache(key, fetcher, {
        enableL1: true,
        enableL2: false,
        enableL3: false,
      });

      assert.deepEqual(result, testData);
      assert(fetcherCalled === false);
      assert(service.memoryCacheStats.hits === 1);
    });

    it('should get data from L2 Redis cache when L1 misses', async () => {
      const key = 'test_l2_key';
      const testData = { id: 1, name: 'test' };

      // 预设L2缓存
      const fullKey = service.buildCacheKey(key, 'system');
      await service.setToRedisCache(fullKey, testData, 300);

      let fetcherCalled = false;
      const fetcher = async () => {
        fetcherCalled = true;
        return { id: 2, name: 'fetcher' };
      };

      const result = await service.getWithMultiLevelCache(key, fetcher, {
        enableL1: true,
        enableL2: true,
        enableL3: false,
      });

      assert.deepEqual(result, testData);
      assert(fetcherCalled === false);

      // 应该回填L1缓存
      const l1Data = service.getFromMemoryCache(fullKey);
      assert.deepEqual(l1Data, testData);
    });

    it('should fetch data when all caches miss', async () => {
      const key = 'test_fetch_key';
      const testData = { id: 1, name: 'fetched' };

      const fetcher = async () => testData;

      const result = await service.getWithMultiLevelCache(key, fetcher, {
        enableL1: true,
        enableL2: true,
        enableL3: false,
      });

      assert.deepEqual(result, testData);

      // 应该缓存到所有启用的层级
      const fullKey = service.buildCacheKey(key, 'system');
      const l1Data = service.getFromMemoryCache(fullKey);
      const l2Data = await service.getFromRedisCache(fullKey);

      assert.deepEqual(l1Data, testData);
      assert.deepEqual(l2Data, testData);
    });

    it('should force refresh when requested', async () => {
      const key = 'test_force_refresh';
      const oldData = { id: 1, name: 'old' };
      const newData = { id: 1, name: 'new' };

      // 预设缓存
      const fullKey = service.buildCacheKey(key, 'system');
      service.setToMemoryCache(fullKey, oldData, 60);

      const fetcher = async () => newData;

      const result = await service.getWithMultiLevelCache(key, fetcher, {
        forceRefresh: true,
      });

      assert.deepEqual(result, newData);
    });
  });

  describe('warmupCache()', () => {
    it('should preload cache with warmup tasks', async () => {
      const warmupTasks = [
        {
          key: 'warmup1',
          fetcher: async () => ({ id: 1, name: 'warmup1' }),
          options: { category: 'user' },
        },
        {
          key: 'warmup2',
          fetcher: async () => ({ id: 2, name: 'warmup2' }),
          options: { category: 'stock' },
        },
      ];

      const results = await service.warmupCache(warmupTasks);

      assert(results.length === 2);

      // 验证缓存已设置
      const key1 = service.buildCacheKey('warmup1', 'user');
      const key2 = service.buildCacheKey('warmup2', 'stock');

      const cached1 = service.getFromMemoryCache(key1);
      const cached2 = service.getFromMemoryCache(key2);

      assert.deepEqual(cached1, { id: 1, name: 'warmup1' });
      assert.deepEqual(cached2, { id: 2, name: 'warmup2' });
    });

    it('should handle warmup failures gracefully', async () => {
      const warmupTasks = [
        {
          key: 'success',
          fetcher: async () => ({ success: true }),
        },
        {
          key: 'failure',
          fetcher: async () => {
            throw new Error('Warmup failed');
          },
        },
      ];

      const results = await service.warmupCache(warmupTasks);

      assert(results.length === 2);
      assert(results[0].status === 'fulfilled');
      assert(results[1].status === 'rejected');
    });
  });

  describe('invalidateCache()', () => {
    it('should invalidate memory cache by pattern', async () => {
      // 设置一些缓存
      service.setToMemoryCache('system_data:user1', { id: 1 }, 60);
      service.setToMemoryCache('system_data:user2', { id: 2 }, 60);
      service.setToMemoryCache('system_data:post1', { id: 3 }, 60);

      await service.invalidateCache('user', {
        levels: [ 'L1' ],
        cascade: false,
      });

      assert(service.getFromMemoryCache('system_data:user1') === null);
      assert(service.getFromMemoryCache('system_data:user2') === null);
      assert(service.getFromMemoryCache('system_data:post1') !== null);
    });

    it('should invalidate Redis cache by pattern', async () => {
      // 设置Redis缓存
      await app.redis.set('system_data:redis1', JSON.stringify({ id: 1 }));
      await app.redis.set('system_data:redis2', JSON.stringify({ id: 2 }));
      await app.redis.set('other_data:redis3', JSON.stringify({ id: 3 }));

      await service.invalidateCache('redis', {
        levels: [ 'L2' ],
        cascade: false,
      });

      const value1 = await app.redis.get('system_data:redis1');
      const value2 = await app.redis.get('system_data:redis2');
      const value3 = await app.redis.get('other_data:redis3');

      assert(value1 === null);
      assert(value2 === null);
      assert(value3 !== null);
    });

    it('should cascade invalidate related caches', async () => {
      // 设置相关缓存
      service.setToMemoryCache('stock_data:AAPL', { price: 150 }, 60);
      service.setToMemoryCache('user_data:portfolio123', { stocks: [ 'AAPL' ] }, 60);
      service.setToMemoryCache('hot_data:stocks_trending', { list: [ 'AAPL' ] }, 60);

      await service.invalidateCache('stock_data:', {
        levels: [ 'L1' ],
        cascade: true,
      });

      // 验证级联失效
      assert(service.getFromMemoryCache('stock_data:AAPL') === null);
      // 相关缓存也应该被清除（根据级联规则）
    });
  });

  describe('buildCacheKey()', () => {
    it('should build cache key with category prefix', async () => {
      const key1 = service.buildCacheKey('test', 'user');
      const key2 = service.buildCacheKey('test', 'stock');
      const key3 = service.buildCacheKey('test', 'unknown');

      assert(key1 === 'user_data:test');
      assert(key2 === 'stock_data:test');
      assert(key3 === 'system_data:test');
    });
  });

  describe('memory cache operations', () => {
    it('should handle memory cache expiry', async () => {
      const key = 'expiry_test';
      const data = { test: true };

      service.setToMemoryCache(key, data, 0.1); // 0.1秒过期

      // 立即获取应该成功
      assert.deepEqual(service.getFromMemoryCache(key), data);

      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150));

      // 过期后应该返回null
      assert(service.getFromMemoryCache(key) === null);
    });

    it('should cleanup memory cache when size limit exceeded', async () => {
      const originalSize = service.memoryCache.size;

      // 添加大量缓存项
      for (let i = 0; i < 1100; i++) {
        service.setToMemoryCache(`test_${i}`, { id: i }, 60);
      }

      // 应该触发清理
      assert(service.memoryCache.size <= 1000);
    });
  });

  describe('getCacheStats()', () => {
    it('should return cache statistics', async () => {
      // 设置一些缓存和统计
      service.setToMemoryCache('test1', { id: 1 }, 60);
      service.setToMemoryCache('test2', { id: 2 }, 60);
      service.memoryCacheStats.hits = 10;
      service.memoryCacheStats.misses = 5;

      const stats = await service.getCacheStats();

      assert(typeof stats.memory === 'object');
      assert(typeof stats.redis === 'object');
      assert(stats.memory.size === 2);
      assert(stats.memory.hits === 10);
      assert(stats.memory.misses === 5);
      assert(stats.memory.hitRate === 66.66666666666666);
    });
  });

  describe('error handling', () => {
    it('should handle Redis connection errors gracefully', async () => {
      // 模拟Redis错误
      const originalGet = app.redis.get;
      app.redis.get = async () => {
        throw new Error('Redis connection failed');
      };

      const key = 'error_test';
      const fetcher = async () => ({ fallback: true });

      const result = await service.getWithMultiLevelCache(key, fetcher, {
        enableL1: false,
        enableL2: true,
      });

      assert.deepEqual(result, { fallback: true });

      // 恢复Redis方法
      app.redis.get = originalGet;
    });

    it('should handle fetcher errors', async () => {
      const key = 'fetcher_error';
      const fetcher = async () => {
        throw new Error('Fetcher failed');
      };

      try {
        await service.getWithMultiLevelCache(key, fetcher);
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert(error.message === 'Fetcher failed');
      }
    });
  });
});
