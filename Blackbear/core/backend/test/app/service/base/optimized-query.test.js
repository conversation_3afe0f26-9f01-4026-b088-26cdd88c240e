'use strict';

const { app, assert } = require('egg-mock/bootstrap');

describe('test/app/service/base/optimized-query.test.js', () => {
  let ctx;
  let service;

  beforeEach(() => {
    ctx = app.mockContext();
    service = ctx.service.base.optimizedQuery;
  });

  describe('withCache()', () => {
    it('should cache query results', async () => {
      const cacheKey = 'test_cache_key';
      let callCount = 0;

      const queryFn = async () => {
        callCount++;
        return { data: 'test_data', timestamp: Date.now() };
      };

      // 第一次调用
      const result1 = await service.withCache(cacheKey, queryFn, { ttl: 60 });
      assert(result1.data === 'test_data');
      assert(callCount === 1);

      // 第二次调用应该从缓存获取
      const result2 = await service.withCache(cacheKey, queryFn, { ttl: 60 });
      assert(result2.data === 'test_data');
      assert(callCount === 1); // 没有再次调用查询函数

      // 强制刷新
      const result3 = await service.withCache(cacheKey, queryFn, {
        ttl: 60,
        forceRefresh: true,
      });
      assert(result3.data === 'test_data');
      assert(callCount === 2); // 再次调用了查询函数
    });

    it('should handle cache miss', async () => {
      const cacheKey = 'non_existent_key';
      const queryFn = async () => ({ data: 'fresh_data' });

      const result = await service.withCache(cacheKey, queryFn);
      assert(result.data === 'fresh_data');
    });

    it('should handle query errors', async () => {
      const cacheKey = 'error_key';
      const queryFn = async () => {
        throw new Error('Query failed');
      };

      try {
        await service.withCache(cacheKey, queryFn);
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert(error.message === 'Query failed');
      }
    });
  });

  describe('executeWithMonitoring()', () => {
    it('should monitor query execution time', async () => {
      const queryFn = async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return { data: 'test' };
      };

      const result = await service.executeWithMonitoring(queryFn, 'test_query');
      assert(result.data === 'test');
    });

    it('should detect slow queries', async () => {
      // 模拟慢查询
      service.slowQueryThreshold = 50; // 50ms阈值

      const queryFn = async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return { data: 'slow_query' };
      };

      const result = await service.executeWithMonitoring(queryFn, 'slow_query');
      assert(result.data === 'slow_query');
    });

    it('should handle monitoring errors', async () => {
      const queryFn = async () => {
        throw new Error('Monitoring test error');
      };

      try {
        await service.executeWithMonitoring(queryFn, 'error_query');
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert(error.message === 'Monitoring test error');
      }
    });
  });

  describe('batchQuery()', () => {
    it('should execute batch queries with transaction', async () => {
      const queries = [
        { execute: async () => ({ id: 1, name: 'test1' }) },
        { execute: async () => ({ id: 2, name: 'test2' }) },
        { execute: async () => ({ id: 3, name: 'test3' }) },
      ];

      const results = await service.batchQuery(queries, { useTransaction: false });
      assert(results.length === 3);
      assert(results[0].id === 1);
      assert(results[1].id === 2);
      assert(results[2].id === 3);
    });

    it('should handle batch query errors', async () => {
      const queries = [
        { execute: async () => ({ id: 1, name: 'test1' }) },
        { execute: async () => { throw new Error('Batch error'); } },
        { execute: async () => ({ id: 3, name: 'test3' }) },
      ];

      try {
        await service.batchQuery(queries, { useTransaction: false });
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert(error.message === 'Batch error');
      }
    });

    it('should control concurrency', async () => {
      let concurrentCount = 0;
      let maxConcurrent = 0;

      const queries = Array.from({ length: 10 }, (_, i) => ({
        execute: async () => {
          concurrentCount++;
          maxConcurrent = Math.max(maxConcurrent, concurrentCount);
          await new Promise(resolve => setTimeout(resolve, 50));
          concurrentCount--;
          return { id: i };
        },
      }));

      const results = await service.batchQuery(queries, {
        useTransaction: false,
        concurrency: 3,
      });

      assert(results.length === 10);
      assert(maxConcurrent <= 3);
    });
  });

  describe('cursorPagination()', () => {
    it('should implement cursor pagination', async () => {
      // 模拟模型
      const mockModel = {
        findAll: async options => {
          const { where, limit, order } = options;
          const allData = Array.from({ length: 50 }, (_, i) => ({ id: i + 1, name: `item${i + 1}` }));

          let filteredData = allData;
          if (where && where.id) {
            const { Op } = app.Sequelize;
            if (where.id[Op.gt]) {
              filteredData = allData.filter(item => item.id > where.id[Op.gt]);
            }
          }

          return filteredData.slice(0, limit);
        },
      };

      const result = await service.cursorPagination(mockModel, {
        limit: 10,
        cursorField: 'id',
        direction: 'ASC',
      });

      assert(result.items.length === 10);
      assert(result.hasNextPage === true);
      assert(result.nextCursor === 10);
    });

    it('should handle last page', async () => {
      const mockModel = {
        findAll: async () => Array.from({ length: 5 }, (_, i) => ({ id: i + 46, name: `item${i + 46}` })),
      };

      const result = await service.cursorPagination(mockModel, {
        cursor: 45,
        limit: 10,
        cursorField: 'id',
      });

      assert(result.items.length === 5);
      assert(result.hasNextPage === false);
      assert(result.nextCursor === 50);
    });
  });

  describe('optimizeQuery()', () => {
    it('should optimize query options', async () => {
      const queryOptions = {
        include: [
          { model: 'User', attributes: [ 'id', 'name' ] },
          { model: 'Post', include: [{ model: 'Comment' }] },
        ],
      };

      const optimized = service.optimizeQuery(queryOptions);

      assert(optimized.attributes.exclude.includes('deletedAt'));
      assert(optimized.benchmark === true);
      assert(optimized.include.length === 2);
    });

    it('should optimize includes', async () => {
      const includes = [
        { model: 'User' },
        { model: 'Post', include: [{ model: 'Comment' }] },
      ];

      const optimized = service.optimizeIncludes(includes);

      assert(optimized[0].limit === 10);
      assert(optimized[0].attributes.exclude.includes('deletedAt'));
      assert(optimized[1].include[0].limit === 10);
    });
  });

  describe('clearCache()', () => {
    it('should clear cache by pattern', async () => {
      // 设置一些缓存
      await app.redis.set('query_cache:test1', 'value1');
      await app.redis.set('query_cache:test2', 'value2');
      await app.redis.set('other_cache:test3', 'value3');

      await service.clearCache('test');

      const value1 = await app.redis.get('query_cache:test1');
      const value2 = await app.redis.get('query_cache:test2');
      const value3 = await app.redis.get('other_cache:test3');

      assert(value1 === null);
      assert(value2 === null);
      assert(value3 === 'value3'); // 不应该被清除
    });

    it('should clear multiple patterns', async () => {
      await app.redis.set('query_cache:user1', 'value1');
      await app.redis.set('query_cache:post1', 'value2');

      await service.clearCache([ 'user', 'post' ]);

      const value1 = await app.redis.get('query_cache:user1');
      const value2 = await app.redis.get('query_cache:post1');

      assert(value1 === null);
      assert(value2 === null);
    });
  });

  describe('getQueryStats()', () => {
    it('should return query statistics', async () => {
      const stats = await service.getQueryStats();

      assert(typeof stats.totalCacheKeys === 'number');
      assert(typeof stats.cacheHitRate === 'number');
      assert(typeof stats.averageQueryTime === 'number');
      assert(typeof stats.slowQueryCount === 'number');
    });
  });
});
