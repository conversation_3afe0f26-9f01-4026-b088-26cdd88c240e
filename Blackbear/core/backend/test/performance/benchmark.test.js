'use strict';

const { app, assert } = require('egg-mock/bootstrap');

describe('Performance Benchmark Tests', () => {
  let token;
  let testData = {};

  before(async () => {
    // 设置测试数据
    await setupTestData();
    
    // 获取认证token
    const loginResponse = await app.httpRequest()
      .post('/api/auth/login')
      .send({
        username: 'benchmarkuser',
        password: 'BenchmarkPass123!',
      });
    
    if (loginResponse.status === 200) {
      token = loginResponse.body.data.token;
    }
  });

  after(async () => {
    // 清理测试数据
    await cleanupTestData();
  });

  describe('Database Query Performance', () => {
    it('should query stocks list within performance threshold', async () => {
      const startTime = Date.now();
      
      const response = await app.httpRequest()
        .get('/api/stocks?page=1&limit=20')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      const duration = Date.now() - startTime;
      
      assert(response.body.success === true);
      assert(duration < 1000, `Query took ${duration}ms, should be under 1000ms`);
      
      console.log(`Stocks list query: ${duration}ms`);
    });

    it('should query news list within performance threshold', async () => {
      const startTime = Date.now();
      
      const response = await app.httpRequest()
        .get('/api/news?page=1&limit=20')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      const duration = Date.now() - startTime;
      
      assert(response.body.success === true);
      assert(duration < 1000, `Query took ${duration}ms, should be under 1000ms`);
      
      console.log(`News list query: ${duration}ms`);
    });

    it('should perform complex search within threshold', async () => {
      const startTime = Date.now();
      
      const response = await app.httpRequest()
        .get('/api/stocks/search?keyword=测试&industry=科技&market=SZ')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      const duration = Date.now() - startTime;
      
      assert(response.body.success === true);
      assert(duration < 2000, `Complex search took ${duration}ms, should be under 2000ms`);
      
      console.log(`Complex search query: ${duration}ms`);
    });
  });

  describe('Cache Performance', () => {
    it('should demonstrate cache hit performance improvement', async () => {
      const endpoint = '/api/stocks?page=1&limit=10';
      
      // 第一次请求（缓存未命中）
      const startTime1 = Date.now();
      const response1 = await app.httpRequest()
        .get(endpoint)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      const duration1 = Date.now() - startTime1;
      
      // 第二次请求（缓存命中）
      const startTime2 = Date.now();
      const response2 = await app.httpRequest()
        .get(endpoint)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      const duration2 = Date.now() - startTime2;
      
      assert(response1.body.success === true);
      assert(response2.body.success === true);
      
      // 缓存命中应该更快
      console.log(`Cache miss: ${duration1}ms, Cache hit: ${duration2}ms`);
      console.log(`Cache improvement: ${((duration1 - duration2) / duration1 * 100).toFixed(2)}%`);
      
      // 缓存命中应该至少快20%
      assert(duration2 < duration1 * 0.8, 'Cache hit should be at least 20% faster');
    });

    it('should test multi-level cache performance', async () => {
      const testKey = 'performance_test_key';
      const testData = { test: 'data', timestamp: Date.now() };
      
      // 测试L1缓存（内存）
      const ctx = app.mockContext();
      const cacheService = ctx.service.base.advancedCache;
      
      const startTime1 = Date.now();
      await cacheService.getWithMultiLevelCache(testKey, async () => testData, {
        enableL1: true,
        enableL2: false,
        enableL3: false,
      });
      const l1Duration = Date.now() - startTime1;
      
      // 测试L2缓存（Redis）
      const startTime2 = Date.now();
      await cacheService.getWithMultiLevelCache(testKey + '_l2', async () => testData, {
        enableL1: false,
        enableL2: true,
        enableL3: false,
      });
      const l2Duration = Date.now() - startTime2;
      
      console.log(`L1 Cache (Memory): ${l1Duration}ms`);
      console.log(`L2 Cache (Redis): ${l2Duration}ms`);
      
      // 内存缓存应该比Redis缓存快
      assert(l1Duration <= l2Duration, 'Memory cache should be faster than Redis cache');
    });
  });

  describe('Concurrent Request Performance', () => {
    it('should handle concurrent requests efficiently', async () => {
      const concurrency = 10;
      const requests = [];
      
      const startTime = Date.now();
      
      for (let i = 0; i < concurrency; i++) {
        requests.push(
          app.httpRequest()
            .get('/api/stocks?page=1&limit=5')
            .set('Authorization', `Bearer ${token}`)
        );
      }
      
      const responses = await Promise.all(requests);
      const totalDuration = Date.now() - startTime;
      const avgDuration = totalDuration / concurrency;
      
      // 检查所有请求都成功
      responses.forEach(response => {
        assert(response.status === 200);
        assert(response.body.success === true);
      });
      
      console.log(`${concurrency} concurrent requests: ${totalDuration}ms total, ${avgDuration.toFixed(2)}ms average`);
      
      // 并发请求的平均时间应该在合理范围内
      assert(avgDuration < 500, `Average response time ${avgDuration}ms should be under 500ms`);
    });

    it('should maintain performance under load', async () => {
      const loadTestDuration = 5000; // 5秒
      const requestInterval = 100; // 每100ms一个请求
      const requests = [];
      const startTime = Date.now();
      
      while (Date.now() - startTime < loadTestDuration) {
        requests.push(
          app.httpRequest()
            .get('/api/stocks?page=1&limit=5')
            .set('Authorization', `Bearer ${token}`)
            .then(response => ({
              status: response.status,
              duration: Date.now() - startTime,
            }))
        );
        
        await new Promise(resolve => setTimeout(resolve, requestInterval));
      }
      
      const results = await Promise.all(requests);
      const successfulRequests = results.filter(r => r.status === 200);
      const successRate = successfulRequests.length / results.length;
      
      console.log(`Load test: ${results.length} requests, ${(successRate * 100).toFixed(2)}% success rate`);
      
      // 成功率应该至少95%
      assert(successRate >= 0.95, `Success rate ${(successRate * 100).toFixed(2)}% should be at least 95%`);
    });
  });

  describe('Memory Usage Performance', () => {
    it('should not have significant memory leaks', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行大量操作
      for (let i = 0; i < 100; i++) {
        await app.httpRequest()
          .get('/api/stocks?page=1&limit=10')
          .set('Authorization', `Bearer ${token}`);
      }
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;
      
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB (${memoryIncreasePercent.toFixed(2)}%)`);
      
      // 内存增长应该在合理范围内（小于50%）
      assert(memoryIncreasePercent < 50, `Memory increase ${memoryIncreasePercent.toFixed(2)}% should be under 50%`);
    });
  });

  describe('Response Size Optimization', () => {
    it('should compress large responses', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks?page=1&limit=100')
        .set('Authorization', `Bearer ${token}`)
        .set('Accept-Encoding', 'gzip, deflate, br')
        .expect(200);
      
      const responseSize = JSON.stringify(response.body).length;
      console.log(`Response size: ${(responseSize / 1024).toFixed(2)}KB`);
      
      // 检查是否启用了压缩
      if (responseSize > 1024) {
        assert(response.headers['content-encoding'], 'Large responses should be compressed');
        console.log(`Compression: ${response.headers['content-encoding']}`);
      }
    });

    it('should optimize response data structure', async () => {
      const response = await app.httpRequest()
        .get('/api/stocks?page=1&limit=10')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      assert(response.body.success === true);
      assert(Array.isArray(response.body.data.list));
      
      // 检查响应数据是否已优化（移除空字段等）
      const firstItem = response.body.data.list[0];
      if (firstItem) {
        const hasEmptyFields = Object.values(firstItem).some(value => 
          value === null || value === undefined || value === ''
        );
        
        console.log(`Response optimization: ${hasEmptyFields ? 'Not optimized' : 'Optimized'}`);
      }
    });
  });

  // 辅助函数
  async function setupTestData() {
    try {
      // 创建测试用户
      const user = await app.model.User.create({
        username: 'benchmarkuser',
        password: 'BenchmarkPass123!',
        email: '<EMAIL>',
        status: 1,
      });
      testData.userId = user.id;

      // 创建测试股票数据
      const stocks = [];
      for (let i = 0; i < 50; i++) {
        stocks.push({
          code: `TEST${String(i).padStart(3, '0')}`,
          name: `测试股票${i}`,
          market: i % 2 === 0 ? 'SZ' : 'SH',
          industry: i % 3 === 0 ? '科技' : '金融',
          status: 1,
        });
      }
      await app.model.Stock.bulkCreate(stocks);

      // 创建测试新闻数据
      const news = [];
      for (let i = 0; i < 30; i++) {
        news.push({
          title: `测试新闻标题${i}`,
          content: `测试新闻内容${i}`,
          source: '测试来源',
          url: `https://example.com/news/${i}`,
          publishTime: new Date(),
          status: 1,
        });
      }
      await app.model.News.bulkCreate(news);

    } catch (error) {
      console.error('Setup test data failed:', error);
    }
  }

  async function cleanupTestData() {
    try {
      // 清理测试数据
      await app.model.User.destroy({ where: { username: 'benchmarkuser' } });
      await app.model.Stock.destroy({ where: { code: { [app.Sequelize.Op.like]: 'TEST%' } } });
      await app.model.News.destroy({ where: { title: { [app.Sequelize.Op.like]: '测试新闻标题%' } } });
    } catch (error) {
      console.error('Cleanup test data failed:', error);
    }
  }
});
