'use strict';

/**
 * 测试应用配置
 */

const path = require('path');

module.exports = {
  framework: {
    path: path.join(__dirname, '../../'),
  },

  // 测试环境配置
  env: 'unittest',

  // 数据库配置
  sequelize: {
    dialect: 'sqlite',
    storage: ':memory:',
    logging: false,
    define: {
      timestamps: true,
      paranoid: true,
      freezeTableName: true,
      underscored: false,
    },
  },

  // Redis配置（使用内存模拟）
  redis: {
    client: {
      host: '127.0.0.1',
      port: 6379,
      db: 15, // 使用测试数据库
    },
  },

  // 安全配置
  security: {
    csrf: {
      enable: false,
    },
  },

  // 日志配置
  logger: {
    level: 'WARN',
    consoleLevel: 'WARN',
  },

  // 中间件配置（测试环境简化）
  middleware: [
    'bodyParser',
    'security',
  ],

  // 测试专用配置
  unittest: {
    port: 7002,
  },
};
