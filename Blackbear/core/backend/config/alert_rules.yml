# Prometheus告警规则
groups:
  - name: blackbear-backend-alerts
    rules:
      # 应用程序告警
      - alert: ApplicationDown
        expr: up{job="blackbear-backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Blackbear Backend应用程序宕机"
          description: "Blackbear Backend应用程序已经宕机超过1分钟"

      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过高"
          description: "95%的请求响应时间超过2秒，持续5分钟"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "错误率过高"
          description: "5xx错误率超过10%，持续2分钟"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "应用程序内存使用超过1GB，持续5分钟"

      # 数据库告警
      - alert: DatabaseDown
        expr: up{job="mysql"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MySQL数据库宕机"
          description: "MySQL数据库连接失败超过1分钟"

      - alert: DatabaseSlowQueries
        expr: mysql_global_status_slow_queries > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库慢查询过多"
          description: "MySQL慢查询数量超过10个，持续5分钟"

      # Redis告警
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis缓存服务宕机"
          description: "Redis缓存服务连接失败超过1分钟"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%，持续5分钟"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，持续5分钟"

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘使用率过高"
          description: "磁盘使用率超过90%，持续5分钟"

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) > 100000000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "网络流量过高"
          description: "网络接收流量超过100MB/s，持续5分钟"
