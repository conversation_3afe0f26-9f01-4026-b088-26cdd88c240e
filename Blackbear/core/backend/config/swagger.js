'use strict';

/**
 * Swagger API文档配置
 */

module.exports = appInfo => {
  const config = {};

  config.swaggerdoc = {
    dirScanner: './app/controller',
    apiInfo: {
      title: 'Blackbear API Documentation',
      description: 'Blackbear Core Backend API 接口文档',
      version: '3.0.0',
      termsOfService: 'https://blackbear.com/terms',
      contact: {
        name: 'Blackbear Team',
        email: '<EMAIL>',
        url: 'https://blackbear.com/contact',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    enableSecurity: true,
    // 安全定义
    securityDefinitions: {
      bearerAuth: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
        description: 'Bearer token for authentication. Format: Bearer {token}',
      },
      apiKey: {
        type: 'api<PERSON><PERSON>',
        name: 'X-API-Key',
        in: 'header',
        description: 'API Key for service authentication',
      },
    },
    enableValidate: true,
    routerMap: true,
    enable: true,
  };

  // Swagger UI配置
  config.swagger = {
    title: 'Blackbear API',
    description: 'Blackbear Core Backend API Documentation',
    version: '3.0.0',
    url: '/swagger-ui.html',
    swaggerOptions: {
      validatorUrl: null,
      oauth: {
        clientId: 'blackbear-api',
        clientSecret: 'your-client-secret',
        realm: 'blackbear',
        appName: 'Blackbear API',
        scopeSeparator: ' ',
        additionalQueryStringParams: {},
      },
    },
    routerMap: true,
    enable: true,
  };

  return config;
};
