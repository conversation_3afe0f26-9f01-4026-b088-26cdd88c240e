'use strict';

/**
 * 本地开发环境配置
 * 用于本地开发时的特殊配置
 * @param appInfo
 */
module.exports = appInfo => {
  const config = exports = {};

  // 本地开发时强制使用MinIO存储服务
  config.useOSS = 0; // 1 = MinIO, 0 = AliOSS

  // 本地开发时的MinIO配置（使用本地Docker容器）
  config.minio = {
    client: {
      endPoint: 'localhost',
      port: 9000,
      useSSL: false,
      accessKey: 'minioadmin',
      secretKey: 'minioadmin',
    },
  };

  // 本地开发时的OSS配置（备用）
  config.oss = {
    client: {
      accessKeyId: process.env.BB_OssAccessKeyId || 'local-dev',
      accessKeySecret: process.env.BB_OssAccessKeySecret || 'local-dev',
      bucket: process.env.BB_OssBucket || 'local-bucket',
      endpoint: process.env.BB_OssEndpoint || 'http://localhost:9000',
      timeout: '60s',
    },
  };

  // 本地开发时的其他配置
  config.logger = {
    level: 'DEBUG',
    consoleLevel: 'DEBUG',
  };

  // 本地开发时的用户配置
  config.ossHost = 'http://localhost:9000';
  config.ossBucketName = 'blackbear';

  return config;
};
