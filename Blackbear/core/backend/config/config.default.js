/* eslint valid-jsdoc: "off" */

'use strict';
const path = require('path');

/**
 * 解析文件大小字符串 (如 "10mb", "1gb")
 */
function parseFileSize(sizeStr) {
  if (!sizeStr || typeof sizeStr !== 'string') {
    return null;
  }

  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024,
  };

  const match = sizeStr.toLowerCase().match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb)$/);
  if (!match) {
    return parseInt(sizeStr) || null;
  }

  const [, size, unit] = match;
  return Math.floor(parseFloat(size) * units[unit]);
}

const specSequelize = require('sequelize');
// 使用 cls-hooked 才能支持在 sequelize 的事务中使用 async/await
const sequelizeCLSNamespace = require('cls-hooked').createNamespace('blackbear');
specSequelize.useCLS(sequelizeCLSNamespace);

// 配置验证器
const { validateConfig } = require('./config.validator');

// 在非测试环境下验证配置
if (process.env.NODE_ENV !== 'test') {
  try {
    validateConfig();
  } catch (error) {
    console.error('Configuration validation failed:', error.message);
    process.exit(1);
  }
}

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */
module.exports = appInfo => {
  /**
   * built-in config
   * @type {Egg.EggAppConfig}
   **/
  const config = exports = {};

  // use for cookie sign key, should change to your own and keep security
  config.keys = `${appInfo.name}_1570610257068_8645`;

  // 优化后的中间件配置（按执行顺序）
  config.middleware = [
    // 1. 增强安全中间件（集成多个安全功能）
    'enhancedSecurity',

    // 2. 监控和追踪
    'apmMonitor',

    // 3. 缓存管理
    'cacheManager',

    // 4. 响应优化
    'responseOptimizer',

    // 5. 原有中间件（保持兼容）
    'errorHandler',
    'userRequired',
    'cacheClear',
  ];

  config.validate = {
    convert: true, // 对参数可以使用convertType规则进行类型转换
  };

  // ==================== 新增中间件配置 ====================

  // 增强安全中间件配置
  config.enhancedSecurity = {
    enabled: process.env.BB_ENHANCED_SECURITY_ENABLED !== 'false',
    enableXssFilter: process.env.BB_XSS_FILTER_ENABLED !== 'false',
    enableSqlInjectionFilter: process.env.BB_SQL_INJECTION_FILTER_ENABLED !== 'false',
    enableFilenameFilter: true,
    enableUrlValidation: true,
    maxBodySize: parseFileSize(process.env.BB_UPLOAD_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    filterFields: ['content', 'description', 'comment', 'message', 'text'],
    skipPaths: ['/api/health', '/api/health/*', '/publickey', '/svgcaptcha'],

    // 集成限流配置
    rateLimit: {
      enabled: process.env.BB_RATE_LIMIT_ENABLED !== 'false',
      windowMs: parseInt(process.env.BB_RATE_LIMIT_DURATION) || 15 * 60 * 1000, // 15分钟
      max: parseInt(process.env.BB_RATE_LIMIT_MAX) || 1000, // 默认限制

      // 特殊端点限制
      endpoints: {
        'POST /login': { max: 5, windowMs: 15 * 60 * 1000 },
        'POST /register': { max: 3, windowMs: 60 * 60 * 1000 },
        'POST /resetpwd': { max: 3, windowMs: 60 * 60 * 1000 },
        'GET /smscode': { max: 5, windowMs: 60 * 1000 },
        'POST /api/v1/upload': { max: 50, windowMs: 60 * 1000 },
        'GET /api/v1/search': { max: 100, windowMs: 60 * 1000 },
      },
    },
  };

  // APM监控配置
  config.apmMonitor = {
    enabled: process.env.BB_APM_ENABLED !== 'false',
    thresholds: {
      slow: parseInt(process.env.BB_SLOW_QUERY_THRESHOLD) || 1000,
      critical: 5000,
    },
    sampling: {
      enabled: process.env.BB_PERFORMANCE_MONITORING !== 'false',
      rate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    },
  };

  // 缓存管理配置
  config.cacheManager = {
    enabled: process.env.BB_CACHE_OPTIMIZATION_ENABLED !== 'false',
    warmup: {
      enabled: process.env.NODE_ENV === 'production',
      onStartup: true,
    },
    compression: {
      enabled: true,
      threshold: 1024,
    },
    ttl: {
      default: parseInt(process.env.BB_CACHE_DEFAULT_TTL) || 300,
      l1: parseInt(process.env.BB_CACHE_L1_TTL) || 60,
      l2: parseInt(process.env.BB_CACHE_L2_TTL) || 300,
      l3: parseInt(process.env.BB_CACHE_L3_TTL) || 3600,
    },
  };

  // 响应优化配置
  config.responseOptimizer = {
    enabled: process.env.BB_RESPONSE_COMPRESSION_ENABLED !== 'false',
    compression: {
      enabled: process.env.BB_RESPONSE_COMPRESSION_ENABLED !== 'false',
      threshold: 1024,
      algorithms: ['gzip', 'deflate', 'br'],
    },
    optimization: {
      removeEmptyFields: true,
      compactArrays: true,
    },
  };

  config.session = {
    key: 'SESSION_BLACKBEAR',
    maxAge: 30 * 3600 * 1000, // 1 天
    httpOnly: true,
    encrypt: true,
    renew: true,
  };

  config.security = {
    domainWhiteList: process.env.NODE_ENV === 'production'
      ? (process.env.BB_DOMAIN_WHITELIST || '').split(',').filter(Boolean)
      : [ '*' ], // 生产环境使用白名单，开发环境允许所有域名
    csrf: {
      ignoreJSON: true,
      headerName: 'x-csrf-token',
      enable: process.env.NODE_ENV === 'production', // 生产环境启用CSRF保护
    },
    xframe: {
      enable: true,
      value: 'SAMEORIGIN', // 防止点击劫持
    },
    hsts: {
      enable: process.env.NODE_ENV === 'production',
      maxAge: 31536000, // 1年
      includeSubdomains: true,
    },
    noopen: {
      enable: true, // 防止IE自动打开下载文件
    },
    nosniff: {
      enable: true, // 防止MIME类型嗅探
    },
    xssProtection: {
      enable: true,
      value: '1; mode=block', // XSS保护
    },
  };

  config.cors = {
    // origin: ctx => ctx.get('origin'),
    origin: '*',
    credentials: true,
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS',
  };

  config.passportLocal = {
    usernameField: 'username',
    passwordField: 'password',
  };

  config.alinode = {
    enable: true,
    server: 'wss://agentserver.node.aliyun.com:8080',
    appid: process.env.BB_AlinodeAppid,
    secret: process.env.BB_AlinodeSecret,
    logdir: '/logs/',
  };


  config.passportWeibo = {
    key: process.env.BB_PassportWeiboKey,
    secret: process.env.BB_PassportWeiboSecret,
    proxy: true,
  };

  config.jwt = {
    secret: 'blackbear',
    // enable: true, // 全局开关
  };

  config.oss = {
    client: {
      accessKeyId: process.env.BB_OssAccessKeyId,
      accessKeySecret: process.env.BB_OssAccessKeySecret,
      bucket: process.env.BB_OssBucket,
      endpoint: process.env.BB_OssEndpoint,
      timeout: '60s',
    },
  };


  config.minio = {
    client: {
      endPoint: process.env.BB_MinioEndPoint,
      port: parseInt(process.env.BB_MinioPort),
      useSSL: false,
      accessKey: process.env.BB_MinioAccessKey,
      secretKey: process.env.BB_MinioSecretKey,
    },
  };

  config.wechatApi = {
    appId: process.env.BB_WechatApiAppId,
    appSecret: process.env.BB_WechatApiAppSecret,
  };

  config.healthy = {
    readinessPath: '/api/readiness',
    livenessPath: '/api/liveness',
  };

  config.mailer = {
    host: process.env.BB_MailerHost,
    port: process.env.BB_MailerPort,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.BB_MailerAuthUser, // generated ethereal user
      pass: process.env.BB_MailerAuthPass,
    },
  };

  config.multipart = {
    fileSize: '100mb',
    // fileExtensions: [
    //   '.docx',
    //   '.doc',
    //   '.xls',
    //   '.xlsx',
    // ],
    whitelist: filename => true, // 不做类型限制
  };

  config.static = {
    prefix: '/public/',
    dir: path.join(appInfo.baseDir, '../backend/public'),
    upload_dir: 'uploads',
  };

  config.mysql = {
    // database configuration
    client: {
      // host
      host: process.env.BB_MYSQL_HOST,
      // port
      port: parseInt(process.env.BB_MYSQL_PORT) || 3306,
      // username
      user: process.env.BB_MYSQL_USER,
      // password
      password: process.env.BB_MYSQL_PASSWD,
      // database
      database: process.env.BB_MYSQL_DBNAME,
      // 连接池配置优化
      connectionLimit: parseInt(process.env.BB_MYSQL_POOL_MAX) || 20,
      acquireTimeout: parseInt(process.env.BB_MYSQL_POOL_ACQUIRE) || 30000,
      timeout: parseInt(process.env.BB_MYSQL_TIMEOUT) || 60000,
      reconnect: true,
      charset: 'utf8mb4',
      timezone: '+08:00',
      // SSL配置（生产环境建议启用）
      ssl: process.env.BB_MYSQL_SSL === 'true' ? {
        rejectUnauthorized: false,
      } : false,
    },
    // load into app, default is open
    app: true,
    // load into agent, default is close
    agent: false,
  };

  config.sequelize = {
    dialect: 'mysql', // support: mysql, mariadb, postgres, mssql
    database: process.env.BB_MYSQL_DBNAME,
    host: process.env.BB_MYSQL_HOST,
    port: parseInt(process.env.BB_MYSQL_PORT) || 3306,
    username: process.env.BB_MYSQL_USER,
    password: process.env.BB_MYSQL_PASSWD,
    Sequelize: specSequelize,
    timezone: '+08:00',
    // 连接池配置优化
    pool: {
      max: parseInt(process.env.BB_MYSQL_POOL_MAX) || 20,
      min: parseInt(process.env.BB_MYSQL_POOL_MIN) || 5,
      acquire: parseInt(process.env.BB_MYSQL_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.BB_MYSQL_POOL_IDLE) || 10000,
      evict: parseInt(process.env.BB_MYSQL_POOL_EVICT) || 1000,
    },
    // 查询配置
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    benchmark: true, // 启用性能基准测试
    retry: {
      max: 3, // 最大重试次数
    },
    define: { // model的全局配置
      timestamps: true, // 添加create,update,delete时间戳
      paranoid: true, // 添加软删除
      freezeTableName: true, // 防止修改表名为复数
      underscored: false, // 防止驼峰式字段被默认转为下划线
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
    },
  };

  config.redis = {
    client: {
      port: parseInt(process.env.BB_REDIS_PORT) || 6379,
      host: process.env.BB_REDIS_HOST || '127.0.0.1',
      password: process.env.BB_REDIS_PASSWORD || '',
      db: parseInt(process.env.BB_REDIS_DB0) || 0,
      // 连接配置优化
      connectTimeout: parseInt(process.env.BB_REDIS_CONNECT_TIMEOUT) || 30000, // 增加到30秒
      commandTimeout: parseInt(process.env.BB_REDIS_COMMAND_TIMEOUT) || 10000, // 增加到10秒
      retryDelayOnFailover: parseInt(process.env.BB_REDIS_RETRY_DELAY) || 100,
      enableReadyCheck: true, // 启用就绪检查
      maxRetriesPerRequest: parseInt(process.env.BB_REDIS_MAX_RETRIES) || 3,
      lazyConnect: false, // 禁用懒连接，立即连接
      keepAlive: 30000,
      // 连接池配置
      family: 4, // 4 (IPv4) or 6 (IPv6)
      keyPrefix: process.env.BB_REDIS_KEY_PREFIX || 'blackbear:',
      // 添加重连配置
      retryDelayOnClusterDown: 300,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      // 健康检查
      enableOfflineQueue: false,
    },
    // 启用agent模式以支持定时任务
    agent: true,
  };

  config.io = {
    init: {}, // 设置引擎, 默认 ws 引擎
    namespace: {
      // namespace(nsp) 通常意味分配到不同的接入点或者路径。
      // 如果客户端没有指定 nsp，则默认分配到 `/` 这个默认的命名空间。
      '/': {
        connectionMiddleware: [ 'connection' ],
        packetMiddleware: [ 'packet' ],
      },
    },
    redis: {
      port: process.env.BB_REDIS_PORT, // Redis port
      host: process.env.BB_REDIS_HOST, // Redis host
      auth_pass: process.env.BB_REDIS_PASSWORD,
      db: process.env.BB_WS_REDIS_DB3,
    },
    generateId: req => {
      return req._query.userId;
    },
  };

  config.logger = {
    dir: './logs', // 打印目录重定向
    outputJSON: true, // json格式输出
    level: 'DEBUG',
    consoleLevel: 'DEBUG',
  };

  // 全局缓存开关，支持环境变量 BB_CACHE_ENABLED
  config.cacheEnabled = process.env.BB_CACHE_ENABLED !== 'false';

  // add your user config here
  const userConfig = {
    // myAppName: 'egg',
    jwtExp: '30d',
    socketOnlineUserRoomName: 'onlineUserRoom:', // socket所有在线用户房间名
    socketRedisExp: 30, // socket消息存入redis过期时间(秒)
    scrapyHost: process.env.BB_SCRAPY_URL,
    taskHost: process.env.BB_TASK_API_URL,
    frontendHost: process.env.BB_FRONTEND_URL,
    aktoolsHost: process.env.BB_AKToolsHost,
    weiboPassportRedirect: process.env.BB_WeiboPassportRedirect,
    webURL: process.env.BB_WEB_URL,
    bingIndexNowKey: process.env.BB_BingIndexNowKey,
    systemUserId: process.env.BB_SystemUserId,
    adminUsername: process.env.BB_AdminUsername,
    ossHost: process.env.BB_OssHost,
    ossBucketName: process.env.BB_BB_OssBucketName,
    useOSS: process.env.BB_UseOSS,
    // 是否加载到 app 上，默认开启
    app: true,
    // 是否加载到 agent 上，默认关闭
    agent: false,
  };

  return {
    ...config,
    ...userConfig,
  };
};
