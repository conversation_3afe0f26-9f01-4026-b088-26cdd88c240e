'use strict';

/**
 * 优化后的中间件配置
 * 集成所有性能优化和监控中间件
 */

module.exports = appInfo => {
  const config = {};

  // 中间件加载顺序（重要：顺序影响性能）
  config.middleware = [
    // 1. 安全相关中间件（最高优先级）
    'securityFilter',
    'advancedRateLimit',

    // 2. 监控和追踪中间件
    'apmMonitor',

    // 3. 缓存管理中间件
    'cacheManager',

    // 4. 响应优化中间件
    'responseOptimizer',

    // 5. 原有中间件
    'notfound',
    'bodyParser',
    'security',
    'cors',
    'static',
    'errorHandler',
  ];

  // 安全过滤中间件配置
  config.securityFilter = {
    enable: true,
    match: '/api',
    enableXssFilter: true,
    enableSqlInjectionFilter: true,
    enableFilenameFilter: true,
    enableUrlValidation: true,
    maxBodySize: 10 * 1024 * 1024, // 10MB
    filterFields: [ 'content', 'description', 'comment', 'message', 'text' ],
    skipPaths: [ '/api/upload', '/api/file', '/api/health' ],
  };

  // 高级限流中间件配置
  config.advancedRateLimit = {
    enable: true,
    match: '/api',
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 默认最大请求数
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    keyPrefix: 'rate_limit:',

    // 不同端点的限流配置
    endpointLimits: {
      '/api/auth/login': { max: 5, windowMs: 15 * 60 * 1000 },
      '/api/auth/register': { max: 3, windowMs: 60 * 60 * 1000 },
      '/api/upload': { max: 10, windowMs: 60 * 1000 },
      '/api/search': { max: 50, windowMs: 60 * 1000 },
    },

    // 用户级别限流
    userLimits: {
      admin: { max: 1000, windowMs: 60 * 1000 },
      vip: { max: 500, windowMs: 60 * 1000 },
      normal: { max: 100, windowMs: 60 * 1000 },
    },

    // 跳过限流的条件
    skip: ctx => {
      return ctx.path.includes('/health') ||
             ctx.path.includes('/ping') ||
             ctx.path.includes('/metrics');
    },
  };

  // APM监控中间件配置
  config.apmMonitor = {
    enable: true,
    match: '/api',

    // 性能阈值配置
    thresholds: {
      slow: 1000, // 慢请求阈值（毫秒）
      critical: 5000, // 严重慢请求阈值（毫秒）
      memory: 100 * 1024 * 1024, // 内存使用阈值（100MB）
    },

    // 采样配置
    sampling: {
      enabled: true,
      rate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 生产环境10%采样
      slowRequestRate: 1.0, // 慢请求100%采样
    },

    // 指标收集配置
    metrics: {
      enabled: true,
      collectInterval: 30000, // 30秒收集一次
      retentionDays: 7, // 保留7天数据
    },

    // 链路追踪配置
    tracing: {
      enabled: true,
      maxSpans: 100,
      includeHeaders: [ 'user-agent', 'x-forwarded-for' ],
    },

    // 错误监控配置
    errorTracking: {
      enabled: true,
      captureStackTrace: process.env.NODE_ENV !== 'production',
      maxStackFrames: 50,
    },
  };

  // 缓存管理中间件配置
  config.cacheManager = {
    enable: true,
    match: '/api',

    // 缓存预热配置
    warmup: {
      enabled: process.env.NODE_ENV === 'production',
      onStartup: true,
      schedule: '0 0 * * *', // 每天凌晨预热
    },

    // 自动失效配置
    autoInvalidation: {
      enabled: true,
      patterns: {
        'user_data:*': [ 'user.update', 'user.delete' ],
        'stock_data:*': [ 'stock.update', 'stock.delete' ],
        'news_data:*': [ 'news.create', 'news.update', 'news.delete' ],
      },
    },

    // 性能监控
    monitoring: {
      enabled: true,
      slowCacheThreshold: 100, // 100ms
      reportInterval: 60000, // 1分钟
    },

    // 缓存压缩
    compression: {
      enabled: true,
      threshold: 1024, // 1KB以上启用压缩
    },
  };

  // 响应优化中间件配置
  config.responseOptimizer = {
    enable: true,
    match: '/api',

    // 压缩配置
    compression: {
      enabled: true,
      threshold: 1024, // 1KB以上启用压缩
      algorithms: [ 'gzip', 'deflate', 'br' ],
      level: process.env.NODE_ENV === 'production' ? 6 : 1, // 生产环境高压缩
      chunkSize: 16 * 1024, // 16KB chunk size
    },

    // 缓存配置
    cache: {
      enabled: true,
      maxAge: 300, // 5分钟
      private: false,
      mustRevalidate: false,
    },

    // 响应优化配置
    optimization: {
      removeEmptyFields: true,
      compactArrays: true,
      optimizeNumbers: true,
      trimStrings: true,
    },

    // 性能监控配置
    monitoring: {
      enabled: true,
      slowResponseThreshold: 2000, // 2秒
      largeResponseThreshold: 1024 * 1024, // 1MB
    },

    // 安全头配置
    security: {
      enabled: true,
      headers: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
      },
    },
  };

  // 原有中间件优化配置
  config.bodyParser = {
    enable: true,
    encoding: 'utf8',
    formLimit: '1mb',
    jsonLimit: '1mb',
    strict: true,
    // 性能优化
    queryString: {
      arrayLimit: 100,
      depth: 5,
      parameterLimit: 1000,
    },
  };

  config.cors = {
    enable: true,
    package: 'egg-cors',
    // 生产环境严格CORS配置
    origin: process.env.NODE_ENV === 'production'
      ? (process.env.BB_CORS_ORIGINS || '').split(',').filter(Boolean)
      : '*',
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
    allowHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'X-Requested-With',
      'X-Trace-Id',
      'X-Span-Id',
    ],
    credentials: true,
    maxAge: 86400, // 24小时
  };

  config.static = {
    enable: true,
    prefix: '/public/',
    dir: path.join(appInfo.baseDir, 'app/public'),
    // 性能优化
    dynamic: true,
    preload: false,
    buffer: process.env.NODE_ENV === 'production',
    maxFiles: 1000,
    maxAge: process.env.NODE_ENV === 'production' ? 31536000 : 0, // 生产环境1年缓存
  };

  // 环境特定配置
  if (process.env.NODE_ENV === 'production') {
    // 生产环境优化
    config.middleware.unshift('compress'); // 添加压缩中间件

    config.compress = {
      threshold: 1024,
      gzip: {
        flush: require('zlib').constants.Z_SYNC_FLUSH,
      },
      deflate: {
        flush: require('zlib').constants.Z_SYNC_FLUSH,
      },
      br: false, // 使用自定义Brotli实现
    };
  } else {
    // 开发环境配置
    config.development = {
      watchDirs: [
        'app',
        'config',
        'lib',
      ],
      ignoreDirs: [
        'app/public',
        'app/views',
        'logs',
      ],
      fastReady: true,
      reloadOnDebug: true,
    };
  }

  return config;
};

// 导入path模块
