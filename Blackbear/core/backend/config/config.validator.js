'use strict';

/**
 * 配置验证器
 * 验证环境变量和配置的完整性和安全性
 */

const requiredEnvVars = [
  // 数据库配置
  'BB_MYSQL_HOST',
  'BB_MYSQL_PORT',
  'BB_MYSQL_USER',
  'BB_MYSQL_PASSWD',
  'BB_MYSQL_DBNAME',

  // Redis配置
  'BB_REDIS_HOST',
  'BB_REDIS_PORT',
  'BB_REDIS_PASSWORD',
  'BB_REDIS_DB0',

  // 系统配置
  'BB_SystemUserId',
  'BB_AdminUsername',

  // API配置
  'BB_API_URL',
];

const optionalEnvVars = [
  // 第三方服务配置
  'BB_OssAccessKeyId',
  'BB_OssAccessKeySecret',
  'BB_OssBucket',
  'BB_OssEndpoint',
  'BB_MinioEndPoint',
  'BB_MinioPort',
  'BB_MinioAccessKey',
  'BB_MinioSecretKey',

  // 微信配置
  'BB_WechatApiAppId',
  'BB_WechatApiAppSecret',

  // 微博配置
  'BB_PassportWeiboKey',
  'BB_PassportWeiboSecret',

  // 邮件配置
  'BB_MailerHost',
  'BB_MailerPort',
  'BB_MailerAuthUser',
  'BB_MailerAuthPass',

  // 外部服务URL
  'BB_SCRAPY_URL',
  'BB_TASK_URL',
  'BB_FRONTEND_URL',
  'BB_WEB_URL',
  'BB_PROXY_URL',
  'BB_PROXY_URL2',
  'BB_ASSETS_URL',
  'BB_IMAGE_URL',

  // 其他配置
  'BB_VERSION',
  'BB_UseOSS',
  'BB_OssHost',
  'BB_BingIndexNowKey',
  'BB_AKToolsHost',
  'BB_CACHE_ENABLED',
];

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars() {
  const missing = [];

  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  });

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * 验证数据库配置
 */
function validateDatabaseConfig() {
  const host = process.env.BB_MYSQL_HOST;
  const port = parseInt(process.env.BB_MYSQL_PORT);
  const user = process.env.BB_MYSQL_USER;
  const password = process.env.BB_MYSQL_PASSWD;

  if (!host || host.trim() === '') {
    throw new Error('Database host cannot be empty');
  }

  if (!port || port < 1 || port > 65535) {
    throw new Error('Database port must be between 1 and 65535');
  }

  if (!user || user.trim() === '') {
    throw new Error('Database user cannot be empty');
  }

  if (!password || password.length < 6) {
    console.warn('Warning: Database password is too short (less than 6 characters)');
  }
}

/**
 * 验证Redis配置
 */
function validateRedisConfig() {
  const host = process.env.BB_REDIS_HOST;
  const port = parseInt(process.env.BB_REDIS_PORT);

  if (!host || host.trim() === '') {
    throw new Error('Redis host cannot be empty');
  }

  if (!port || port < 1 || port > 65535) {
    throw new Error('Redis port must be between 1 and 65535');
  }
}

/**
 * 验证安全配置
 */
function validateSecurityConfig() {
  const env = process.env.NODE_ENV || 'development';

  // 生产环境安全检查
  if (env === 'production') {
    // 检查是否使用了默认密码
    const defaultPasswords = [ '123456', 'password', 'admin', 'root' ];
    const dbPassword = process.env.BB_MYSQL_PASSWD;

    if (defaultPasswords.includes(dbPassword)) {
      throw new Error('Cannot use default passwords in production environment');
    }

    // 检查Redis密码
    const redisPassword = process.env.BB_REDIS_PASSWORD;
    if (!redisPassword || redisPassword.length < 8) {
      console.warn('Warning: Redis password should be at least 8 characters in production');
    }
  }
}

/**
 * 验证URL配置
 */
function validateUrlConfig() {
  const urls = [
    'BB_SCRAPY_URL',
    'BB_TASK_API_URL',
    'BB_FRONTEND_URL',
    'BB_WEB_URL',
  ];

  urls.forEach(urlVar => {
    const url = process.env[urlVar];
    if (url) {
      try {
        new URL(url);
      } catch (error) {
        throw new Error(`Invalid URL format for ${urlVar}: ${url}`);
      }
    }
  });
}

/**
 * 生成配置报告
 */
function generateConfigReport() {
  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    requiredVars: {
      total: requiredEnvVars.length,
      configured: 0,
      missing: [],
    },
    optionalVars: {
      total: optionalEnvVars.length,
      configured: 0,
      missing: [],
    },
    security: {
      level: 'unknown',
      warnings: [],
    },
  };

  // 检查必需变量
  requiredEnvVars.forEach(varName => {
    if (process.env[varName]) {
      report.requiredVars.configured++;
    } else {
      report.requiredVars.missing.push(varName);
    }
  });

  // 检查可选变量
  optionalEnvVars.forEach(varName => {
    if (process.env[varName]) {
      report.optionalVars.configured++;
    } else {
      report.optionalVars.missing.push(varName);
    }
  });

  // 安全等级评估
  if (report.environment === 'production') {
    if (report.requiredVars.missing.length === 0) {
      report.security.level = 'good';
    } else {
      report.security.level = 'poor';
      report.security.warnings.push('Missing required environment variables in production');
    }
  } else {
    report.security.level = 'development';
  }

  return report;
}

/**
 * 主验证函数
 */
function validateConfig() {
  try {
    console.log('🔍 Validating configuration...');

    validateRequiredEnvVars();
    validateDatabaseConfig();
    validateRedisConfig();
    validateSecurityConfig();
    validateUrlConfig();

    const report = generateConfigReport();

    console.log('✅ Configuration validation passed');
    console.log(`📊 Configuration Report:
      Environment: ${report.environment}
      Required vars: ${report.requiredVars.configured}/${report.requiredVars.total}
      Optional vars: ${report.optionalVars.configured}/${report.optionalVars.total}
      Security level: ${report.security.level}
    `);

    if (report.security.warnings.length > 0) {
      console.warn('⚠️  Security warnings:');
      report.security.warnings.forEach(warning => {
        console.warn(`   - ${warning}`);
      });
    }

    return report;

  } catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
    throw error;
  }
}

module.exports = {
  validateConfig,
  generateConfigReport,
  validateRequiredEnvVars,
  validateDatabaseConfig,
  validateRedisConfig,
  validateSecurityConfig,
  validateUrlConfig,
};
