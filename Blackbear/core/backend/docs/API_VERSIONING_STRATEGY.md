# API版本管理策略

## 🎯 当前问题

当前路由配置存在以下版本管理问题：

1. **版本混乱**: 同时存在 `/api/v1`、`/api/web`、`/api/agent` 三套API
2. **缺少版本策略**: 没有明确的API版本升级和废弃策略
3. **向后兼容性**: 缺少版本间的兼容性保证
4. **文档不一致**: 不同版本的API文档不统一

## 📋 建议的版本管理策略

### 1. 统一API版本结构

```
/api/v1/          # 主要API版本 (当前稳定版)
/api/v2/          # 下一个主要版本 (开发中)
/api/web/         # Web端专用API (保持向后兼容)
/api/mobile/      # 移动端专用API (新增)
/api/internal/    # 内部服务API (替代agent)
```

### 2. 版本生命周期管理

- **开发阶段**: `/api/v2/` (alpha/beta)
- **稳定阶段**: `/api/v1/` (当前生产版本)
- **维护阶段**: `/api/v0/` (仅安全更新)
- **废弃阶段**: 6个月通知期后移除

### 3. 向后兼容性保证

- 同一主版本内保证向后兼容
- 新增字段不影响现有客户端
- 废弃字段保留6个月过渡期
- 提供迁移指南和工具

## 🔧 实施建议

1. **立即修复**: 为现有API添加版本头
2. **渐进迁移**: 逐步将agent API迁移到internal
3. **文档统一**: 使用OpenAPI 3.0规范
4. **监控追踪**: 监控各版本API使用情况
