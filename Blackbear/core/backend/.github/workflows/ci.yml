name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  # 代码质量检查
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run ESLint
      run: npm run lint
    
    - name: Fix code quality issues
      run: npm run fix-quality

  # 安全检查
  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run security audit
      run: npm run security-audit
    
    - name: Run security check
      run: npm run security-check

  # 单元测试
  test:
    runs-on: ubuntu-latest
    needs: [lint, security]
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root123
          MYSQL_DATABASE: blackbear_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Wait for MySQL
      run: |
        while ! mysqladmin ping -h"127.0.0.1" -P3306 -uroot -proot123 --silent; do
          sleep 1
        done
    
    - name: Wait for Redis
      run: |
        while ! redis-cli -h 127.0.0.1 -p 6379 ping; do
          sleep 1
        done
    
    - name: Run unit tests
      run: npm run test:unit
      env:
        NODE_ENV: test
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_USERNAME: root
        DB_PASSWORD: root123
        DB_DATABASE: blackbear_test
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
    
    - name: Run integration tests
      run: npm run test:integration
      env:
        NODE_ENV: test
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_USERNAME: root
        DB_PASSWORD: root123
        DB_DATABASE: blackbear_test
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
    
    - name: Generate coverage report
      run: npm run cov
      env:
        NODE_ENV: test
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_USERNAME: root
        DB_PASSWORD: root123
        DB_DATABASE: blackbear_test
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info

  # 性能测试
  performance:
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root123
          MYSQL_DATABASE: blackbear_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Start application
      run: |
        npm start &
        sleep 30
      env:
        NODE_ENV: production
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_USERNAME: root
        DB_PASSWORD: root123
        DB_DATABASE: blackbear_test
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
    
    - name: Wait for application
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:7001/api/health; do sleep 2; done'
    
    - name: Run performance benchmark
      run: npm run benchmark
      env:
        BB_API_URL: http://localhost:7001
        BENCHMARK_CONCURRENCY: 5
        BENCHMARK_DURATION: 30000

  # Docker构建
  docker:
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.optimized
        push: true
        tags: |
          blackbear/backend:latest
          blackbear/backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 这里添加实际的部署脚本

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [docker, performance]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的部署脚本
