# Blackbear Backend Environment Variables
# 复制此文件为 .env 并根据实际环境配置

# ==================== 应用基础配置 ====================
NODE_ENV=development
PORT=7001
HOSTNAME=0.0.0.0

# 应用密钥
EGG_KEYS=blackbear_1570610257068_8645_production_key
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# ==================== 数据库配置 ====================
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=blackbear
DB_PASSWORD=blackbear123
DB_DATABASE=blackbear

# 数据库连接池配置
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# ==================== Redis配置 ====================
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CACHE_DB=2

# ==================== 安全配置 ====================
# CORS允许的域名（生产环境用逗号分隔）
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# 域名白名单
DOMAIN_WHITELIST=localhost,yourdomain.com

# ==================== 会话配置 ====================
SESSION_MAX_AGE=86400000
JWT_EXPIRES_IN=7d

# ==================== 文件上传配置 ====================
UPLOAD_FILE_SIZE=10mb
UPLOAD_PATH=/app/uploads

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
CONSOLE_LOG_LEVEL=INFO

# ==================== 限流配置 ====================
RATE_LIMIT_MAX=100
RATE_LIMIT_DURATION=60000

# ==================== 监控配置 ====================
MONITORING_ENABLED=true
MONITORING_ENDPOINT=/metrics

# ==================== API配置 ====================
BB_API_URL=http://localhost:7001

# ==================== 第三方服务配置 ====================
# 阿里云OSS配置
ALIOSS_ACCESS_KEY_ID=
ALIOSS_ACCESS_KEY_SECRET=
ALIOSS_BUCKET=
ALIOSS_REGION=

# MinIO配置
MINIO_ENDPOINT=
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET=

# ==================== 外部API配置 ====================
# 微信API配置
WECHAT_APP_ID=
WECHAT_APP_SECRET=

# 微博API配置
WEIBO_APP_KEY=
WEIBO_APP_SECRET=

# 雪球API配置
XUEQIU_TOKEN=

# 百度API配置
BAIDU_API_KEY=
BAIDU_SECRET_KEY=

# 必应API配置
BING_API_KEY=

# ==================== 数据库配置（生产环境）====================
# 生产环境MySQL配置
MYSQL_ROOT_PASSWORD=your-super-secure-root-password

# ==================== 监控和日志配置 ====================
# Elasticsearch配置
ELASTICSEARCH_HOSTS=http://elasticsearch:9200

# Grafana配置
GRAFANA_PASSWORD=admin123

# ==================== 性能优化配置 ====================
# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_L1_TTL=60
CACHE_L2_TTL=300
CACHE_L3_TTL=3600

# 查询优化配置
QUERY_SLOW_THRESHOLD=1000
QUERY_CACHE_ENABLED=true

# 响应优化配置
RESPONSE_COMPRESSION_ENABLED=true
RESPONSE_COMPRESSION_THRESHOLD=1024

# APM配置
APM_ENABLED=true
APM_SAMPLING_RATE=0.1
APM_SLOW_REQUEST_THRESHOLD=1000

# ==================== 基准测试配置 ====================
BENCHMARK_CONCURRENCY=10
BENCHMARK_DURATION=30000

# ==================== 开发环境配置 ====================
# 开发环境特殊配置
DEV_WATCH_DIRS=app,config,lib
DEV_IGNORE_DIRS=app/public,app/views,logs
DEV_FAST_READY=true
DEV_RELOAD_ON_DEBUG=true
