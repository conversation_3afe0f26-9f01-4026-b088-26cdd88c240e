#!/bin/bash

# 开发环境启动脚本
# 加载环境变量并启动应用

echo "🚀 Starting Blackbear Backend in Development Mode..."

# 检查环境变量文件是否存在
if [ ! -f "../../config/test.sh" ]; then
    echo "❌ ../../config/test.sh file not found!"
    echo "Please ensure the config/test.sh file exists in the project root."
    exit 1
fi

# 加载环境变量
echo "📋 Loading environment variables from ../../config/test.sh..."
source ../../config/test.sh

# 显示配置信息
echo "🔧 Configuration:"
echo "   NODE_ENV: $NODE_ENV"
echo "   Database: $BB_MYSQL_HOST:$BB_MYSQL_PORT/$BB_MYSQL_DBNAME"
echo "   Redis: $BB_REDIS_HOST:$BB_REDIS_PORT"
echo "   API URL: $BB_API_URL"
echo "   Port: ${BB_PORT:-7001}"
echo "   Security: ${BB_ENHANCED_SECURITY_ENABLED:-true}"
echo "   Monitoring: ${BB_MONITORING_ENABLED:-true}"

# 检查数据库连接（可选）
echo "🔍 Checking database connection..."
if command -v mysql &> /dev/null; then
    if mysql -h"$BB_MYSQL_HOST" -P"$BB_MYSQL_PORT" -u"$BB_MYSQL_USER" -p"$BB_MYSQL_PASSWD" -e "SELECT 1;" &> /dev/null; then
        echo "✅ Database connection successful"
    else
        echo "⚠️  Database connection failed, but continuing..."
    fi
else
    echo "⚠️  MySQL client not found, skipping database check"
fi

# 检查Redis连接（可选）
echo "🔍 Checking Redis connection..."
if command -v redis-cli &> /dev/null; then
    if redis-cli -h "$BB_REDIS_HOST" -p "$BB_REDIS_PORT" ping &> /dev/null; then
        echo "✅ Redis connection successful"
    else
        echo "⚠️  Redis connection failed, but continuing..."
    fi
else
    echo "⚠️  Redis client not found, skipping Redis check"
fi

# 启动应用
echo "🎯 Starting application..."
npm run dev
