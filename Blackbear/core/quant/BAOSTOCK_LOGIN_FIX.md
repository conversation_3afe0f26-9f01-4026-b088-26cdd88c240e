# BaoStock登录问题修复报告

## 问题描述
在使用BaoStock数据源时出现错误：
```
2025-07-16 07:14:20,181 [ERROR] 查询股票 sh.600667 历史数据失败: 用户未登录
```

## 问题原因分析

### 1. 多实例问题
原始代码中存在多个地方创建 `Baostock` 实例，导致登录状态管理混乱：
- 不同模块创建独立的实例
- 登录状态无法在实例间共享
- 可能导致某些实例未正确登录

### 2. 登录状态检查不够严格
原始的 `_ensure_logged_in()` 方法只检查本地状态标志，但实际的BaoStock连接可能已经断开：
```python
def _ensure_logged_in(self):
    if not self.is_logged_in:
        if not self.login():
            raise ConnectionError("无法连接到BaoStock服务")
```

### 3. 缺乏重连机制
当BaoStock连接断开时，没有自动重连机制，导致后续查询失败。

### 4. 线程安全问题
在多线程环境下，登录状态可能被并发修改，导致状态不一致。

## 解决方案

### 1. 实现单例模式
确保全局只有一个BaoStock实例：

```python
class BaostockDataSource:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, auto_login: bool = True):
        """单例模式，确保全局只有一个BaoStock实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(BaostockDataSource, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
```

### 2. 增强登录状态检查
添加实际连接验证，而不仅仅依赖本地状态：

```python
def _ensure_logged_in(self):
    """确保已登录，包含重连机制"""
    # 首先检查本地登录状态
    if not self.is_logged_in:
        if not self.login():
            raise ConnectionError("无法连接到BaoStock服务")
        return
    
    # 即使本地状态显示已登录，也要验证连接是否有效
    try:
        # 使用一个轻量级的查询来测试连接
        test_rs = bs.query_trade_dates(start_date="2024-01-01", end_date="2024-01-01")
        if test_rs.error_code != '0':
            # 连接无效，需要重新登录
            logger.warning(f"BaoStock连接已断开，尝试重新登录: {test_rs.error_msg}")
            self.is_logged_in = False
            if not self.login():
                raise ConnectionError("重新连接BaoStock服务失败")
    except Exception as e:
        # 连接测试失败，重新登录
        logger.warning(f"BaoStock连接测试失败，尝试重新登录: {e}")
        self.is_logged_in = False
        if not self.login():
            raise ConnectionError("重新连接BaoStock服务失败")
```

### 3. 添加重试机制
为登录和查询操作添加重试机制：

```python
def login(self, max_retries: int = 3) -> bool:
    """登录BaoStock，带重试机制和线程安全"""
    with self._lock:
        # 如果已经登录，直接返回
        if self.is_logged_in:
            return True
            
        for attempt in range(max_retries):
            try:
                # 先尝试登出，确保清理之前的连接
                try:
                    bs.logout()
                except:
                    pass
                
                # 短暂延迟，避免频繁重连
                if attempt > 0:
                    time.sleep(1)
                
                self.login_info = bs.login()

                if self.login_info.error_code == '0':
                    self.is_logged_in = True
                    logger.info(f"BaoStock登录成功 (尝试 {attempt + 1}/{max_retries})")
                    return True
                else:
                    logger.warning(f"BaoStock登录失败 (尝试 {attempt + 1}/{max_retries}): {self.login_info.error_msg}")

            except Exception as e:
                logger.warning(f"BaoStock登录异常 (尝试 {attempt + 1}/{max_retries}): {e}")
        
        self.is_logged_in = False
        return False
```

### 4. 查询重试机制
为查询操作添加重试机制，特别处理"用户未登录"错误：

```python
# 执行查询，带重试机制
max_retries = 2
for attempt in range(max_retries):
    rs = bs.query_history_k_data_plus(...)

    if rs.error_code == '0':
        break
    elif rs.error_msg and "用户未登录" in rs.error_msg:
        # 登录状态失效，重新登录后重试
        logger.warning(f"检测到登录状态失效，重新登录后重试 (尝试 {attempt + 1}/{max_retries})")
        self.is_logged_in = False
        self._ensure_logged_in()
        if attempt == max_retries - 1:
            logger.error(f"查询股票 {stock_code} 历史数据失败: {rs.error_msg}")
            return pd.DataFrame()
    else:
        logger.error(f"查询股票 {stock_code} 历史数据失败: {rs.error_msg}")
        return pd.DataFrame()
```

### 5. 全局实例管理
提供全局实例管理器：

```python
# 全局实例管理
_global_baostock_instance = None
_global_lock = threading.Lock()

def get_baostock_instance() -> BaostockDataSource:
    """获取全局BaoStock实例"""
    global _global_baostock_instance
    if _global_baostock_instance is None:
        with _global_lock:
            if _global_baostock_instance is None:
                _global_baostock_instance = BaostockDataSource(auto_login=True)
                logger.info('全局BaoStock实例初始化完成')
    return _global_baostock_instance
```

## 修复效果验证

经过修复后，测试结果显示：

```
🧪 开始测试BaoStock...
📦 创建BaoStock实例...
login success!
🔐 登录状态: True
📊 测试查询股票数据...
   股票代码: sh.600667
   开始日期: 2024-01-01
   结束日期: 2024-01-02
✅ 查询成功！获取到 1 条记录
🎉 BaoStock测试成功！
```

## 修复的关键改进

1. **单例模式**：确保全局只有一个BaoStock实例，避免多实例问题
2. **连接验证**：不仅检查本地状态，还验证实际连接有效性
3. **自动重连**：当连接断开时自动重新登录
4. **重试机制**：为登录和查询操作添加重试机制
5. **线程安全**：使用锁确保多线程环境下的状态一致性
6. **错误处理**：特别处理"用户未登录"错误，自动重新登录

## 总结

通过实现单例模式、增强连接验证、添加重试机制和改善错误处理，彻底解决了BaoStock"用户未登录"的问题。现在BaoStock连接更加稳定可靠，能够自动处理连接断开和重连，确保数据查询的连续性和可靠性。
