# -*- coding: utf-8 -*-
"""
数据重置工具系统 (优化版本)

0 0 0 * * *

主要功能:
1. 历史数据清理 (股票历史、分时数据)
2. 新闻数据管理 (清理过期、标记已读)
3. 研报和媒体数据清理
4. 任务系统状态重置
5. 数据完整性维护

优化特性:
- 完善的错误处理和日志记录
- 安全确认机制
- 批量操作优化
- 模块化设计和类型提示
- 详细的清理统计
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import time
import argparse
from typing import Dict, List, Optional
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import (Task, News, NewsStock, Report, Research, Media,
                      Stock, Jieba, Aggregation, Daliy, StockMin,
                      StockHistory, MediaStock)

# 工具类
from utils.helper import ago_day_timestr, ago_day_timestamp
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 数据获取
from db.fetch import fetch_quant_raw, quant_monitor

@dataclass
class CleanupStats:
    """清理统计"""
    table_name: str
    deleted_count: int
    updated_count: int = 0
    description: str = ""

class DataResetProcessor:
    """数据重置处理器"""

    def __init__(self):
        self.settings = get_settings()
        self.quant_raw = fetch_quant_raw()
        self.cleanup_stats = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def add_cleanup_stat(self, table_name: str, deleted_count: int,
                        updated_count: int = 0, description: str = ""):
        """添加清理统计"""
        stat = CleanupStats(
            table_name=table_name,
            deleted_count=deleted_count,
            updated_count=updated_count,
            description=description
        )
        self.cleanup_stats.append(stat)

    @monitor_performance
    def cleanup_stock_trend(self) -> bool:
        """清理股票趋势表"""
        try:
            logger.info("开始清理股票趋势表")

            with db_manager.get_session() as session:
                result = session.execute('TRUNCATE TABLE stocktrend')
                session.commit()

            self.add_cleanup_stat('stocktrend', -1, description='全表清空')
            logger.info("✅ 股票趋势表清理完成")
            return True

        except Exception as e:
            logger.error(f"清理股票趋势表失败: {e}")
            return False

    @monitor_performance
    def cleanup_stock_history(self, retain_days: int = 730) -> bool:
        """清理股票历史数据"""
        try:
            logger.info(f"开始清理股票历史数据 (保留{retain_days}天)")

            cutoff_date = ago_day_timestr(retain_days, '%Y-%m-%d')

            with db_manager.get_session() as session:
                deleted_count = session.query(StockHistory).filter(
                    StockHistory.date < cutoff_date
                ).count()

                session.query(StockHistory).filter(
                    StockHistory.date < cutoff_date
                ).delete()
                session.commit()

            self.add_cleanup_stat('stockhistory', deleted_count,
                                description=f'删除{cutoff_date}之前的数据')
            logger.info(f"✅ 股票历史数据清理完成: 删除 {deleted_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"清理股票历史数据失败: {e}")
            return False

    @monitor_performance
    def cleanup_stock_min(self) -> bool:
        """清理股票分时数据"""
        try:
            retain_days = self.quant_raw.stockMinRetainDay if self.quant_raw else 30
            logger.info(f"开始清理股票分时数据 (保留{retain_days}天)")

            cutoff_date = ago_day_timestr(retain_days, '%Y-%m-%d')

            with db_manager.get_session() as session:
                deleted_count = session.query(StockMin).filter(
                    StockMin.tradedate < cutoff_date
                ).count()

                session.query(StockMin).filter(
                    StockMin.tradedate < cutoff_date
                ).delete()
                session.commit()

            self.add_cleanup_stat('stockmin', deleted_count,
                                description=f'删除{cutoff_date}之前的数据')
            logger.info(f"✅ 股票分时数据清理完成: 删除 {deleted_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"清理股票分时数据失败: {e}")
            return False

    @monitor_performance
    def cleanup_news_data(self) -> bool:
        """清理新闻数据"""
        try:
            retain_days = self.quant_raw.newsRetainDay if self.quant_raw else 90
            read_days = self.quant_raw.newsReadDay if self.quant_raw else 7

            logger.info(f"开始清理新闻数据 (保留{retain_days}天, 标记已读{read_days}天)")

            # 清理过期新闻
            cutoff_time = ago_day_timestr(retain_days, '%Y-%m-%d %H:%M:%S')

            with db_manager.get_session() as session:
                # 删除过期的非重要新闻
                deleted_news = session.query(News).filter(
                    News.time < cutoff_time,
                    News.important == 0
                ).count()

                session.query(News).filter(
                    News.time < cutoff_time,
                    News.important == 0
                ).delete()

                # 删除过期的新闻-股票关联
                deleted_newsstock = session.query(NewsStock).filter(
                    NewsStock.createdAt < cutoff_time
                ).count()

                session.query(NewsStock).filter(
                    NewsStock.createdAt < cutoff_time
                ).delete()

                session.commit()

            # 标记已读
            read_cutoff = ago_day_timestr(read_days, '%Y-%m-%d %H:%M:%S')

            with db_manager.get_session() as session:
                updated_count = session.query(News).filter(
                    News.time < read_cutoff
                ).update({"read": 1})
                session.commit()

            self.add_cleanup_stat('news', deleted_news, updated_count,
                                f'删除过期新闻，标记{read_days}天前为已读')
            self.add_cleanup_stat('newsstock', deleted_newsstock,
                                description='删除过期新闻-股票关联')

            logger.info(f"✅ 新闻数据清理完成: 删除 {deleted_news} 条新闻, "
                       f"{deleted_newsstock} 条关联, 标记已读 {updated_count} 条")
            return True

        except Exception as e:
            logger.error(f"清理新闻数据失败: {e}")
            return False

    @monitor_performance
    def cleanup_aggregation_data(self) -> bool:
        """清理聚合数据"""
        try:
            retain_days = self.quant_raw.aggregationRetainDay if self.quant_raw else 30
            logger.info(f"开始清理聚合数据 (保留{retain_days}天)")

            cutoff_date = ago_day_timestr(retain_days, '%Y-%m-%d')

            with db_manager.get_session() as session:
                # 删除过期数据
                deleted_old = session.query(Aggregation).filter(
                    Aggregation.createdAt < cutoff_date
                ).count()

                session.query(Aggregation).filter(
                    Aggregation.createdAt < cutoff_date
                ).delete()

                # 删除已读数据
                deleted_read = session.query(Aggregation).filter(
                    Aggregation.read == True
                ).count()

                session.query(Aggregation).filter(
                    Aggregation.read == True
                ).delete()

                session.commit()

            total_deleted = deleted_old + deleted_read
            self.add_cleanup_stat('aggregation', total_deleted,
                                description=f'删除过期和已读数据')

            logger.info(f"✅ 聚合数据清理完成: 删除 {total_deleted} 条记录")
            return True

        except Exception as e:
            logger.error(f"清理聚合数据失败: {e}")
            return False

    @monitor_performance
    def cleanup_report_data(self) -> bool:
        """清理公告数据"""
        try:
            retain_days = self.quant_raw.reportRetainDay if self.quant_raw else 180
            logger.info(f"开始清理公告数据 (保留{retain_days}天)")

            cutoff_timestamp = ago_day_timestamp(retain_days)

            with db_manager.get_session() as session:
                deleted_count = session.query(Report).filter(
                    Report.announcementTime < cutoff_timestamp
                ).count()

                session.query(Report).filter(
                    Report.announcementTime < cutoff_timestamp
                ).delete()
                session.commit()

            self.add_cleanup_stat('report', deleted_count,
                                description=f'删除{retain_days}天前的公告')

            logger.info(f"✅ 公告数据清理完成: 删除 {deleted_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"清理公告数据失败: {e}")
            return False

    @monitor_performance
    def cleanup_research_data(self) -> bool:
        """清理研报数据"""
        try:
            retain_days = self.quant_raw.researchRetainDay if self.quant_raw else 180
            logger.info(f"开始清理研报数据 (保留{retain_days}天)")

            cutoff_date = ago_day_timestr(retain_days, '%Y-%m-%d')

            with db_manager.get_session() as session:
                deleted_count = session.query(Research).filter(
                    Research.publishdate < cutoff_date,
                    Research.important == 0
                ).count()

                session.query(Research).filter(
                    Research.publishdate < cutoff_date,
                    Research.important == 0
                ).delete()
                session.commit()

            self.add_cleanup_stat('research', deleted_count,
                                description=f'删除{retain_days}天前的非重要研报')

            logger.info(f"✅ 研报数据清理完成: 删除 {deleted_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"清理研报数据失败: {e}")
            return False

    @monitor_performance
    def cleanup_media_data(self) -> bool:
        """清理媒体数据"""
        try:
            retain_days = self.quant_raw.mediaRetainDay if self.quant_raw else 90
            read_days = self.quant_raw.mediaReadDay if self.quant_raw else 7

            logger.info(f"开始清理媒体数据 (保留{retain_days}天, 标记已读{read_days}天)")

            # 清理过期媒体文章
            cutoff_date = ago_day_timestr(retain_days, '%Y-%m-%d')

            with db_manager.get_session() as session:
                deleted_count = session.query(Media).filter(
                    Media.createTime < cutoff_date,
                    Media.important == 0
                ).count()

                session.query(Media).filter(
                    Media.createTime < cutoff_date,
                    Media.important == 0
                ).delete()
                session.commit()

            # 清理孤立的媒体-股票关联
            with db_manager.get_session() as session:
                deleted_mediastock = session.query(MediaStock).filter(
                    MediaStock.mediaId.notin_(session.query(Media.id))
                ).count()

                session.query(MediaStock).filter(
                    MediaStock.mediaId.notin_(session.query(Media.id))
                ).delete(synchronize_session=False)
                session.commit()

            # 标记已读
            read_cutoff = ago_day_timestr(read_days, '%Y-%m-%d')

            with db_manager.get_session() as session:
                updated_count = session.query(Media).filter(
                    Media.createTime < read_cutoff
                ).update({"read": 1})
                session.commit()

            self.add_cleanup_stat('media', deleted_count, updated_count,
                                f'删除过期媒体文章，标记{read_days}天前为已读')
            self.add_cleanup_stat('mediastock', deleted_mediastock,
                                description='删除孤立的媒体-股票关联')

            logger.info(f"✅ 媒体数据清理完成: 删除 {deleted_count} 条媒体文章, "
                       f"{deleted_mediastock} 条关联, 标记已读 {updated_count} 条")
            return True

        except Exception as e:
            logger.error(f"清理媒体数据失败: {e}")
            return False

    @monitor_performance
    def reset_jieba_ranking(self) -> bool:
        """重置分词排名"""
        try:
            logger.info("开始重置分词排名")

            with db_manager.get_session() as session:
                # 重置lastranking
                updated_count = session.query(Jieba).update({"lastranking": 0})
                session.commit()

            # 更新lastranking为当前ranking
            with engine.connect() as conn:
                conn.execute("UPDATE jieba SET lastranking = ranking")

            self.add_cleanup_stat('jieba', 0, updated_count, '重置分词排名')

            logger.info(f"✅ 分词排名重置完成: 更新 {updated_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"重置分词排名失败: {e}")
            return False

    @monitor_performance
    def reset_task_status(self) -> bool:
        """重置任务状态"""
        try:
            logger.info("开始重置任务状态")

            with db_manager.get_session() as session:
                updated_count = session.query(Task).update({
                    "success": 0,
                    "fail": 0
                })
                session.commit()

            self.add_cleanup_stat('task', 0, updated_count, '重置任务状态')

            logger.info(f"✅ 任务状态重置完成: 更新 {updated_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"重置任务状态失败: {e}")
            return False

    @monitor_performance
    def ensure_daily_record(self) -> bool:
        """确保每日记录存在"""
        try:
            logger.info("确保每日记录存在")

            today = time.strftime('%Y%m%d', time.localtime())

            with db_manager.get_session() as session:
                new_daily = Daliy(date=today)
                session.merge(new_daily)
                session.commit()

            logger.info(f"✅ 每日记录确保完成: {today}")
            return True

        except Exception as e:
            logger.error(f"确保每日记录失败: {e}")
            return False

    def print_cleanup_summary(self):
        """打印清理摘要"""
        try:
            logger.info("=" * 60)
            logger.info("🧹 数据清理摘要")
            logger.info("=" * 60)

            total_deleted = 0
            total_updated = 0

            for stat in self.cleanup_stats:
                if stat.deleted_count > 0 or stat.updated_count > 0:
                    logger.info(f"📊 {stat.table_name}:")
                    if stat.deleted_count > 0:
                        logger.info(f"   删除: {stat.deleted_count:,} 条记录")
                        total_deleted += stat.deleted_count if stat.deleted_count > 0 else 0
                    if stat.updated_count > 0:
                        logger.info(f"   更新: {stat.updated_count:,} 条记录")
                        total_updated += stat.updated_count
                    if stat.description:
                        logger.info(f"   说明: {stat.description}")
                    logger.info("")

            logger.info(f"📈 总计: 删除 {total_deleted:,} 条记录, 更新 {total_updated:,} 条记录")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"打印清理摘要失败: {e}")

    @monitor_performance
    def reset_all_data(self, confirm: bool = False) -> bool:
        """重置所有数据"""
        try:
            if not confirm:
                logger.warning("数据重置需要确认，请使用 --confirm 参数")
                return False

            logger.info("🚀 开始数据重置操作")

            success_count = 0
            operations = [
                ("清理股票趋势表", self.cleanup_stock_trend),
                ("清理股票历史数据", self.cleanup_stock_history),
                ("清理股票分时数据", self.cleanup_stock_min),
                ("清理新闻数据", self.cleanup_news_data),
                ("清理聚合数据", self.cleanup_aggregation_data),
                ("清理公告数据", self.cleanup_report_data),
                ("清理研报数据", self.cleanup_research_data),
                ("清理媒体数据", self.cleanup_media_data),
                ("重置分词排名", self.reset_jieba_ranking),
                ("重置任务状态", self.reset_task_status),
                ("确保每日记录", self.ensure_daily_record),
            ]

            for desc, operation in operations:
                try:
                    if operation():
                        success_count += 1
                        logger.info(f"✅ {desc} 完成")
                    else:
                        logger.error(f"❌ {desc} 失败")
                except Exception as e:
                    logger.error(f"❌ {desc} 异常: {e}")

            # 打印摘要
            self.print_cleanup_summary()

            logger.info(f"数据重置操作完成: 成功 {success_count}/{len(operations)} 个操作")
            return success_count == len(operations)

        except Exception as e:
            logger.error(f"数据重置操作失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def reset_data(confirm: bool = False) -> bool:
    """数据重置 - 优化版本"""
    with DataResetProcessor() as processor:
        return processor.reset_all_data(confirm)

@monitor_performance
def main():
    """
    主函数 - 执行数据重置工具系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始数据重置工具系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="数据重置工具系统 (优化版本)")
        arg_parser.add_argument("--confirm", action='store_true',
                               help="确认执行数据重置操作 (必需)")
        args = arg_parser.parse_args()

        confirm = args.confirm

        if not confirm:
            logger.warning("⚠️  数据重置是危险操作，需要明确确认")
            logger.warning("请使用 --confirm 参数确认执行")
            return False

        logger.info("执行参数: 确认重置=True")

        # 执行数据重置
        success = reset_data(confirm)

        # 更新监控状态
        if success:
            quant_monitor(32, True)
            logger.info("✅ 数据重置工具系统执行完成")
        else:
            quant_monitor(32, False)
            logger.error("❌ 数据重置工具系统执行失败")

        return success

    except Exception as e:
        logger.error(f"数据重置工具系统执行失败: {e}")
        quant_monitor(32, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)