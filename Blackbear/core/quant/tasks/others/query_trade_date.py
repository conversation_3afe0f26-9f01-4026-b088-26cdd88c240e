# -*- coding: UTF-8 -*-
"""
交易日期查询系统 (优化版本)

0 0 0 1 1 ?

主要功能:
1. 获取历史交易日期数据
2. 更新交易日历到数据库
3. 支持自定义时间范围
4. 节假日和周末识别

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import datetime
import time
import argparse
from typing import List, Dict, Optional, Set
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession
from db.models import Daliy

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool, ago_day_timestr
from config.settings import get_settings

# 第三方库
import akshare as ak
import pandas as pd

# 数据获取
from db.fetch import quant_monitor

@dataclass
class TradeDateInfo:
    """交易日期信息"""
    date: str
    is_open: bool
    weekday: int
    is_holiday: bool = False

class TradeDateProcessor:
    """交易日期处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 已知节假日配置 (可以从配置文件读取)
        self.known_holidays = {
            # 2024年节假日示例
            '20240101', '20240210', '20240211', '20240212', '20240213', '20240214', '20240215', '20240216',
            '20240404', '20240405', '20240406',
            '20240501', '20240502', '20240503',
            '20240610',
            '20240915', '20240916', '20240917',
            '20241001', '20241002', '20241003', '20241004', '20241005', '20241006', '20241007'
        }

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_trade_dates_from_akshare(self) -> Optional[pd.DataFrame]:
        """
        从akshare获取交易日期数据

        :return: 交易日期DataFrame
        """
        try:
            logger.info("从akshare获取交易日期数据")

            # 获取历史交易日期
            trade_dates_df = ak.tool_trade_date_hist_sina()

            if trade_dates_df.empty:
                logger.error("获取交易日期数据失败")
                return None

            logger.info(f"获取到 {len(trade_dates_df)} 个交易日期")
            return trade_dates_df

        except Exception as e:
            logger.error(f"从akshare获取交易日期失败: {e}")
            return None

    def generate_date_range(self, start_date: str, end_date: str) -> List[TradeDateInfo]:
        """
        生成日期范围内的交易日期信息

        :param start_date: 开始日期 (YYYYMMDD)
        :param end_date: 结束日期 (YYYYMMDD)
        :return: 交易日期信息列表
        """
        try:
            logger.info(f"生成日期范围: {start_date} - {end_date}")

            start_dt = datetime.datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.datetime.strptime(end_date, '%Y%m%d')

            trade_date_list = []
            current_dt = start_dt

            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y%m%d')
                weekday = current_dt.weekday()  # 0=Monday, 6=Sunday

                # 判断是否为交易日
                is_weekend = weekday >= 5  # Saturday=5, Sunday=6
                is_holiday = date_str in self.known_holidays
                is_open = not (is_weekend or is_holiday)

                trade_date_info = TradeDateInfo(
                    date=date_str,
                    is_open=is_open,
                    weekday=weekday,
                    is_holiday=is_holiday
                )
                trade_date_list.append(trade_date_info)

                current_dt += datetime.timedelta(days=1)

            logger.info(f"生成 {len(trade_date_list)} 个日期，其中交易日 {sum(1 for d in trade_date_list if d.is_open)} 个")
            return trade_date_list

        except Exception as e:
            logger.error(f"生成日期范围失败: {e}")
            return []

    def parse_akshare_data(self, trade_dates_df: pd.DataFrame, year_filter: str = None) -> List[TradeDateInfo]:
        """
        解析akshare交易日期数据

        :param trade_dates_df: akshare交易日期DataFrame
        :param year_filter: 年份过滤器 (YYYY)
        :return: 交易日期信息列表
        """
        try:
            trade_date_list = []

            for _, row in trade_dates_df.iterrows():
                trade_date_str = str(row['trade_date'])

                # 年份过滤
                if year_filter and not trade_date_str.startswith(year_filter):
                    continue

                # 转换日期格式
                date_formatted = trade_date_str.replace('-', '')

                # 获取星期几
                try:
                    dt = datetime.datetime.strptime(date_formatted, '%Y%m%d')
                    weekday = dt.weekday()
                except:
                    weekday = 0

                trade_date_info = TradeDateInfo(
                    date=date_formatted,
                    is_open=True,  # akshare返回的都是交易日
                    weekday=weekday,
                    is_holiday=False
                )
                trade_date_list.append(trade_date_info)

            logger.info(f"解析akshare数据完成: {len(trade_date_list)} 个交易日")
            return trade_date_list

        except Exception as e:
            logger.error(f"解析akshare数据失败: {e}")
            return []

    def save_trade_dates(self, trade_date_list: List[TradeDateInfo]) -> bool:
        """
        保存交易日期到数据库

        :param trade_date_list: 交易日期信息列表
        :return: 是否成功
        """
        try:
            if not trade_date_list:
                logger.warning("没有交易日期数据需要保存")
                return False

            with db_manager.get_session() as session:
                success_count = 0

                for trade_date_info in trade_date_list:
                    try:
                        new_daily = Daliy(
                            date=trade_date_info.date,
                            isOpen=trade_date_info.is_open
                        )
                        session.merge(new_daily)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"保存日期 {trade_date_info.date} 失败: {e}")
                        continue

                session.commit()

            logger.info(f"交易日期保存完成: 成功 {success_count}/{len(trade_date_list)} 条记录")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存交易日期失败: {e}")
            return False

    @monitor_performance
    def update_trade_dates_from_akshare(self, year_filter: str = None) -> bool:
        """
        从akshare更新交易日期

        :param year_filter: 年份过滤器
        :return: 是否成功
        """
        try:
            logger.info("开始从akshare更新交易日期")

            # 获取数据
            trade_dates_df = self.get_trade_dates_from_akshare()
            if trade_dates_df is None:
                return False

            # 解析数据
            trade_date_list = self.parse_akshare_data(trade_dates_df, year_filter)
            if not trade_date_list:
                return False

            # 保存数据
            success = self.save_trade_dates(trade_date_list)

            if success:
                logger.info("✅ 从akshare更新交易日期完成")
            else:
                logger.error("❌ 从akshare更新交易日期失败")

            return success

        except Exception as e:
            logger.error(f"从akshare更新交易日期失败: {e}")
            return False

    @monitor_performance
    def update_trade_dates_by_range(self, start_date: str, end_date: str) -> bool:
        """
        按日期范围更新交易日期

        :param start_date: 开始日期 (YYYYMMDD)
        :param end_date: 结束日期 (YYYYMMDD)
        :return: 是否成功
        """
        try:
            logger.info(f"开始按日期范围更新交易日期: {start_date} - {end_date}")

            # 生成日期范围
            trade_date_list = self.generate_date_range(start_date, end_date)
            if not trade_date_list:
                return False

            # 保存数据
            success = self.save_trade_dates(trade_date_list)

            if success:
                logger.info("✅ 按日期范围更新交易日期完成")
            else:
                logger.error("❌ 按日期范围更新交易日期失败")

            return success

        except Exception as e:
            logger.error(f"按日期范围更新交易日期失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def query_trade_date(year_filter: str = None) -> bool:
    """交易日期查询 - 优化版本"""
    with TradeDateProcessor() as processor:
        return processor.update_trade_dates_from_akshare(year_filter)

def update_trade_dates(start_date: str, end_date: str) -> bool:
    """更新交易日期 - 优化版本"""
    with TradeDateProcessor() as processor:
        return processor.update_trade_dates_by_range(start_date, end_date)

@monitor_performance
def main():
    """
    主函数 - 执行交易日期查询系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始交易日期查询系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="交易日期查询系统 (优化版本)")
        arg_parser.add_argument("-y", "--year", required=False, type=str,
                               help="年份过滤器 YYYY")
        arg_parser.add_argument("-s", "--start", required=False, type=str,
                               help="开始日期 YYYYMMDD")
        arg_parser.add_argument("-e", "--end", required=False, type=str,
                               help="结束日期 YYYYMMDD")
        arg_parser.add_argument("-m", "--mode", required=False, type=str,
                               choices=['akshare', 'range'], default='akshare',
                               help="更新模式: akshare(从akshare获取) 或 range(按日期范围)")
        args = arg_parser.parse_args()

        year_filter = args.year or time.strftime('%Y', time.localtime())
        start_date = args.start
        end_date = args.end
        mode = args.mode

        logger.info(f"执行参数: 年份={year_filter}, 开始日期={start_date}, 结束日期={end_date}, 模式={mode}")

        with TradeDateProcessor() as processor:
            if mode == 'range' and start_date and end_date:
                # 按日期范围更新
                success = processor.update_trade_dates_by_range(start_date, end_date)
            else:
                # 从akshare更新
                success = processor.update_trade_dates_from_akshare(year_filter)

        # 更新监控状态
        if success:
            quant_monitor(25, True)
            logger.info("✅ 交易日期查询系统执行完成")
        else:
            quant_monitor(25, False)
            logger.error("❌ 交易日期查询系统执行失败")

        return success

    except Exception as e:
        logger.error(f"交易日期查询系统执行失败: {e}")
        quant_monitor(25, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)

