# -*- coding: UTF-8 -*-
"""
CNI指数历史数据查询系统 (优化版本)

0 0 20,21,22,23 * * 1-5

主要功能:
1. 获取中证指数历史数据
2. 获取国证指数历史数据
3. 计算相对沪深300的偏离度
4. 支持多种指数类型
5. 数据清洗和验证

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import argparse
import hashlib
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession
from db.models import Csindex

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 第三方库
import akshare as ak
import pandas as pd

# 数据获取
from db.fetch import quant_monitor, fetch_quant

@dataclass
class IndexConfig:
    """指数配置"""
    code: str
    name: str
    type: int  # 1: 中证指数, 2: 国证指数

@dataclass
class IndexHistoryData:
    """指数历史数据"""
    id: str
    code: str
    date: str
    tradedate: str
    name: str
    open: float
    high: float
    low: float
    close: float
    volume: float
    amount: float
    pct_chg: float
    pe: float = 0.0
    type: int = 1
    deviation: float = 0.0

class CNIIndexProcessor:
    """CNI指数数据处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 中证指数配置
        self.csindex_configs = [
            IndexConfig('000016', '上证50', 1),
            IndexConfig('000300', '沪深300', 1),
            IndexConfig('000905', '中证500', 1),
            IndexConfig('000852', '中证1000', 1),
            IndexConfig('932000', '中证2000', 1),
            IndexConfig('000688', '科创50', 1),
            IndexConfig('899050', '北证50', 1),
            IndexConfig('930931', '港股通50', 1),
            IndexConfig('930050', '中证A50', 1),
            IndexConfig('000922', '中证红利', 1),
            IndexConfig('399986', '中证银行', 1),
            IndexConfig('399998', '中证煤炭', 1),
            IndexConfig('000827', '中证环保', 1),
            IndexConfig('399808', '中证新能', 1),
            IndexConfig('399967', '中证军工', 1),
            IndexConfig('399971', '中证传媒', 1),
            IndexConfig('399997', '中证白酒', 1),
            IndexConfig('399989', '中证医疗', 1),
            IndexConfig('930608', '中证基建', 1),
            IndexConfig('930606', '中证钢铁', 1),
            IndexConfig('930601', '中证软件', 1),
            IndexConfig('000949', '中证农业', 1),
            IndexConfig('930708', '中证有色', 1),
            IndexConfig('930707', '中证畜牧', 1),
            IndexConfig('930641', '中证中药', 1),
            IndexConfig('930633', '中证旅游', 1),
            IndexConfig('930821', '中证VR', 1),
            IndexConfig('930902', '中证数据', 1),
            IndexConfig('H30588', '中证证保', 1),
            IndexConfig('931866', '中证机床', 1),
            IndexConfig('931865', '中证半导', 1),
            IndexConfig('931747', '中证储能', 1),
            IndexConfig('931688', '中证算力', 1),
            IndexConfig('931247', '中证信创', 1),
            IndexConfig('931235', '中证电信', 1),
            IndexConfig('931229', '中证氢能', 1),
            IndexConfig('930706', '中证水泥', 1),
            IndexConfig('930642', '中证核电', 1),
            IndexConfig('930618', '中证保险', 1),
            IndexConfig('930607', '中证汽车', 1),
        ]

        # 国证指数配置
        self.cni_configs = [
            IndexConfig('399372', '大盘成长', 2),
            IndexConfig('399373', '大盘价值', 2),
            IndexConfig('399374', '中盘成长', 2),
            IndexConfig('399375', '中盘价值', 2),
            IndexConfig('399376', '小盘成长', 2),
            IndexConfig('399377', '小盘价值', 2),
        ]

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_date_range(self) -> Tuple[str, str]:
        """
        获取日期范围

        :return: (开始日期, 结束日期)
        """
        try:
            decision_date = fetch_quant()[2]
            start_date = decision_date.replace('-', '')
            end_date = time.strftime('%Y%m%d', time.localtime())

            logger.info(f"日期范围: {start_date} - {end_date}")
            return start_date, end_date

        except Exception as e:
            logger.error(f"获取日期范围失败: {e}")
            # 默认使用最近30天
            end_date = time.strftime('%Y%m%d', time.localtime())
            start_date = time.strftime('%Y%m%d', time.localtime(time.time() - 30*24*3600))
            return start_date, end_date

    def calculate_base_300_return(self, start_date: str, end_date: str) -> float:
        """
        计算沪深300基准收益率

        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 基准收益率
        """
        try:
            logger.info("计算沪深300基准收益率")

            df = ak.stock_zh_index_hist_csindex(symbol='000300', start_date=start_date, end_date=end_date)

            if df.empty:
                logger.warning("沪深300数据为空，使用默认基准收益率0")
                return 0.0

            first_row = df.iloc[0]
            last_row = df.iloc[-1]

            base_return = (last_row['收盘'] - first_row['收盘']) / first_row['收盘']

            logger.info(f"沪深300基准收益率: {base_return:.4f}")
            return base_return

        except Exception as e:
            logger.error(f"计算沪深300基准收益率失败: {e}")
            return 0.0

    def get_csindex_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        获取中证指数数据

        :param symbol: 指数代码
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 指数数据DataFrame
        """
        try:
            logger.debug(f"获取中证指数数据: {symbol}")

            df = ak.stock_zh_index_hist_csindex(symbol=symbol, start_date=start_date, end_date=end_date)

            if df.empty:
                logger.warning(f"中证指数 {symbol} 数据为空")
                return None

            # 数据清理
            df['开盘'].fillna(0, inplace=True)
            df['最高'].fillna(0, inplace=True)
            df['最低'].fillna(0, inplace=True)
            df['收盘'].fillna(0, inplace=True)

            logger.debug(f"中证指数 {symbol} 数据获取成功: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取中证指数 {symbol} 数据失败: {e}")
            return None

    def get_cni_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        获取国证指数数据

        :param symbol: 指数代码
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 指数数据DataFrame
        """
        try:
            logger.debug(f"获取国证指数数据: {symbol}")

            df = ak.index_hist_cni(symbol=symbol, start_date=start_date, end_date=end_date)

            if df.empty:
                logger.warning(f"国证指数 {symbol} 数据为空")
                return None

            # 数据清理
            df['开盘价'].fillna(0, inplace=True)
            df['最高价'].fillna(0, inplace=True)
            df['最低价'].fillna(0, inplace=True)
            df['收盘价'].fillna(0, inplace=True)

            logger.debug(f"国证指数 {symbol} 数据获取成功: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取国证指数 {symbol} 数据失败: {e}")
            return None

    def generate_record_id(self, code: str, date: str) -> str:
        """
        生成记录ID

        :param code: 指数代码
        :param date: 日期
        :return: 记录ID
        """
        try:
            raw_string = f"{code}{date}"
            return hashlib.md5(raw_string.encode("utf-8")).hexdigest().lower().replace('-', '')
        except Exception as e:
            logger.error(f"生成记录ID失败: {e}")
            return f"{code}_{date}".replace('-', '_')

    def parse_csindex_data(self, df: pd.DataFrame, config: IndexConfig,
                          base_return: float) -> List[IndexHistoryData]:
        """
        解析中证指数数据

        :param df: 指数数据DataFrame
        :param config: 指数配置
        :param base_return: 基准收益率
        :return: 指数历史数据列表
        """
        try:
            if df.empty:
                return []

            first_row = df.iloc[0]
            index_data_list = []

            for _, row in df.iterrows():
                try:
                    # 计算相对基准的偏离度
                    current_return = (float(row['收盘']) - first_row['收盘']) / first_row['收盘']
                    deviation = round(current_return - base_return, 4)

                    index_data = IndexHistoryData(
                        id=self.generate_record_id(str(row['指数代码']), str(row['日期'])),
                        code=str(row['指数代码']),
                        date=str(row['日期']),
                        tradedate=str(row['日期']).replace('-', ''),
                        name=str(row['指数中文简称']),
                        open=float(row['开盘']),
                        high=float(row['最高']),
                        low=float(row['最低']),
                        close=float(row['收盘']),
                        volume=float(row['成交量']),
                        amount=float(row['成交金额']),
                        pct_chg=float(row['涨跌幅']),
                        pe=float(row['滚动市盈率']) if pd.notna(row['滚动市盈率']) else 0.0,
                        type=config.type,
                        deviation=deviation
                    )
                    index_data_list.append(index_data)

                except Exception as e:
                    logger.error(f"解析中证指数行数据失败: {e}")
                    continue

            logger.debug(f"中证指数 {config.code} 数据解析完成: {len(index_data_list)} 条记录")
            return index_data_list

        except Exception as e:
            logger.error(f"解析中证指数 {config.code} 数据失败: {e}")
            return []

    def parse_cni_data(self, df: pd.DataFrame, config: IndexConfig,
                      base_return: float) -> List[IndexHistoryData]:
        """
        解析国证指数数据

        :param df: 指数数据DataFrame
        :param config: 指数配置
        :param base_return: 基准收益率
        :return: 指数历史数据列表
        """
        try:
            if df.empty:
                return []

            first_row = df.iloc[0]
            index_data_list = []

            for _, row in df.iterrows():
                try:
                    # 计算相对基准的偏离度
                    current_return = (float(row['收盘价']) - first_row['收盘价']) / first_row['收盘价']
                    deviation = round(current_return - base_return, 4)

                    index_data = IndexHistoryData(
                        id=self.generate_record_id(config.code, str(row['日期'])),
                        code=config.code,
                        date=str(row['日期']),
                        tradedate=str(row['日期']).replace('-', ''),
                        name=config.name,
                        open=float(row['开盘价']),
                        high=float(row['最高价']),
                        low=float(row['最低价']),
                        close=float(row['收盘价']),
                        volume=float(row['成交量']),
                        amount=float(row['成交额']),
                        pct_chg=float(row['涨跌幅']),
                        pe=0.0,  # 国证指数没有PE数据
                        type=config.type,
                        deviation=deviation
                    )
                    index_data_list.append(index_data)

                except Exception as e:
                    logger.error(f"解析国证指数行数据失败: {e}")
                    continue

            logger.debug(f"国证指数 {config.code} 数据解析完成: {len(index_data_list)} 条记录")
            return index_data_list

        except Exception as e:
            logger.error(f"解析国证指数 {config.code} 数据失败: {e}")
            return []

    def save_index_data(self, index_data_list: List[IndexHistoryData]) -> bool:
        """
        保存指数数据到数据库

        :param index_data_list: 指数数据列表
        :return: 是否成功
        """
        try:
            if not index_data_list:
                logger.warning("没有指数数据需要保存")
                return True

            with db_manager.get_session() as session:
                success_count = 0

                for index_data in index_data_list:
                    try:
                        new_index = Csindex(
                            id=index_data.id,
                            code=index_data.code,
                            date=index_data.date,
                            tradedate=index_data.tradedate,
                            name=index_data.name,
                            open=index_data.open,
                            high=index_data.high,
                            low=index_data.low,
                            close=index_data.close,
                            volume=index_data.volume,
                            amount=index_data.amount,
                            pctChg=index_data.pct_chg,
                            pe=index_data.pe,
                            type=index_data.type,
                            deviation=index_data.deviation
                        )
                        session.merge(new_index)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"保存指数数据 {index_data.code}-{index_data.date} 失败: {e}")
                        continue

                session.commit()

            logger.info(f"指数数据保存完成: 成功 {success_count}/{len(index_data_list)} 条记录")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存指数数据失败: {e}")
            return False

    @monitor_performance
    def process_csindex_data(self, start_date: str, end_date: str, base_return: float) -> bool:
        """
        处理中证指数数据

        :param start_date: 开始日期
        :param end_date: 结束日期
        :param base_return: 基准收益率
        :return: 是否成功
        """
        try:
            logger.info("开始处理中证指数数据")

            all_data = []
            success_count = 0

            for config in self.csindex_configs:
                try:
                    # 获取指数数据
                    df = self.get_csindex_data(config.code, start_date, end_date)
                    if df is None:
                        continue

                    # 解析数据
                    index_data_list = self.parse_csindex_data(df, config, base_return)
                    if index_data_list:
                        all_data.extend(index_data_list)
                        success_count += 1
                        logger.info(f"✅ 中证指数 {config.name} ({config.code}): {len(index_data_list)} 条记录")
                    else:
                        logger.warning(f"❌ 中证指数 {config.name} ({config.code}): 无数据")

                except Exception as e:
                    logger.error(f"处理中证指数 {config.name} ({config.code}) 失败: {e}")
                    continue

            # 保存所有数据
            if all_data:
                save_success = self.save_index_data(all_data)
                logger.info(f"中证指数数据处理完成: 成功 {success_count}/{len(self.csindex_configs)} 个指数，"
                           f"总计 {len(all_data)} 条记录")
                return save_success
            else:
                logger.warning("没有中证指数数据需要保存")
                return False

        except Exception as e:
            logger.error(f"处理中证指数数据失败: {e}")
            return False

    @monitor_performance
    def process_cni_data(self, start_date: str, end_date: str, base_return: float) -> bool:
        """
        处理国证指数数据

        :param start_date: 开始日期
        :param end_date: 结束日期
        :param base_return: 基准收益率
        :return: 是否成功
        """
        try:
            logger.info("开始处理国证指数数据")

            all_data = []
            success_count = 0

            for config in self.cni_configs:
                try:
                    # 获取指数数据
                    df = self.get_cni_data(config.code, start_date, end_date)
                    if df is None:
                        continue

                    # 解析数据
                    index_data_list = self.parse_cni_data(df, config, base_return)
                    if index_data_list:
                        all_data.extend(index_data_list)
                        success_count += 1
                        logger.info(f"✅ 国证指数 {config.name} ({config.code}): {len(index_data_list)} 条记录")
                    else:
                        logger.warning(f"❌ 国证指数 {config.name} ({config.code}): 无数据")

                except Exception as e:
                    logger.error(f"处理国证指数 {config.name} ({config.code}) 失败: {e}")
                    continue

            # 保存所有数据
            if all_data:
                save_success = self.save_index_data(all_data)
                logger.info(f"国证指数数据处理完成: 成功 {success_count}/{len(self.cni_configs)} 个指数，"
                           f"总计 {len(all_data)} 条记录")
                return save_success
            else:
                logger.warning("没有国证指数数据需要保存")
                return False

        except Exception as e:
            logger.error(f"处理国证指数数据失败: {e}")
            return False

    @monitor_performance
    def process_all_index_data(self) -> bool:
        """
        处理所有指数数据

        :return: 是否成功
        """
        try:
            logger.info("开始处理CNI指数历史数据")

            # 获取日期范围
            start_date, end_date = self.get_date_range()

            # 计算沪深300基准收益率
            base_return = self.calculate_base_300_return(start_date, end_date)

            # 处理中证指数数据
            csindex_success = self.process_csindex_data(start_date, end_date, base_return)

            # 处理国证指数数据
            cni_success = self.process_cni_data(start_date, end_date, base_return)

            success = csindex_success or cni_success

            if success:
                logger.info("✅ CNI指数历史数据处理完成")
            else:
                logger.error("❌ CNI指数历史数据处理失败")

            return success

        except Exception as e:
            logger.error(f"处理所有指数数据失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def query_index_history_cni() -> bool:
    """CNI指数历史数据查询 - 优化版本"""
    with CNIIndexProcessor() as processor:
        return processor.process_all_index_data()

@monitor_performance
def main():
    """
    主函数 - 执行CNI指数历史数据查询系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始CNI指数历史数据查询系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="CNI指数历史数据查询系统 (优化版本)")
        args = arg_parser.parse_args()

        logger.info("执行参数: 无特殊参数")

        # 执行指数数据查询
        success = query_index_history_cni()

        # 更新监控状态
        if success:
            quant_monitor(36, True)
            logger.info("✅ CNI指数历史数据查询系统执行完成")
        else:
            quant_monitor(36, False)
            logger.error("❌ CNI指数历史数据查询系统执行失败")

        return success

    except Exception as e:
        logger.error(f"CNI指数历史数据查询系统执行失败: {e}")
        quant_monitor(36, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)