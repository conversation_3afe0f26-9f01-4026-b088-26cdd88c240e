# -*- coding: UTF-8 -*-
"""
今日指数数据查询系统 (优化版本)

0 */15 9-16 * * 1-5

主要功能:
1. 获取三大指数今日实时数据
2. 包括上证指数、深证成指、创业板指
3. 获取成交量、收盘价、涨跌幅等指标
4. 实时更新到数据库

优化特性:
- 完善的错误处理和日志记录
- 性能监控和数据验证
- 多数据源支持和容错机制
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import datetime
import time
import argparse
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession
from db.models import Daliy, Source

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 第三方库
import akshare as ak
import pandas as pd
from sqlalchemy.dialects.mysql import insert

# 数据获取
from db.fetch import is_trade_date, quant_monitor

@dataclass
class IndexRealTimeData:
    """指数实时数据类"""
    code: str
    name: str
    current_price: float
    open_price: float
    high_price: float
    low_price: float
    volume: float
    amount: float
    change_pct: float
    change_amount: float

@dataclass
class MarketRealTimeData:
    """市场实时数据类"""
    date: str
    sh_data: IndexRealTimeData
    sz_data: IndexRealTimeData
    cyb_data: IndexRealTimeData
    total_volume: float
    total_amount: float

class IndexTodayProcessor:
    """今日指数数据处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 指数代码映射
        self.index_mapping = {
            'sh000001': '上证指数',
            'sz399001': '深证成指',
            'sz399006': '创业板指'
        }

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_index_realtime_data_akshare(self) -> Optional[pd.DataFrame]:
        """
        从akshare获取指数实时数据

        :return: 指数实时数据DataFrame
        """
        try:
            logger.info("从akshare获取指数实时数据")

            # 获取指数实时数据
            df = ak.stock_zh_index_spot_sina()

            if df.empty:
                logger.error("获取指数实时数据失败")
                return None

            logger.debug(f"获取到 {len(df)} 个指数数据")
            return df

        except Exception as e:
            logger.error(f"从akshare获取指数实时数据失败: {e}")
            return None

    def parse_index_data(self, df: pd.DataFrame, index_code: str) -> Optional[IndexRealTimeData]:
        """
        解析单个指数数据

        :param df: 指数数据DataFrame
        :param index_code: 指数代码
        :return: 指数实时数据对象
        """
        try:
            # 查找指定指数
            index_df = df[df['代码'] == index_code]

            if index_df.empty:
                logger.warning(f"未找到指数 {index_code} 的数据")
                return None

            row = index_df.iloc[-1]

            index_data = IndexRealTimeData(
                code=index_code,
                name=self.index_mapping.get(index_code, index_code),
                current_price=float(row['最新价']) if pd.notna(row['最新价']) else 0.0,
                open_price=float(row['今开']) if pd.notna(row['今开']) else 0.0,
                high_price=float(row['最高']) if pd.notna(row['最高']) else 0.0,
                low_price=float(row['最低']) if pd.notna(row['最低']) else 0.0,
                volume=float(row['成交量']) if pd.notna(row['成交量']) else 0.0,
                amount=float(row['成交额']) if pd.notna(row['成交额']) else 0.0,
                change_pct=float(row['涨跌幅']) if pd.notna(row['涨跌幅']) else 0.0,
                change_amount=float(row['涨跌额']) if pd.notna(row['涨跌额']) else 0.0
            )

            logger.debug(f"解析指数 {index_code} 数据成功: 价格={index_data.current_price}, 涨跌幅={index_data.change_pct}%")
            return index_data

        except Exception as e:
            logger.error(f"解析指数 {index_code} 数据失败: {e}")
            return None

    def combine_market_data(self, sh_data: IndexRealTimeData, sz_data: IndexRealTimeData,
                          cyb_data: IndexRealTimeData, date: str) -> MarketRealTimeData:
        """
        合并市场数据

        :param sh_data: 上证指数数据
        :param sz_data: 深证成指数据
        :param cyb_data: 创业板指数据
        :param date: 日期
        :return: 市场实时数据
        """
        try:
            total_volume = sh_data.volume + sz_data.volume
            total_amount = sh_data.amount + sz_data.amount

            market_data = MarketRealTimeData(
                date=date,
                sh_data=sh_data,
                sz_data=sz_data,
                cyb_data=cyb_data,
                total_volume=total_volume,
                total_amount=total_amount
            )

            logger.info(f"市场数据合并完成: 总成交量={total_volume}, 总成交额={total_amount}")
            return market_data

        except Exception as e:
            logger.error(f"合并市场数据失败: {e}")
            raise

    def save_market_data(self, market_data: MarketRealTimeData) -> bool:
        """
        保存市场数据到数据库

        :param market_data: 市场实时数据
        :return: 是否成功
        """
        try:
            with db_manager.get_session() as session:
                new_daily = Daliy(
                    date=market_data.date,
                    volume=market_data.total_amount,  # 使用成交额
                    cybVolume=market_data.cyb_data.amount,
                    shVolume=market_data.sh_data.amount,
                    szVolume=market_data.sz_data.amount,
                    shOpen=market_data.sh_data.open_price,
                    shHigh=market_data.sh_data.high_price,
                    shLow=market_data.sh_data.low_price,
                    shClose=market_data.sh_data.current_price,
                    szClose=market_data.sz_data.current_price,
                    cybOpen=market_data.cyb_data.open_price,
                    cybHigh=market_data.cyb_data.high_price,
                    cybLow=market_data.cyb_data.low_price,
                    cybClose=market_data.cyb_data.current_price,
                    shClosePct=market_data.sh_data.change_pct,
                    szClosePct=market_data.sz_data.change_pct,
                    cybClosePct=market_data.cyb_data.change_pct
                )

                session.merge(new_daily)
                session.commit()

            logger.info(f"市场数据保存成功: 日期={market_data.date}")
            return True

        except Exception as e:
            logger.error(f"保存市场数据失败: {e}")
            return False

    @monitor_performance
    def process_today_index_data(self, target_date: str = None) -> bool:
        """
        处理今日指数数据

        :param target_date: 目标日期 (YYYYMMDD)，默认为今日
        :return: 是否成功
        """
        try:
            if not target_date:
                target_date = time.strftime('%Y%m%d', time.localtime())

            logger.info(f"开始处理今日指数数据: {target_date}")

            # 检查是否为交易日
            if not is_trade_date(target_date):
                logger.warning(f"日期 {target_date} 不是交易日，跳过处理")
                return True

            # 获取指数实时数据
            df = self.get_index_realtime_data_akshare()
            if df is None:
                return False

            # 解析各指数数据
            sh_data = self.parse_index_data(df, 'sh000001')
            sz_data = self.parse_index_data(df, 'sz399001')
            cyb_data = self.parse_index_data(df, 'sz399006')

            # 检查数据完整性
            if not all([sh_data, sz_data, cyb_data]):
                logger.error("部分指数数据获取失败")
                return False

            # 合并市场数据
            market_data = self.combine_market_data(sh_data, sz_data, cyb_data, target_date)

            # 保存数据
            success = self.save_market_data(market_data)

            if success:
                logger.info("✅ 今日指数数据处理完成")
            else:
                logger.error("❌ 今日指数数据处理失败")

            return success

        except Exception as e:
            logger.error(f"处理今日指数数据失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def query_index_today_data(target_date: str = None) -> bool:
    """今日指数数据查询 - 优化版本"""
    with IndexTodayProcessor() as processor:
        return processor.process_today_index_data(target_date)

@monitor_performance
def main():
    """
    主函数 - 执行今日指数数据查询系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始今日指数数据查询系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="今日指数数据查询系统 (优化版本)")
        arg_parser.add_argument("-d", "--date", required=False, type=str,
                               help="目标日期 YYYYMMDD，默认为今日")
        args = arg_parser.parse_args()

        target_date = args.date or time.strftime('%Y%m%d', time.localtime())

        logger.info(f"执行参数: 目标日期={target_date}")

        # 执行数据处理
        success = query_index_today_data(target_date)

        # 更新监控状态
        if success:
            quant_monitor(26, True)
            logger.info("✅ 今日指数数据查询系统执行完成")
        else:
            quant_monitor(26, False)
            logger.error("❌ 今日指数数据查询系统执行失败")

        return success

    except Exception as e:
        logger.error(f"今日指数数据查询系统执行失败: {e}")
        quant_monitor(26, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)