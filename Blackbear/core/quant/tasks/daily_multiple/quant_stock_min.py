# -*- coding: UTF-8 -*-
"""
分钟级股票数据分析系统 (优化版本)

0 0 16 * * 1-5
0 30 12 * * 1-5

主要功能:
1. 分钟级股票数据异动检测
2. 成交量异常识别
3. 价格波动分析
4. 资金流向监控
5. 实时异动预警

优化特性:
- 完善的错误处理和日志记录
- 性能监控和并发处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import argparse
import time
from datetime import date, datetime
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, Quant, Daliy, Task, StockMin, GroupStock, Source

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 数据获取
from db.fetch import fetch_all_stock, quant_monitor, fetch_quant_raw, is_trade_date
from tqdm import tqdm

@dataclass
class VolumeSpike:
    """成交量异动数据"""
    id: str
    code: str
    time: str
    price: float
    volume: float
    pct_change: float
    volume_change: float
    spike_ratio: float

@dataclass
class StockMinAnalysis:
    """股票分钟级分析结果"""
    code: str
    total_records: int
    spike_count: int
    max_spike_ratio: float
    avg_volume: float
    max_pct_change: float

class StockMinProcessor:
    """分钟级股票数据处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 获取量化参数
        self.quant_raw = fetch_quant_raw()

        # 分析参数
        self.volume_ma_window = 10  # 成交量移动平均窗口
        self.volume_spike_threshold = self.quant_raw.stockMinVolume if self.quant_raw else 3.0
        self.pct_change_threshold = self.quant_raw.stockMinPct if self.quant_raw else 0.02

        # 日期设置
        self.today = date.today().strftime('%Y%m%d')
        self.today_formatted = date.today().strftime('%Y-%m-%d')

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_eligible_stocks(self) -> List[str]:
        """
        获取符合条件的股票列表

        :return: 股票代码列表
        """
        try:
            logger.info("获取符合条件的股票列表")

            with db_manager.get_session() as session:
                # 根据市值筛选股票
                average_value = self.quant_raw.averageValue if self.quant_raw else 0

                stocks = session.query(Source).filter(
                    Source.f20 >= average_value
                ).yield_per(1000)

                stock_codes = [stock.code for stock in stocks]

            logger.info(f"找到 {len(stock_codes)} 只符合条件的股票")
            return stock_codes

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_stock_min_data(self, code: str, target_date: str = None) -> Optional[pd.DataFrame]:
        """
        获取股票分钟级数据

        :param code: 股票代码
        :param target_date: 目标日期 (YYYY-MM-DD)
        :return: 分钟级数据DataFrame
        """
        try:
            if not target_date:
                target_date = self.today_formatted

            sql = f'''
                SELECT * FROM stockmin
                WHERE code = '{code}'
                AND tradedate = '{target_date}'
                ORDER BY date ASC
            '''

            df = pd.read_sql(sql, con=engine)

            if df.empty:
                logger.debug(f"股票 {code} 在 {target_date} 没有分钟级数据")
                return None

            logger.debug(f"获取股票 {code} 分钟级数据: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取股票 {code} 分钟级数据失败: {e}")
            return None

    def check_data_availability(self, target_date: str) -> bool:
        """
        检查指定日期是否有分钟级数据

        :param target_date: 目标日期 (YYYY-MM-DD)
        :return: 是否有数据
        """
        try:
            sql = f'''
                SELECT COUNT(*) as count
                FROM stockmin
                WHERE tradedate = '{target_date}'
                LIMIT 1
            '''

            df = pd.read_sql(sql, con=engine)
            count = df.iloc[0]['count'] if not df.empty else 0

            logger.info(f"日期 {target_date} 的分钟级数据条数: {count}")
            return count > 0

        except Exception as e:
            logger.error(f"检查数据可用性失败: {e}")
            return False

    def get_latest_data_date(self) -> Optional[str]:
        """
        获取最新的数据日期

        :return: 最新数据日期 (YYYY-MM-DD)
        """
        try:
            sql = '''
                SELECT tradedate
                FROM stockmin
                ORDER BY tradedate DESC
                LIMIT 1
            '''

            df = pd.read_sql(sql, con=engine)
            if not df.empty:
                latest_date = df.iloc[0]['tradedate']
                logger.info(f"数据库中最新的分钟级数据日期: {latest_date}")
                return latest_date
            else:
                logger.warning("数据库中没有分钟级数据")
                return None

        except Exception as e:
            logger.error(f"获取最新数据日期失败: {e}")
            return None

    def analyze_volume_spikes(self, df: pd.DataFrame, code: str) -> List[VolumeSpike]:
        """
        分析成交量异动

        :param df: 分钟级数据
        :param code: 股票代码
        :return: 成交量异动列表
        """
        try:
            if df.empty:
                return []

            # 数据预处理
            df = df.copy()
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            df['close'] = pd.to_numeric(df['close'], errors='coerce')
            df = df.dropna(subset=['volume', 'close'])

            if len(df) < self.volume_ma_window:
                logger.debug(f"股票 {code} 数据不足，跳过分析")
                return []

            # 计算技术指标
            df['volume_ma'] = df['volume'].rolling(window=self.volume_ma_window).mean()
            df['pct_change'] = df['close'].pct_change()
            df['volume_change'] = df['volume'].pct_change()

            # 识别成交量异动
            # 条件：价格变化超过阈值 且 成交量超过移动平均的倍数
            df['volume_spike'] = (
                (abs(df['pct_change']) > self.pct_change_threshold) &
                (df['volume'] > df['volume_ma'] * self.volume_spike_threshold)
            )

            # 计算异动比率
            df['spike_ratio'] = df['volume'] / df['volume_ma']

            # 提取异动记录
            spike_data = df[df['volume_spike']].copy()
            volume_spikes = []

            for _, row in spike_data.iterrows():
                spike = VolumeSpike(
                    id=str(row['id']),
                    code=code,
                    time=str(row['date']),
                    price=float(row['close']),
                    volume=float(row['volume']),
                    pct_change=float(row['pct_change']) if pd.notna(row['pct_change']) else 0.0,
                    volume_change=float(row['volume_change']) if pd.notna(row['volume_change']) else 0.0,
                    spike_ratio=float(row['spike_ratio']) if pd.notna(row['spike_ratio']) else 0.0
                )
                volume_spikes.append(spike)

            if volume_spikes:
                logger.debug(f"股票 {code} 发现 {len(volume_spikes)} 个成交量异动")

            return volume_spikes

        except Exception as e:
            logger.error(f"分析股票 {code} 成交量异动失败: {e}")
            return []

    def save_volume_spikes(self, volume_spikes: List[VolumeSpike]) -> bool:
        """
        保存成交量异动到数据库

        :param volume_spikes: 成交量异动列表
        :return: 是否成功
        """
        try:
            if not volume_spikes:
                return True

            with db_manager.get_session() as session:
                success_count = 0

                for spike in volume_spikes:
                    try:
                        new_min = StockMin(
                            id=spike.id,
                            spike=True
                        )
                        session.merge(new_min)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"保存异动记录 {spike.id} 失败: {e}")
                        continue

                session.commit()

            logger.debug(f"成交量异动保存完成: 成功 {success_count}/{len(volume_spikes)} 条记录")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存成交量异动失败: {e}")
            return False

    @monitor_performance
    def analyze_single_stock(self, code: str, target_date: str = None) -> Optional[StockMinAnalysis]:
        """
        分析单只股票的分钟级数据

        :param code: 股票代码
        :param target_date: 目标日期
        :return: 分析结果
        """
        try:
            logger.debug(f"开始分析股票 {code}")

            # 获取分钟级数据
            df = self.get_stock_min_data(code, target_date)
            if df is None:
                return None

            # 分析成交量异动
            volume_spikes = self.analyze_volume_spikes(df, code)

            # 保存异动记录
            if volume_spikes:
                self.save_volume_spikes(volume_spikes)

                # 记录异动信息
                for spike in volume_spikes:
                    logger.info(f"📈 股票 {code} 成交量异动: 时间={spike.time}, "
                               f"涨跌幅={spike.pct_change:.2%}, 异动比率={spike.spike_ratio:.2f}")

            # 生成分析结果
            analysis = StockMinAnalysis(
                code=code,
                total_records=len(df),
                spike_count=len(volume_spikes),
                max_spike_ratio=max([s.spike_ratio for s in volume_spikes], default=0.0),
                avg_volume=df['volume'].mean() if not df.empty else 0.0,
                max_pct_change=abs(df['close'].pct_change()).max() if not df.empty else 0.0
            )

            return analysis

        except Exception as e:
            logger.error(f"分析股票 {code} 失败: {e}")
            return None

    @monitor_performance
    def process_all_stocks(self, target_date: str = None, max_workers: int = 5) -> bool:
        """
        处理所有股票的分钟级数据分析

        :param target_date: 目标日期
        :param max_workers: 最大工作线程数
        :return: 是否成功
        """
        try:
            if not target_date:
                target_date = self.today

            logger.info(f"开始处理分钟级股票数据分析: {target_date}")

            # 检查是否为交易日
            if not is_trade_date(target_date):
                logger.warning(f"日期 {target_date} 不是交易日，跳过处理")
                return True

            # 转换日期格式用于数据库查询
            target_date_formatted = datetime.strptime(target_date, '%Y%m%d').strftime('%Y-%m-%d')

            # 检查数据可用性
            if not self.check_data_availability(target_date_formatted):
                logger.warning(f"日期 {target_date_formatted} 没有分钟级数据")

                # 获取最新数据日期
                latest_date = self.get_latest_data_date()
                if latest_date:
                    logger.info(f"自动切换到最新数据日期: {latest_date}")
                    target_date_formatted = latest_date
                else:
                    logger.error("数据库中没有任何分钟级数据")
                    return False

            # 获取股票列表
            stock_codes = self.get_eligible_stocks()
            if not stock_codes:
                logger.error("没有找到符合条件的股票")
                return False

            logger.info(f"准备分析 {len(stock_codes)} 只股票")

            # 使用线程池并发处理
            success_count = 0
            total_spikes = 0

            executor = ThreadPoolExecutor(max_workers=max_workers)
            future_to_code = {
                executor.submit(self.analyze_single_stock, code, target_date_formatted): code
                for code in stock_codes
            }
            try:
                for future in tqdm(as_completed(future_to_code), total=len(stock_codes), desc="分析股票"):
                    code = future_to_code[future]
                    try:
                        analysis = future.result()
                        if analysis:
                            success_count += 1
                            total_spikes += analysis.spike_count
                    except Exception as e:
                        logger.error(f"处理股票 {code} 异常: {e}")
            finally:
                for future in future_to_code:
                    if not future.done():
                        future.cancel()
                executor.shutdown(wait=False)

            logger.info(f"分钟级数据分析完成: 成功分析 {success_count}/{len(stock_codes)} 只股票，"
                       f"发现 {total_spikes} 个成交量异动")
            return success_count > 0

        except Exception as e:
            logger.error(f"处理所有股票失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def quant_stock_min(target_date: str = None) -> bool:
    """分钟级股票数据分析 - 优化版本"""
    with StockMinProcessor() as processor:
        return processor.process_all_stocks(target_date)

@monitor_performance
def main():
    """
    主函数 - 执行分钟级股票数据分析系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始分钟级股票数据分析系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="分钟级股票数据分析系统 (优化版本)")
        arg_parser.add_argument("-d", "--date", required=False, type=str,
                               help="目标日期 YYYYMMDD，默认为今日")
        arg_parser.add_argument("-w", "--workers", required=False, type=int,
                               default=5, help="工作线程数")
        args = arg_parser.parse_args()

        target_date = args.date or date.today().strftime('%Y%m%d')
        max_workers = args.workers

        logger.info(f"执行参数: 目标日期={target_date}, 工作线程={max_workers}")

        # 执行分钟级数据分析
        success = quant_stock_min(target_date)

        # 更新监控状态
        if success:
            quant_monitor(37, True)
            logger.info("✅ 分钟级股票数据分析系统执行完成")
        else:
            quant_monitor(37, False)
            logger.error("❌ 分钟级股票数据分析系统执行失败")

        return success

    except Exception as e:
        logger.error(f"分钟级股票数据分析系统执行失败: {e}")
        quant_monitor(37, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)
