# -*- coding: UTF-8 -*-
"""
每日量化+多因子一体化批量处理系统（合并版）

主要功能:
1. 每日市场统计指标批量计算（涨跌停、新高新低、风格、总市值等）
2. 多因子（SMB/HML/AR）批量计算
3. 统一写入 Daliy 表，字段兼容原有
4. 支持单日/区间/批量/多线程
5. 日志、监控、参数兼容原有

用法示例：
python quant_daliy_multi_factor.py -d 20240701           # 只处理指定日期
python quant_daliy_multi_factor.py -d 20240701 -p       # 处理从默认起始到指定日期的所有数据
python quant_daliy_multi_factor.py -w 8                 # 指定线程数
"""

import sys
import os
from pathlib import Path

# 设置项目根目录到Python路径，确保可导入本地模块
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import time
import argparse
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

# 数据库相关依赖
from db.database import db_manager, DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode, Csindex
from sqlalchemy import and_

# 工具类与配置
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.helper import isLimit, isDownLimit, ago_day_timestr, check_bool
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 数据获取与监控
from db.fetch import fetch_all_stock, quant_monitor, fetch_all_history

# 第三方数据接口
import akshare as ak

@dataclass
class DailyMetrics:
    """
    每日市场指标+多因子数据类
    用于统一存储单日所有统计与因子结果，便于写入数据库
    """
    date: str
    limitdown: int = 0
    limitup: int = 0
    newhigh: int = 0
    newlow: int = 0
    averagechange: float = 0.0
    averageprice: float = 0.0
    changeratio: float = 0.0
    crowding: float = 0.0
    medianincrease: float = 0.0
    brokenpb: int = 0
    rose3: int = 0
    fall3: int = 0
    UES: int = 0
    style: str = ''
    totalvalue: float = 0.0
    SMB: float = 0.0
    HML: float = 0.0
    AR: float = 0.0

class DailyMultiFactorAnalyzer:
    """
    每日市场统计+多因子一体化分析器
    封装所有批量处理、单日处理、指标与因子计算、数据库写入等核心逻辑
    """
    def __init__(self):
        self.settings = get_settings()
        self.akshare = Akshare()
        self.bs = Baostock()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 资源清理，退出baostock等
        try:
            if hasattr(self.bs, 'logout'):
                self.bs.logout()
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")

    @staticmethod
    def cal_is_limit(row) -> str:
        """
        判断单只股票是否涨停
        :param row: 股票数据行
        :return: '1' 涨停, '0' 非涨停
        """
        try:
            return '1' if isLimit(row) else '0'
        except Exception as e:
            logger.error(f"判断涨停失败: {e}")
            return '0'

    @staticmethod
    def cal_is_down_limit(row) -> str:
        """
        判断单只股票是否跌停
        :param row: 股票数据行
        :return: '1' 跌停, '0' 非跌停
        """
        try:
            return '1' if isDownLimit(row) else '0'
        except Exception as e:
            logger.error(f"判断跌停失败: {e}")
            return '0'

    def cal_market_style(self, date: str) -> str:
        """
        计算市场主导风格（如成长、价值、周期等）
        :param date: 交易日期
        :return: 主导风格名称
        """
        try:
            with db_manager.get_session() as session:
                csindex = session.query(Csindex).filter(
                    and_(Csindex.tradedate == date, Csindex.type == 2)
                ).all()
                if not csindex:
                    logger.warning(f"日期 {date} 没有找到风格指数数据")
                    return ''
                max_style = ''
                max_value = -20
                for index in csindex:
                    if hasattr(index, 'pctChg') and index.pctChg > max_value:
                        max_style = index.name
                        max_value = index.pctChg
                return max_style
        except Exception as e:
            logger.error(f"计算市场风格失败: {e}")
            return ''

    def calculate_daily_metrics(self, df: pd.DataFrame, date: str) -> DailyMetrics:
        """
        计算单日市场统计指标
        :param df: 单日全市场股票数据
        :param date: 交易日期
        :return: DailyMetrics 对象
        """
        try:
            if df.empty:
                logger.warning(f"日期 {date} 没有股票数据")
                return DailyMetrics(date=date)
            df = df.copy()
            df['turn'] = df['turn'].replace(0, np.inf)
            df['mkt'] = df.apply(lambda x: x['volume'] / x['turn'] if x['turn'] != np.inf else 0, axis=1)
            df['uplimit'] = df.apply(lambda x: self.cal_is_limit(x), axis=1)
            df['downlimit'] = df.apply(lambda x: self.cal_is_down_limit(x), axis=1)
            df_up = df[df['pctChg'] > 0]
            df_down = df[df['pctChg'] < 0]
            df_max = df[df['max'] == '1'] if 'max' in df.columns else pd.DataFrame()
            df_min = df[df['min'] == '1'] if 'min' in df.columns else pd.DataFrame()
            df_uplimit = df[df['uplimit'] == '1']
            df_downlimit = df[df['downlimit'] == '1']
            df_rose3 = df[df['rose3'] == '1'] if 'rose3' in df.columns else pd.DataFrame()
            df_fall3 = df[df['fall3'] == '1'] if 'fall3' in df.columns else pd.DataFrame()
            crowding = self.calculate_crowding(df)
            median_increase = self.calculate_median_increase(df)
            broken_pb = self.calculate_broken_pb(df)
            change_ratio = self.calculate_change_ratio(df_up, df_down)
            average_change = df['pctChg'].mean() if not df['pctChg'].empty else 0.0
            average_price = df['close'].mean() if not df['close'].empty else 0.0
            style = self.cal_market_style(date)
            return DailyMetrics(
                date=date,
                limitdown=len(df_downlimit),
                limitup=len(df_uplimit),
                newhigh=len(df_max),
                newlow=len(df_min),
                averagechange=round(average_change, 4),
                averageprice=round(average_price, 2),
                changeratio=round(change_ratio, 4),
                crowding=crowding,
                medianincrease=median_increase,
                brokenpb=broken_pb,
                rose3=len(df_rose3),
                fall3=len(df_fall3),
                UES=len(df_max) + len(df_min),
                style=style
            )
        except Exception as e:
            logger.error(f"计算日期 {date} 指标失败: {e}")
            return DailyMetrics(date=date)

    def calculate_crowding(self, df: pd.DataFrame) -> float:
        """
        计算交易拥挤度（前5%成交额占比）
        :param df: 股票数据
        :return: 拥挤度百分比
        """
        try:
            if df.empty or 'amount' not in df.columns:
                return 0.0
            df_amount_sort = df.sort_values(by=['amount'], ascending=[False])
            top_5_percent_count = max(1, round(len(df_amount_sort) * 0.05))
            df_5percent = df_amount_sort.iloc[:top_5_percent_count]
            total_amount = df_amount_sort['amount'].sum()
            if total_amount == 0:
                return 0.0
            crowding = round(df_5percent['amount'].sum() / total_amount, 4) * 100
            return crowding
        except Exception as e:
            logger.error(f"计算交易拥挤度失败: {e}")
            return 0.0

    def calculate_median_increase(self, df: pd.DataFrame) -> float:
        """
        计算中位数涨幅
        :param df: 股票数据
        :return: 中位涨幅
        """
        try:
            if df.empty or 'pctChg' not in df.columns:
                return 0.0
            pct_changes = df['pctChg'].dropna().tolist()
            if not pct_changes:
                return 0.0
            median_increase = np.median(pct_changes)
            return round(median_increase, 4)
        except Exception as e:
            logger.error(f"计算中位数涨幅失败: {e}")
            return 0.0

    def calculate_broken_pb(self, df: pd.DataFrame) -> int:
        """
        计算破净股数量
        :param df: 股票数据
        :return: 破净股数量
        """
        try:
            if df.empty or 'pbMRQ' not in df.columns:
                return 0
            df_broken_pb = df[(df['pbMRQ'] > 0) & (df['pbMRQ'] < 1)]
            return len(df_broken_pb)
        except Exception as e:
            logger.error(f"计算破净股失败: {e}")
            return 0

    def calculate_change_ratio(self, df_up: pd.DataFrame, df_down: pd.DataFrame) -> float:
        """
        计算涨跌比
        :param df_up: 上涨股票数据
        :param df_down: 下跌股票数据
        :return: 涨跌比
        """
        try:
            up_count = len(df_up)
            down_count = len(df_down)
            if down_count == 0:
                return float('inf') if up_count > 0 else 0.0
            return up_count / down_count
        except Exception as e:
            logger.error(f"计算涨跌比失败: {e}")
            return 0.0

    def get_market_total_value(self, date: str) -> Optional[float]:
        """
        获取深沪两市总市值（单位：亿元）
        :param date: 交易日期
        :return: 总市值
        """
        try:
            szse_total = self.get_szse_market_value(date)
            if szse_total is None:
                logger.warning(f"日期 {date} 深圳市值获取失败")
                return None
            sse_total = self.get_sse_market_value(date)
            if sse_total is None:
                logger.warning(f"日期 {date} 上海市值获取失败")
                return None
            total_value = round(szse_total + sse_total, 2)
            return total_value
        except Exception as e:
            logger.error(f"获取日期 {date} 总市值失败: {e}")
            return None

    def get_szse_market_value(self, date: str) -> Optional[float]:
        """
        获取深圳主板A股总市值（单位：亿元）
        :param date: 交易日期
        :return: 深圳主板A股总市值
        """
        try:
            stock_szse_summary_df = ak.stock_szse_summary(date=date)
            if stock_szse_summary_df.empty:
                return None
            main_board_df = stock_szse_summary_df[
                stock_szse_summary_df['证券类别'] == '主板A股'
            ].reset_index()
            if main_board_df.empty:
                return None
            szse_value = main_board_df.loc[0]['总市值'] / 100000000
            return szse_value
        except Exception as e:
            logger.error(f"获取深圳市场总市值失败: {e}")
            return None

    def get_sse_market_value(self, date: str) -> Optional[float]:
        """
        获取上海主板A股总市值（单位：亿元）
        :param date: 交易日期
        :return: 上海主板A股总市值
        """
        try:
            stock_sse_deal_daily_df = ak.stock_sse_deal_daily(date=date)
            if stock_sse_deal_daily_df.empty:
                return None
            market_value_df = stock_sse_deal_daily_df[
                stock_sse_deal_daily_df['单日情况'] == '市价总值'
            ].reset_index()
            if market_value_df.empty:
                return None
            sse_value = market_value_df.loc[0]['主板A']
            return sse_value
        except Exception as e:
            logger.error(f"获取上海市场总市值失败: {e}")
            return None

    @staticmethod
    def classify_by_quantile(value: float, df: pd.DataFrame, column: str) -> str:
        """
        分位数分组（高/中/低）
        :param value: 待分组值
        :param df: 数据表
        :param column: 分组字段
        :return: 'H'/'M'/'L'
        """
        try:
            if value >= df[column].quantile(0.7):
                return 'H'
            elif value < df[column].quantile(0.3):
                return 'L'
            else:
                return 'M'
        except Exception:
            return 'M'

    def calculate_ar_indicator(self, row: pd.Series) -> float:
        """
        计算单只股票AR人气指标
        :param row: 股票数据行
        :return: AR值
        """
        try:
            high = float(row['high'])
            open_price = float(row['open'])
            low = float(row['low'])
            if high == open_price or open_price == low:
                return 100.0
            ar = (high - open_price) / (open_price - low) * 100
            return ar
        except Exception as e:
            logger.debug(f"计算AR指标失败: {e}")
            return 100.0

    def calculate_average_ar(self, df: pd.DataFrame) -> float:
        """
        计算全市场平均AR人气指标
        :param df: 股票数据
        :return: 平均AR
        """
        try:
            df['ar'] = df.apply(self.calculate_ar_indicator, axis=1)
            return round(df['ar'].mean(), 2)
        except Exception as e:
            logger.error(f"计算平均AR失败: {e}")
            return 100.0

    def calculate_smb_hml_factors(self, df: pd.DataFrame) -> Tuple[float, float]:
        """
        SMB/HML多因子计算
        :param df: 股票数据
        :return: (SMB, HML)
        """
        try:
            df = df.copy()
            df = df[df['turn'] != 0]
            df = df.dropna(subset=['volume', 'turn', 'pbMRQ', 'pctChg'])
            if df.empty:
                return 0.0, 0.0
            df['mkt'] = df['volume'] / df['turn']
            df['SB'] = df['mkt'].map(lambda x: 'B' if x >= df['mkt'].median() else 'S')
            df['BM'] = 1 / df['pbMRQ'].replace(0, np.inf)
            df['BM'] = df['BM'].replace([np.inf, -np.inf], np.nan)
            df = df.dropna(subset=['BM'])
            if df.empty:
                return 0.0, 0.0
            df['HML_group'] = df['BM'].apply(
                lambda x: self.classify_by_quantile(x, df, 'BM')
            )
            df['SB_HML'] = df['SB'] + '/' + df['HML_group']
            df['ret'] = df['pctChg'] / 100
            ret_groups = df.groupby(['SB_HML']).apply(
                lambda x: (x['ret'] * x['mkt']).sum() / x['mkt'].sum()
            ).reset_index().set_index('SB_HML')
            ret_groups.rename(columns={ret_groups.columns[-1]: 'ret'}, inplace=True)
            required_groups = ['S/L', 'S/M', 'S/H', 'B/L', 'B/M', 'B/H']
            missing_groups = [g for g in required_groups if g not in ret_groups.index]
            if missing_groups:
                for group in missing_groups:
                    ret_groups.loc[group] = {'ret': 0.0}
            small_cap_ret = (
                ret_groups.at['S/L', 'ret'] +
                ret_groups.at['S/M', 'ret'] +
                ret_groups.at['S/H', 'ret']
            ) / 3
            big_cap_ret = (
                ret_groups.at['B/L', 'ret'] +
                ret_groups.at['B/M', 'ret'] +
                ret_groups.at['B/H', 'ret']
            ) / 3
            smb = round(small_cap_ret - big_cap_ret, 4)
            high_bm_ret = (
                ret_groups.at['S/H', 'ret'] +
                ret_groups.at['B/H', 'ret']
            ) / 2
            low_bm_ret = (
                ret_groups.at['S/L', 'ret'] +
                ret_groups.at['B/L', 'ret']
            ) / 2
            hml = round(high_bm_ret - low_bm_ret, 4)
            return smb, hml
        except Exception as e:
            logger.error(f"计算SMB和HML因子失败: {e}")
            return 0.0, 0.0

    def process_single_date(self, date: str, df_all: pd.DataFrame) -> bool:
        """
        处理单个交易日：市场统计+多因子
        :param date: 交易日期
        :param df_all: 全部历史数据
        :return: 是否成功
        """
        try:
            df = df_all[df_all['tradedate'] == date].copy()
            if df.empty:
                logger.warning(f"日期 {date} 没有股票数据，跳过")
                return False
            metrics = self.calculate_daily_metrics(df, date)
            # 多因子
            smb, hml = self.calculate_smb_hml_factors(df)
            ar = self.calculate_average_ar(df)
            metrics.SMB = smb
            metrics.HML = hml
            metrics.AR = ar
            # 总市值
            total_value = self.get_market_total_value(date)
            if total_value is not None:
                metrics.totalvalue = total_value
            return self.save_daily_metrics(metrics)
        except Exception as e:
            logger.error(f"处理日期 {date} 失败: {e}")
            return False

    def save_daily_metrics(self, metrics: DailyMetrics) -> bool:
        """
        写入Daliy表，字段兼容原有
        :param metrics: DailyMetrics对象
        :return: 是否成功
        """
        print(metrics)
        try:
            with db_manager.get_session() as session:
                new_daily = Daliy(
                    date=metrics.date,
                    limitdown=metrics.limitdown,
                    limitup=metrics.limitup,
                    newhigh=metrics.newhigh,
                    newlow=metrics.newlow,
                    averagechange=metrics.averagechange,
                    averageprice=metrics.averageprice,
                    changeratio=metrics.changeratio,
                    crowding=metrics.crowding,
                    medianincrease=metrics.medianincrease,
                    brokenpb=metrics.brokenpb,
                    rose3=metrics.rose3,
                    fall3=metrics.fall3,
                    UES=metrics.UES,
                    style=metrics.style,
                    totalvalue=metrics.totalvalue,
                    SMB=metrics.SMB,
                    HML=metrics.HML,
                    AR=metrics.AR
                )
                session.merge(new_daily)
                session.commit()
            logger.debug(f"保存日期 {metrics.date} 的指标数据成功")
            return True
        except Exception as e:
            logger.error(f"保存日期 {metrics.date} 的指标数据失败: {e}")
            return False

    @monitor_performance
    def process_batch(self, begin: str, end: str, max_workers: int = 4) -> bool:
        """
        批量处理主流程，支持多线程
        :param begin: 起始日期
        :param end: 结束日期
        :param max_workers: 线程数
        :return: 是否成功
        """
        try:
            logger.info(f"开始批量处理: {begin} - {end}")
            df_all = fetch_all_history(begin=begin, end=end)
            if df_all.empty:
                logger.warning(f"时间段 {begin} - {end} 没有历史数据")
                return False
            with db_manager.get_session() as session:
                trading_days = session.query(Daliy).filter(
                    and_(Daliy.date <= end, Daliy.date >= begin, Daliy.isOpen == True)
                ).order_by(Daliy.date.asc()).all()
                trading_dates = [d.date for d in trading_days]
            if not trading_dates:
                logger.warning(f"时间段 {begin} - {end} 没有交易日数据")
                return False
            logger.info(f"准备处理 {len(trading_dates)} 个交易日，线程数: {max_workers}")
            success_count = 0
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_date = {
                    executor.submit(self.process_single_date, date, df_all): date
                    for date in trading_dates
                }
                for future in as_completed(future_to_date):
                    date = future_to_date[future]
                    try:
                        success = future.result()
                        if success:
                            success_count += 1
                    except Exception as e:
                        logger.error(f"处理日期 {date} 异常: {e}")
            logger.info(f"批量处理完成: 成功 {success_count}/{len(trading_dates)} 个交易日")
            return success_count > 0
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return False

@monitor_performance
def main():
    """
    命令行主入口
    支持参数：-d 指定日期，-p 区间批量，-w 线程数
    """
    try:
        logger.info("🚀 启动每日量化+多因子一体化批量处理系统（合并版）")
        arg_parser = argparse.ArgumentParser(description="每日量化+多因子一体化批量处理系统（合并版）")
        arg_parser.add_argument("-d", "--date", required=False, type=str, help="输入指定日期 yyyymmdd")
        arg_parser.add_argument("-p", "--period", required=False, type=check_bool, nargs='?', const=True, default=False, help="批量处理从开始到指定日期的所有数据")
        arg_parser.add_argument("-w", "--workers", required=False, type=int, default=4, help="工作线程数")
        args = arg_parser.parse_args()
        end_date = args.date
        period = args.period
        max_workers = args.workers
        begin_date = ago_day_timestr(125, '%Y%m%d')
        today = time.strftime('%Y%m%d', time.localtime(time.time()))
        # 参数逻辑：支持单日、区间、批量
        if end_date:
            if period:
                start_date, target_date = begin_date, end_date
            else:
                start_date, target_date = end_date, end_date
        else:
            start_date, target_date = today, today
        logger.info(f"执行时间范围: {start_date} - {target_date}")
        with DailyMultiFactorAnalyzer() as analyzer:
            success = analyzer.process_batch(start_date, target_date, max_workers)
        if success:
            quant_monitor(28, True)
            logger.info("✅ 每日量化+多因子一体化批量处理完成")
            return True
        else:
            quant_monitor(28, False)
            logger.error("❌ 每日量化+多因子一体化批量处理失败")
            return False
    except Exception as e:
        logger.error(f"每日量化+多因子一体化批量处理系统执行失败: {e}")
        quant_monitor(28, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)
