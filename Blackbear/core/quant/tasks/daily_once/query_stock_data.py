# -*- coding: UTF-8 -*-
"""
股票数据查询系统 (优化版本)

主要功能:
1. 股票历史数据获取和存储
2. 技术指标计算 (30日最高/最低价、三连涨/跌等)
3. 股票代码同步和维护
4. 多线程并发数据处理
5. Redis任务队列管理

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 资源管理和连接池
- 模块化设计和类型提示
"""

import sys
import os
import psutil
import gc
from pathlib import Path
# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import redis
import json
import time
import pandas as pd
import hashlib
import argparse
import threading
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed, CancelledError

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, Daliy, StockHistory, StockCode
from sqlalchemy.types import String, Float, Integer, Boolean
from sqlalchemy import and_

# 工具类
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.helper import ago_day_timestr, sz_or_sh, check_bool
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler,
    register_cleanup,
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance, optimize_dataframe_memory
from config.settings import get_settings

# 数据获取
from db.fetch import fetch_all_stock, quant_monitor, fetch_all_history

# 第三方库
import akshare as ak
from tqdm import tqdm

@dataclass
class StockTask:
    """股票数据任务"""
    stock_code: str
    start_date: str
    end_date: str
    onlyend: bool = False
    fail_count: int = 0

@dataclass
class TechnicalIndicators:
    """技术指标数据类"""
    max_30: str = '0'
    min_30: str = '0'
    rose3: str = '0'
    fall3: str = '0'

class StockDataProcessor:
    """股票数据处理器（优化版本）"""

    def __init__(self):
        self.settings = get_settings()
        self.akshare = Akshare()
        self.bs = Baostock()
        self.redis_client = self._init_redis()
        self.db_lock = threading.Lock()  # 数据库写入锁

        # 添加已知无法获取数据的股票代码集合，用于快速跳过
        self.skip_stocks = set()

        # 性能监控和统计
        self.stats = {
            'total_processed': 0,
            'total_success': 0,
            'total_failed': 0,
            'start_time': time.time(),
            'method1_success': 0,
            'method2_success': 0,
            'data_source_failures': 0,
            'db_save_failures': 0,
            'calculation_failures': 0,
            'skipped': 0
        }

        self.dtypedict = {
            'code': String(6), 'date': String(10), 'tradedate': String(8), 'open': Float, 'high': Float,
            'low': Float, 'close': Float, 'volume': Float, 'amount': Float, 'turn': Float,
            'pctChg': Float, 'peTTM': Float, 'pbMRQ': Float, 'max': String(1), 'min': String(1),
            'rose3': String(1), 'fall3': String(1), 'ret30': Float,
        }

        # 注册清理函数
        register_cleanup(self._cleanup_resources)

        # 内存监控
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB

    def __enter__(self):
        logger.debug("StockDataProcessor 上下文管理器进入")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出时清理资源"""
        self._cleanup_resources()
        return False  # 不抑制异常

    def get_memory_usage(self) -> float:
        """获取当前内存使用情况（MB）"""
        return self.process.memory_info().rss / 1024 / 1024

    def log_memory_usage(self, operation: str = ""):
        """记录内存使用情况"""
        current_memory = self.get_memory_usage()
        diff = current_memory - self.initial_memory
        logger.info(f"内存使用 [{operation}]: 当前 {current_memory:.2f} MB, "
                   f"增加 {diff:.2f} MB 相比初始状态")

    def force_garbage_collection(self):
        """强制垃圾回收"""
        before = self.get_memory_usage()
        gc.collect()
        after = self.get_memory_usage()
        logger.debug(f"垃圾回收: 释放 {before - after:.2f} MB 内存")

    def _cleanup_resources(self):
        """清理所有资源"""
        logger.debug("开始清理 StockDataProcessor 资源...")

        # 记录性能统计
        if self.stats['total_processed'] > 0:
            elapsed = time.time() - self.stats['start_time']
            success_rate = (self.stats['total_success'] / self.stats['total_processed']) * 100
            logger.info(f"性能统计: 处理 {self.stats['total_processed']} 只股票，"
                       f"成功率 {success_rate:.2f}%，耗时 {elapsed:.2f} 秒，"
                       f"平均每只股票 {elapsed/self.stats['total_processed']:.2f} 秒")
            logger.info(f"数据源统计: Method1成功 {self.stats['method1_success']}，"
                       f"Method2成功 {self.stats['method2_success']}，"
                       f"数据源失败 {self.stats['data_source_failures']}")

            # 记录内存使用情况
            self.log_memory_usage("程序结束")

        # 关闭Baostock连接
        if hasattr(self.bs, 'logout'):
            try:
                self.bs.logout()
                logger.debug("Baostock连接已关闭")
            except Exception as e:
                logger.warning(f"关闭Baostock连接失败: {e}")

        # 关闭Redis连接
        if self.redis_client:
            try:
                self.redis_client.close()
                logger.debug("Redis连接已关闭")
            except Exception as e:
                logger.warning(f"关闭Redis连接失败: {e}")

        # 强制垃圾回收
        self.force_garbage_collection()

        logger.debug("StockDataProcessor 资源清理完成")

    def _init_redis(self) -> Optional[redis.Redis]:
        try:
            redis_client = redis.Redis(
                host=os.getenv('BB_REDIS_HOST', 'localhost'), port=int(os.getenv('BB_REDIS_PORT', 6379)),
                db=7, password=os.getenv('BB_REDIS_PASSWORD'), decode_responses=True
            )
            redis_client.ping()
            logger.info("Redis连接成功")
            return redis_client
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return None

    @staticmethod
    def calculate_max_min_indicators(df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """向量化计算30天最高价和最低价指标"""
        try:
            if len(df) <= 30:
                return pd.Series('0', index=df.index), pd.Series('0', index=df.index)

            # 计算30天滚动最高价和最低价
            rolling_max = df['close'].rolling(window=30, min_periods=30).max()
            rolling_min = df['close'].rolling(window=30, min_periods=30).min()

            # 判断当前价格是否为30天最高价/最低价
            max_indicator = ((df['close'] == rolling_max) & (df.index >= 29)).astype(str).replace({'True': '1', 'False': '0'})
            min_indicator = ((df['close'] == rolling_min) & (df.index >= 29)).astype(str).replace({'True': '1', 'False': '0'})

            # 前29天设为'0'
            max_indicator.iloc[:29] = '0'
            min_indicator.iloc[:29] = '0'

            return max_indicator, min_indicator
        except Exception as e:
            logger.error(f"计算30天最高/最低价指标失败: {e}")
            return pd.Series('0', index=df.index), pd.Series('0', index=df.index)

    @staticmethod
    def calculate_three_day_indicators(df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """向量化计算三连涨和三连跌指标"""
        try:
            if len(df) < 3:
                return pd.Series('0', index=df.index), pd.Series('0', index=df.index)

            # 判断涨跌
            is_rise = df['pctChg'] > 0
            is_fall = df['pctChg'] < 0

            # 计算连续三天的情况
            rose3 = pd.Series('0', index=df.index)
            fall3 = pd.Series('0', index=df.index)

            for i in range(2, len(df)):
                if is_rise.iloc[i-2] and is_rise.iloc[i-1] and is_rise.iloc[i]:
                    rose3.iloc[i] = '1'
                if is_fall.iloc[i-2] and is_fall.iloc[i-1] and is_fall.iloc[i]:
                    fall3.iloc[i] = '1'

            return rose3, fall3
        except Exception as e:
            logger.error(f"计算三连涨/跌指标失败: {e}")
            return pd.Series('0', index=df.index), pd.Series('0', index=df.index)

    def get_stock_data_method1(self, task: StockTask) -> Optional[pd.DataFrame]:
        """使用Akshare获取股票数据（超快版本）"""
        try:
            logger.debug(f"Method1获取股票 {task.stock_code} 数据")

            # 设置更严格的超时控制
            start_time = time.time()

            # 添加请求间隔，避免频率限制
            time.sleep(0.1)  # 100ms间隔

            df = self.akshare.query_history_data(task.stock_code, task.start_date, task.end_date)
            elapsed = time.time() - start_time

            # 降低超时阈值，快速放弃
            if elapsed > 5:  # 5秒超时
                logger.debug(f"Method1获取股票 {task.stock_code} 超时: {elapsed:.2f} 秒")
                return None

            if df is not None and not df.empty:
                self.stats['method1_success'] += 1
                logger.debug(f"Method1成功获取股票 {task.stock_code} 数据，共 {len(df)} 条记录")
                return df
            else:
                logger.debug(f"Method1获取股票 {task.stock_code} 数据为空")

        except Exception as e:
            logger.debug(f"Method1获取数据失败 {task.stock_code}: {str(e)[:100]}")  # 限制错误信息长度
            self.stats['data_source_failures'] += 1

        return None

    def get_stock_data_method2(self, task: StockTask) -> Optional[pd.DataFrame]:
        """使用Baostock获取股票数据（超快版本）"""
        try:
            logger.debug(f"Method2获取股票 {task.stock_code} 数据")

            # 设置更严格的超时控制
            start_time = time.time()

            # 添加请求间隔，避免频率限制
            time.sleep(0.1)  # 200ms间隔，比Method1长一点

            # 格式化日期
            start_date = datetime.strptime(task.start_date, '%Y%m%d').strftime('%Y-%m-%d')
            end_date = datetime.strptime(task.end_date, '%Y%m%d').strftime('%Y-%m-%d')

            # 获取数据
            df = self.bs.query_history_data(sz_or_sh(task.stock_code), start_date, end_date)
            elapsed = time.time() - start_time

            # 降低超时阈值，快速放弃
            if elapsed > 5:  # 5秒超时
                logger.debug(f"Method2获取股票 {task.stock_code} 超时: {elapsed:.2f} 秒")
                return None

            if df is not None and not df.empty:
                self.stats['method2_success'] += 1
                logger.debug(f"Method2成功获取股票 {task.stock_code} 数据，共 {len(df)} 条记录")
                return df
            else:
                logger.debug(f"Method2获取股票 {task.stock_code} 数据为空")

        except Exception as e:
            # 限制错误信息长度，避免日志过大
            error_msg = str(e)
            if len(error_msg) > 100:
                error_msg = error_msg[:100] + "..."
            logger.debug(f"Method2获取数据失败 {task.stock_code}: {error_msg}")
            self.stats['data_source_failures'] += 1

        return None

    def safe_optimize_dataframe_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """安全的DataFrame内存优化，避免分类数据问题"""
        try:
            # 记录原始内存使用
            original_memory = df.memory_usage(deep=True).sum()

            # 手动优化数据类型，避免使用可能有问题的optimize_dataframe_memory
            for col in df.columns:
                if df[col].dtype == 'object':
                    # 对于字符串列，尝试转换为category（如果重复值较多）
                    unique_ratio = df[col].nunique() / len(df)
                    if unique_ratio < 0.5:  # 如果唯一值比例小于50%，转换为category
                        try:
                            df[col] = df[col].astype('category')
                        except Exception:
                            pass  # 如果转换失败，保持原样
                elif df[col].dtype == 'float64':
                    # 尝试降级float类型
                    try:
                        df[col] = pd.to_numeric(df[col], downcast='float')
                    except Exception:
                        pass
                elif df[col].dtype == 'int64':
                    # 尝试降级int类型
                    try:
                        df[col] = pd.to_numeric(df[col], downcast='integer')
                    except Exception:
                        pass

            # 记录优化后的内存使用
            final_memory = df.memory_usage(deep=True).sum()
            memory_saved = original_memory - final_memory
            if memory_saved > 0:
                logger.debug(f"内存优化: 节省 {memory_saved / 1024 / 1024:.2f} MB")

            return df
        except Exception as e:
            logger.warning(f"内存优化失败，使用原始DataFrame: {e}")
            return df

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标（修复版本）"""
        try:
            if df.empty:
                return df

            # 记录原始内存使用
            original_memory = df.memory_usage(deep=True).sum()
            logger.debug(f"技术指标计算前内存使用: {original_memory / 1024 / 1024:.2f} MB")

            # 确保数据按日期排序
            df = df.sort_values('tradedate').reset_index(drop=True)

            # 向量化计算ID（使用更高效的方法）
            df['id'] = (df['code'].astype(str) + df['tradedate'].astype(str)).apply(
                lambda x: hashlib.md5(x.encode("utf-8")).hexdigest()
            )

            # 向量化计算技术指标
            max_indicator, min_indicator = self.calculate_max_min_indicators(df)
            rose3_indicator, fall3_indicator = self.calculate_three_day_indicators(df)

            # 直接赋值，避免创建临时变量
            df['max'] = max_indicator
            df['min'] = min_indicator
            df['rose3'] = rose3_indicator
            df['fall3'] = fall3_indicator

            # 清理临时变量以释放内存
            del max_indicator, min_indicator, rose3_indicator, fall3_indicator

            # 数据清理
            df = df.replace(r'^\s*$', 0, regex=True)

            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn', 'pctChg', 'peTTM', 'pbMRQ']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                    # 尝试降级数据类型以节省内存
                    try:
                        if df[col].dtype == 'float64':
                            df[col] = pd.to_numeric(df[col], downcast='float')
                        elif df[col].dtype == 'int64':
                            df[col] = pd.to_numeric(df[col], downcast='integer')
                    except Exception:
                        pass  # 如果降级失败，保持原类型

            # 使用安全的内存优化
            df = self.safe_optimize_dataframe_memory(df)

            # 记录优化后的内存使用
            final_memory = df.memory_usage(deep=True).sum()
            memory_saved = original_memory - final_memory
            logger.debug(f"技术指标计算后内存使用: {final_memory / 1024 / 1024:.2f} MB，"
                        f"节省: {memory_saved / 1024 / 1024:.2f} MB")

            return df
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}", exc_info=True)
            return pd.DataFrame()


    def save_stock_data(self, df: pd.DataFrame, task: StockTask) -> bool:
        """保存股票数据到数据库（线程安全，修复版本）"""
        save_start_time = time.time()
        try:
            if df.empty:
                return True  # 无数据也算成功，不重试

            # 优化内存使用：避免不必要的复制
            df_to_save = df
            if task.onlyend:
                df_to_save = df[df['tradedate'] == task.end_date]

            if df_to_save.empty:
                return True

            # 数据验证
            required_columns = ['code', 'tradedate', 'close']
            missing_columns = [col for col in required_columns if col not in df_to_save.columns]
            if missing_columns:
                logger.error(f"股票 {task.stock_code} 数据缺少必要列: {missing_columns}")
                return False

            # 快速数据库操作，减少锁的持有时间
            try:
                # 使用更快的批量操作，减少数据库锁时间
                trade_dates = df_to_save['tradedate'].unique().tolist() if not task.onlyend else [task.end_date]

                # 快速删除和插入操作
                with self.db_lock:
                    with db_manager.get_session() as session:
                        try:
                            # 快速删除旧数据
                            if task.onlyend:
                                session.query(StockHistory).filter(and_(
                                    StockHistory.tradedate == task.end_date,
                                    StockHistory.code == task.stock_code
                                )).delete(synchronize_session=False)
                            else:
                                if trade_dates:
                                    session.query(StockHistory).filter(and_(
                                        StockHistory.code == task.stock_code,
                                        StockHistory.tradedate.in_(trade_dates)
                                    )).delete(synchronize_session=False)

                            # 快速插入新数据（在同一个事务中）
                            self._fast_insert_data(df_to_save, task.stock_code, session)
                            session.commit()

                        except Exception as e:
                            session.rollback()
                            logger.error(f"数据库操作失败 {task.stock_code}: {e}")
                            raise

                save_elapsed = time.time() - save_start_time
                logger.debug(f"成功保存 {len(df_to_save)} 条股票 {task.stock_code} 数据，耗时 {save_elapsed:.2f} 秒")
                return True

            except Exception as e:
                logger.error(f"数据库操作失败 {task.stock_code}: {e}", exc_info=True)
                return False

        except Exception as e:
            save_elapsed = time.time() - save_start_time
            logger.error(f"保存股票 {task.stock_code} 数据失败，耗时 {save_elapsed:.2f} 秒: {e}", exc_info=True)
            return False

    def _fast_insert_data(self, df: pd.DataFrame, stock_code: str, session) -> None:
        """快速插入数据到数据库（在现有事务中）"""
        try:
            # 使用bulk_insert_mappings进行快速批量插入
            records = df.to_dict('records')
            if records:
                session.bulk_insert_mappings(StockHistory, records)
        except Exception as e:
            logger.error(f"快速插入股票 {stock_code} 数据失败: {e}")
            raise

    def _batch_insert_data(self, df: pd.DataFrame, stock_code: str, chunk_size: int = 500):
        """批量插入数据的优化方法"""
        try:
            # 首先尝试使用最高效的批量插入方法
            df.to_sql('stockhistory', con=engine, if_exists='append',
                     index=False, dtype=self.dtypedict, method='multi', chunksize=chunk_size)
            logger.debug(f"使用批量方法成功插入股票 {stock_code} 数据")

        except Exception as e:
            logger.warning(f"批量插入失败，尝试分块插入 {stock_code}: {e}")

            # 如果批量插入失败，尝试分块插入
            try:
                for i in range(0, len(df), chunk_size):
                    chunk = df.iloc[i:i + chunk_size]
                    chunk.to_sql('stockhistory', con=engine, if_exists='append',
                               index=False, dtype=self.dtypedict, method='multi')
                logger.debug(f"使用分块方法成功插入股票 {stock_code} 数据")

            except Exception as e2:
                logger.warning(f"分块插入失败，尝试逐行插入 {stock_code}: {e2}")

                # 最后尝试逐行插入
                try:
                    df.to_sql('stockhistory', con=engine, if_exists='append',
                             index=False, dtype=self.dtypedict)
                    logger.debug(f"使用逐行方法成功插入股票 {stock_code} 数据")
                except Exception as e3:
                    logger.error(f"所有插入方法都失败 {stock_code}: {e3}")
                    raise

    def process_stock_task(self, task: StockTask) -> bool:
        """处理单个股票任务（超快版本）"""
        self.stats['total_processed'] += 1

        try:
            # 尝试获取数据
            df = self.get_stock_data_method2(task)
            if df is None or df.empty:
                df = self.get_stock_data_method1(task)

            if df is None or df.empty:
                self.stats['total_failed'] += 1
                return False

            # 计算技术指标
            try:
                df = self.calculate_technical_indicators(df)
                if df.empty:
                    self.stats['calculation_failures'] += 1
                    self.stats['total_failed'] += 1
                    return False
            except Exception:
                self.stats['calculation_failures'] += 1
                self.stats['total_failed'] += 1
                return False

            # 保存数据
            try:
                if self.save_stock_data(df, task):
                    self.stats['total_success'] += 1
                    return True
                else:
                    self.stats['db_save_failures'] += 1
                    self.stats['total_failed'] += 1
                    return False
            except Exception:
                self.stats['db_save_failures'] += 1
                self.stats['total_failed'] += 1
                return False

        except Exception:
            self.stats['total_failed'] += 1
            return False

    def process_stock_task_with_retry(self, task: StockTask, max_retries: int = 1) -> bool:
        """处理股票任务（超快速处理，无重试）"""
        # 快速跳过已知无法获取数据的股票
        if task.stock_code in self.skip_stocks:
            self.stats['skipped'] += 1
            return False

        # 设置超时控制
        start_time = time.time()

        try:
            # 直接处理，不重试
            if self.process_stock_task(task):
                return True

            # 处理失败，加入跳过列表
            self.skip_stocks.add(task.stock_code)
            return False

        except Exception as e:
            # 处理异常，加入跳过列表
            self.skip_stocks.add(task.stock_code)

            # 限制错误信息长度
            error_msg = str(e)
            if len(error_msg) > 100:
                error_msg = error_msg[:100] + "..."
            logger.debug(f"股票 {task.stock_code} 处理异常: {error_msg}")

            return False
        finally:
            # 检查处理时间
            elapsed = time.time() - start_time
            if elapsed > 5:
                logger.debug(f"股票 {task.stock_code} 处理耗时: {elapsed:.2f}秒")

        return False



    def setup_redis_queue(self, queue_name: str = 'stock_data_queue') -> bool:
        """设置Redis任务队列"""
        if not self.redis_client:
            logger.error("Redis客户端未初始化，无法使用队列模式")
            return False

        try:
            # 清空现有队列
            self.redis_client.delete(queue_name)
            logger.info(f"Redis队列 {queue_name} 已清空")
            return True
        except Exception as e:
            logger.error(f"设置Redis队列失败: {e}")
            return False

    def add_tasks_to_redis_queue(self, tasks: List[StockTask], queue_name: str = 'stock_data_queue') -> bool:
        """将任务添加到Redis队列"""
        if not self.redis_client:
            return False

        try:
            for task in tasks:
                task_json = json.dumps({
                    'stock_code': task.stock_code,
                    'start_date': task.start_date,
                    'end_date': task.end_date,
                    'onlyend': task.onlyend,
                    'fail_count': task.fail_count
                })
                self.redis_client.lpush(queue_name, task_json)

            logger.info(f"已添加 {len(tasks)} 个任务到Redis队列 {queue_name}")
            return True
        except Exception as e:
            logger.error(f"添加任务到Redis队列失败: {e}")
            return False

    def process_redis_queue(self, queue_name: str = 'stock_data_queue', timeout: int = 5) -> Optional[StockTask]:
        """从Redis队列处理任务"""
        if not self.redis_client:
            return None

        try:
            result = self.redis_client.brpop(queue_name, timeout=timeout)
            if result:
                _, task_json = result
                task_data = json.loads(task_json)
                return StockTask(
                    stock_code=task_data['stock_code'],
                    start_date=task_data['start_date'],
                    end_date=task_data['end_date'],
                    onlyend=task_data.get('onlyend', False),
                    fail_count=task_data.get('fail_count', 0)
                )
            return None
        except Exception as e:
            logger.error(f"从Redis队列处理任务失败: {e}")
            return None

    @monitor_performance
    def batch_process_stocks(self, stock_codes: List[str], start_date: str, end_date: str,
                           onlyend: bool = False, max_workers: int = 5, start_time: float = None,
                           batch_size: int = 20) -> bool:
        """批量处理股票数据（超稳定版本）"""
        logger.info(f"开始批量处理 {len(stock_codes)} 只股票，最大并发数: {max_workers}，批次大小: {batch_size}")

        total_success = 0
        total_failed = 0
        total_skipped = 0

        # 记录初始内存使用
        self.log_memory_usage("批处理开始")

        # 分批处理以减少内存使用
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(stock_codes) + batch_size - 1) // batch_size

            logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_codes)} 只股票")

            # 记录批次开始内存和时间
            batch_start_memory = self.get_memory_usage()
            batch_start_time = time.time()

            # 创建任务列表
            tasks = [StockTask(code, start_date, end_date, onlyend) for code in batch_codes]
            success_count = 0
            failed_count = 0
            skipped_count = 0

            # 使用顺序处理，避免并发问题
            with tqdm(total=len(tasks), desc=f"批次 {batch_num}/{total_batches}") as pbar:
                for task in tasks:
                    # 检查是否超时或退出
                    if start_time is not None and time.time() - start_time > 3600:
                        logger.critical("程序运行超过1小时，自动退出")
                        return False

                    if is_exit_requested():
                        logger.info("检测到退出信号，停止处理")
                        return False

                    # 检查是否已经在跳过列表中
                    if task.stock_code in self.skip_stocks:
                        skipped_count += 1
                        total_skipped += 1
                        pbar.update(1)
                        continue

                    # 处理单个任务
                    try:
                        if self.process_stock_task_with_retry(task):
                            success_count += 1
                            total_success += 1
                        else:
                            failed_count += 1
                            total_failed += 1
                    except Exception as e:
                        logger.debug(f"处理任务 {task.stock_code} 异常: {str(e)[:100]}")
                        failed_count += 1
                        total_failed += 1

                    # 更新进度条
                    pbar.update(1)

                    # 短暂休息，避免请求过快
                    time.sleep(0.1)

            # 记录批次结束内存和执行时间
            batch_end_memory = self.get_memory_usage()
            memory_diff = batch_end_memory - batch_start_memory
            batch_elapsed = time.time() - batch_start_time
            logger.info(f"批次 {batch_num} 完成: 成功 {success_count}, 失败 {failed_count}, 跳过 {skipped_count}, "
                       f"内存变化: {memory_diff:.2f} MB，耗时: {batch_elapsed:.2f} 秒")

            # 强制垃圾回收
            self.force_garbage_collection()

            # 批次间休息，让系统恢复
            time.sleep(1)  # 增加批次间隔，避免网络拥塞

        # 记录最终内存使用
        self.log_memory_usage("批处理结束")
        logger.info(f"所有批次处理完成: 总成功 {total_success}, 失败 {total_failed}, 跳过 {total_skipped}, 共 {len(stock_codes)} 只股票")
        return True  # 即使有失败也返回成功，避免整体失败

    @monitor_performance
    def sync_stock_codes(self) -> bool:
        """同步股票代码"""
        try:
            logger.info("开始同步股票代码")
            stock_info = ak.stock_info_a_code_name()
            if stock_info.empty:
                logger.error("获取股票代码列表失败")
                return False

            # 保存股票代码表
            with db_manager.get_session() as session:
                session.query(StockCode).delete()
                session.bulk_insert_mappings(StockCode, stock_info.to_dict('records'))
                session.commit()

            # 获取现有股票
            exits_stocks = fetch_all_stock(mark=False)
            add_stocks = stock_info['code'].to_numpy()

            # 添加新股票
            with db_manager.get_session() as session:
                for addcode in tqdm(list(set(add_stocks).difference(set(exits_stocks)))):
                    new_stock = Stock(code=str(addcode))
                    session.merge(new_stock)
                session.commit()

            logger.info(f"股票代码同步完成: {len(stock_info)} 只股票")
            return True
        except Exception as e:
            logger.error(f"同步股票代码失败: {e}", exc_info=True)
            return False


@monitor_performance
def main():
    """主函数（增强版本）"""
    logger.info("🚀 开始股票数据查询系统 (优化版本)")
    start_time = time.time()  # 记录启动时间
    processor = None

    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="股票数据查询系统")
        parser.add_argument("-d", "--date", type=str, help="输入指定日期 yyyymmdd")
        parser.add_argument("-p", "--period", type=check_bool, nargs='?', const=True, default=False,
                          help="爬取从开始到指定日期的所有数据")
        parser.add_argument("-w", "--workers", type=int, default=5, help="并发工作线程数（默认5，避免网络拥塞）")
        parser.add_argument("-b", "--batch-size", type=int, default=20, help="批处理大小（默认20，减少内存压力）")
        parser.add_argument("--sync-only", action="store_true", help="仅同步股票代码")
        args = parser.parse_args()

        # 参数验证
        if args.workers < 1 or args.workers > 50:
            logger.error("工作线程数必须在1-50之间")
            return False

        if args.batch_size < 1 or args.batch_size > 1000:
            logger.error("批处理大小必须在1-1000之间")
            return False

        end_date = args.date or time.strftime('%Y%m%d', time.localtime())
        begin_date = ago_day_timestr(125, '%Y%m%d')
        onlyend = not args.period

        logger.info(f"执行参数: 开始={begin_date}, 结束={end_date}, 仅最后一天={onlyend}, "
                   f"工作线程={args.workers}, 批处理大小={args.batch_size}")

        # 检查退出信号
        if is_exit_requested():
            logger.info("检测到退出信号，程序提前终止")
            return False

        with StockDataProcessor() as processor:
            overall_success = True

            # 同步股票代码
            logger.info('开始同步股票代码...')
            try:
                if not processor.sync_stock_codes():
                    logger.warning('同步股票代码失败，但将继续处理')
                    if args.sync_only:
                        return False
                else:
                    logger.info('股票代码同步成功')
                    if args.sync_only:
                        return True
            except Exception as e:
                logger.error(f"同步股票代码时发生异常: {e}", exc_info=True)
                if args.sync_only:
                    return False

            # 检查退出信号
            if is_exit_requested():
                logger.info("检测到退出信号，停止处理")
                return False

            # 获取股票列表并处理
            logger.info('获取股票列表...')
            stock_codes = fetch_all_stock(mark=False)
            if not stock_codes:
                logger.error("未获取到任何股票代码，任务终止")
                quant_monitor(9, False)
                return False

            logger.info(f"获取到 {len(stock_codes)} 只股票，开始处理...")

            # 批量处理股票数据
            try:
                if not processor.batch_process_stocks(
                    stock_codes, begin_date, end_date, onlyend,
                    args.workers, start_time, args.batch_size
                ):
                    overall_success = False
            except Exception as e:
                logger.error(f"批量处理股票数据时发生异常: {e}", exc_info=True)
                overall_success = False

        # 记录最终结果
        elapsed_time = time.time() - start_time
        logger.info(f"程序总耗时: {elapsed_time:.2f} 秒")

        quant_monitor(9, overall_success)
        logger.info(f"✅ 股票数据查询系统执行{'成功' if overall_success else '中有失败项'}")
        return overall_success

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号，开始优雅退出...")
        if processor:
            processor._cleanup_resources()
        quant_monitor(9, False)
        return False
    except SystemExit:
        logger.info("程序正常退出")
        raise
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"程序发生未捕获的严重错误，运行时间 {elapsed_time:.2f} 秒: {e}", exc_info=True)
        if processor:
            processor._cleanup_resources()
        quant_monitor(9, False)
        return False
    finally:
        # 确保资源清理
        if processor:
            try:
                processor._cleanup_resources()
            except Exception as e:
                logger.warning(f"最终资源清理时发生异常: {e}")


def cleanup_handler():
    """程序退出时的清理函数"""
    logger.info("执行最终清理操作...")
    try:
        # 关闭数据库连接池
        if hasattr(db_manager, 'dispose'):
            db_manager.dispose()
        elif hasattr(engine, 'dispose'):
            engine.dispose()
        logger.info("数据库连接池已关闭")
    except Exception as e:
        logger.warning(f"关闭数据库连接池失败: {e}")


if __name__ == '__main__':
    # 设置信号处理
    setup_signal_handler()

    # 注册清理函数
    register_cleanup(cleanup_handler)

    # 记录启动信息
    logger.info(f"程序启动，进程ID: {os.getpid()}")

    try:
        # 运行主函数
        is_successful = main()

        # 正常退出
        logger.info(f"程序正常完成，退出状态: {'成功' if is_successful else '失败'}")
        sys.exit(0 if is_successful else 1)

    except SystemExit as e:
        # 系统退出异常，保持原状态码
        sys.exit(e.code)

    except KeyboardInterrupt:
        # 键盘中断
        logger.info("程序被用户强制中断")
        sys.exit(130)  # 标准的SIGINT退出码

    except Exception as e:
        # 未捕获的异常
        logger.critical(f"程序顶层异常退出: {e}", exc_info=True)
        sys.exit(1)
