# -*- coding: UTF-8 -*-
"""
指数历史数据查询系统 (优化版本)

0 10 18  * * 1-5

主要功能:
1. 获取上证指数(sh.000001)历史数据
2. 获取深证成指(sz.399001)历史数据
3. 获取创业板指(sz.399006)历史数据
4. 计算综合市场指标
5. 数据清洗和验证

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import datetime
import time
import argparse
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession
from db.models import Daliy

# 工具类
from utils.baostock import Baostock
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool, ago_day_timestr
from config.settings import get_settings

# 数据获取
from db.fetch import quant_monitor

@dataclass
class IndexData:
    """指数数据类"""
    date: str
    code: str
    open: float
    high: float
    low: float
    close: float
    volume: float
    amount: float
    turn: float
    pct_chg: float

@dataclass
class MarketData:
    """市场综合数据类"""
    date: str
    total_volume: float
    sh_data: IndexData
    sz_data: IndexData
    cyb_data: IndexData

class IndexHistoryProcessor:
    """指数历史数据处理器"""

    def __init__(self):
        self.settings = get_settings()
        self.bs = Baostock()

        # 指数代码配置
        self.index_codes = {
            'sh': 'sh.000001',  # 上证指数
            'sz': 'sz.399001',  # 深证成指
            'cyb': 'sz.399006'  # 创业板指
        }

        # 数据列顺序
        self.column_order = [
            'date', 'code', 'open', 'high', 'low', 'close',
            'volume', 'amount', 'turn', 'pctChg', 'tradedate'
        ]

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        try:
            if hasattr(self.bs, 'logout'):
                self.bs.logout()
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")

    def get_index_data(self, index_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        获取指数历史数据

        :param index_code: 指数代码
        :param start_date: 开始日期 (YYYY-MM-DD)
        :param end_date: 结束日期 (YYYY-MM-DD)
        :return: 指数数据DataFrame
        """
        try:
            logger.debug(f"获取指数数据: {index_code} ({start_date} - {end_date})")

            # 获取数据
            df = self.bs.query_history_data(index_code, start_date, end_date)

            if df is None or df.empty:
                logger.warning(f"指数 {index_code} 数据为空")
                return None

            # 重新排列列顺序
            df = df.reindex(columns=self.column_order)

            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn', 'pctChg']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 清理无效数据
            df = df.dropna()

            logger.debug(f"指数 {index_code} 数据获取成功，行数: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"获取指数 {index_code} 数据失败: {e}")
            return None

    def parse_index_data(self, df: pd.DataFrame, index_name: str) -> List[IndexData]:
        """
        解析指数数据为结构化对象

        :param df: 指数数据DataFrame
        :param index_name: 指数名称
        :return: 指数数据对象列表
        """
        try:
            index_data_list = []

            for _, row in df.iterrows():
                index_data = IndexData(
                    date=row['date'],
                    code=row['code'],
                    open=float(row['open']) if pd.notna(row['open']) else 0.0,
                    high=float(row['high']) if pd.notna(row['high']) else 0.0,
                    low=float(row['low']) if pd.notna(row['low']) else 0.0,
                    close=float(row['close']) if pd.notna(row['close']) else 0.0,
                    volume=float(row['volume']) if pd.notna(row['volume']) else 0.0,
                    amount=float(row['amount']) if pd.notna(row['amount']) else 0.0,
                    turn=float(row['turn']) if pd.notna(row['turn']) else 0.0,
                    pct_chg=float(row['pctChg']) if pd.notna(row['pctChg']) else 0.0
                )
                index_data_list.append(index_data)

            logger.debug(f"{index_name} 数据解析完成，记录数: {len(index_data_list)}")
            return index_data_list

        except Exception as e:
            logger.error(f"解析 {index_name} 数据失败: {e}")
            return []

    def combine_market_data(self, sh_data: List[IndexData], sz_data: List[IndexData],
                          cyb_data: List[IndexData]) -> List[MarketData]:
        """
        合并市场数据

        :param sh_data: 上证指数数据
        :param sz_data: 深证成指数据
        :param cyb_data: 创业板指数据
        :return: 市场综合数据列表
        """
        try:
            market_data_list = []

            # 确保数据长度一致
            min_length = min(len(sh_data), len(sz_data), len(cyb_data))

            for i in range(min_length):
                # 计算总成交量
                total_volume = sh_data[i].volume + sz_data[i].volume

                market_data = MarketData(
                    date=sh_data[i].date.replace('-', ''),  # 转换为YYYYMMDD格式
                    total_volume=total_volume,
                    sh_data=sh_data[i],
                    sz_data=sz_data[i],
                    cyb_data=cyb_data[i]
                )
                market_data_list.append(market_data)

            logger.info(f"市场数据合并完成，记录数: {len(market_data_list)}")
            return market_data_list

        except Exception as e:
            logger.error(f"合并市场数据失败: {e}")
            return []

    def save_market_data(self, market_data_list: List[MarketData]) -> bool:
        """
        保存市场数据到数据库

        :param market_data_list: 市场数据列表
        :return: 是否成功
        """
        try:
            if not market_data_list:
                logger.warning("没有市场数据需要保存")
                return False

            with db_manager.get_session() as session:
                success_count = 0

                for market_data in market_data_list:
                    try:
                        new_daily = Daliy(
                            date=market_data.date,
                            volume=market_data.total_volume,
                            cybVolume=market_data.cyb_data.volume,
                            shVolume=market_data.sh_data.volume,
                            szVolume=market_data.sz_data.volume,
                            shOpen=market_data.sh_data.open,
                            shHigh=market_data.sh_data.high,
                            shLow=market_data.sh_data.low,
                            shClose=market_data.sh_data.close,
                            szClose=market_data.sz_data.close,
                            cybOpen=market_data.cyb_data.open,
                            cybHigh=market_data.cyb_data.high,
                            cybLow=market_data.cyb_data.low,
                            cybClose=market_data.cyb_data.close,
                            shClosePct=market_data.sh_data.pct_chg,
                            szClosePct=market_data.sz_data.pct_chg,
                            cybClosePct=market_data.cyb_data.pct_chg
                        )
                        session.merge(new_daily)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"保存日期 {market_data.date} 数据失败: {e}")
                        continue

                session.commit()

            logger.info(f"市场数据保存完成: 成功 {success_count}/{len(market_data_list)} 条记录")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存市场数据失败: {e}")
            return False

    @monitor_performance
    def process_index_history(self, start_date: str, end_date: str) -> bool:
        """
        处理指数历史数据

        :param start_date: 开始日期 (YYYY-MM-DD)
        :param end_date: 结束日期 (YYYY-MM-DD)
        :return: 是否成功
        """
        try:
            logger.info(f"开始处理指数历史数据: {start_date} - {end_date}")

            # 获取各指数数据
            sh_df = self.get_index_data(self.index_codes['sh'], start_date, end_date)
            sz_df = self.get_index_data(self.index_codes['sz'], start_date, end_date)
            cyb_df = self.get_index_data(self.index_codes['cyb'], start_date, end_date)

            # 检查数据完整性
            if sh_df is None or sz_df is None or cyb_df is None:
                logger.error("部分指数数据获取失败")
                return False

            if sh_df.empty or sz_df.empty or cyb_df.empty:
                logger.error("部分指数数据为空")
                return False

            # 解析数据
            sh_data = self.parse_index_data(sh_df, "上证指数")
            sz_data = self.parse_index_data(sz_df, "深证成指")
            cyb_data = self.parse_index_data(cyb_df, "创业板指")

            # 合并数据
            market_data_list = self.combine_market_data(sh_data, sz_data, cyb_data)

            # 保存数据
            success = self.save_market_data(market_data_list)

            if success:
                logger.info("✅ 指数历史数据处理完成")
            else:
                logger.error("❌ 指数历史数据处理失败")

            return success

        except Exception as e:
            logger.error(f"处理指数历史数据失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def query_index_history_data(start_date: str = None, end_date: str = None) -> bool:
    """指数历史数据查询 - 优化版本"""
    if not start_date:
        start_date = '2021-01-01'
    if not end_date:
        end_date = time.strftime('%Y-%m-%d', time.localtime())

    with IndexHistoryProcessor() as processor:
        return processor.process_index_history(start_date, end_date)

@monitor_performance
def main():
    """
    主函数 - 执行指数历史数据查询系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始指数历史数据查询系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="指数历史数据查询系统 (优化版本)")
        arg_parser.add_argument("-s", "--start", required=False, type=str,
                               default='2021-01-01', help="开始日期 YYYY-MM-DD")
        arg_parser.add_argument("-e", "--end", required=False, type=str,
                               help="结束日期 YYYY-MM-DD")
        args = arg_parser.parse_args()

        start_date = args.start
        end_date = args.end or time.strftime('%Y-%m-%d', time.localtime())

        logger.info(f"执行参数: 开始日期={start_date}, 结束日期={end_date}")

        # 执行数据处理
        success = query_index_history_data(start_date, end_date)

        # 更新监控状态
        if success:
            quant_monitor(24, True)
            logger.info("✅ 指数历史数据查询系统执行完成")
        else:
            quant_monitor(24, False)
            logger.error("❌ 指数历史数据查询系统执行失败")

        return success

    except Exception as e:
        logger.error(f"指数历史数据查询系统执行失败: {e}")
        quant_monitor(24, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)