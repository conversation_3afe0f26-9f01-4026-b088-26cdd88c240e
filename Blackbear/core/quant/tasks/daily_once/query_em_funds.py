# -*- coding: UTF-8 -*-
"""
东方财富基金数据查询系统 (优化版本)

0 0 20  * * 1-5

主要功能:
1. 获取开放式基金排行数据
2. 基金净值和收益率更新
3. 基金星级评定
4. 基金持股数据分析
5. 基金业绩监控

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import argparse
import time
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Fund, FundStock

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 第三方库
import akshare as ak
import pandas as pd
from sqlalchemy.dialects.mysql import insert

# 数据获取
from db.fetch import fetch_all_stock, quant_monitor
from tqdm import tqdm

@dataclass
class FundData:
    """基金数据类"""
    code: str
    name: str
    worth: float  # 单位净值
    day_rate: float  # 日增长率
    year_rate: float  # 今年来收益率
    star: int  # 星级 (0-1)

@dataclass
class FundHoldingData:
    """基金持股数据类"""
    fund_code: str
    fund_name: str
    stock_code: str
    amount: float  # 持股市值
    percent: float  # 占流通股比例

class EMFundProcessor:
    """东方财富基金数据处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 星级基金阈值 (前100名为星级基金)
        self.star_threshold = 100

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_fund_rank_data(self, symbol: str = "全部") -> Optional[pd.DataFrame]:
        """
        获取基金排行数据

        :param symbol: 基金类型
        :return: 基金排行DataFrame
        """
        try:
            logger.info(f"获取基金排行数据: {symbol}")

            # 获取基金排行数据
            df = ak.fund_open_fund_rank_em(symbol=symbol)

            if df.empty:
                logger.error("获取基金排行数据失败")
                return None

            # 数据清理
            df.columns = [col.strip() for col in df.columns]
            df = df.reset_index(drop=True)

            # 数据类型转换
            numeric_columns = ['单位净值', '日增长率', '今年来']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 按今年来收益率排序
            if '今年来' in df.columns:
                df = df.sort_values(by='今年来', ascending=False)

            # 清理无效数据
            df = df.dropna(subset=numeric_columns)

            logger.info(f"获取基金排行数据成功: {len(df)} 只基金")
            return df

        except Exception as e:
            logger.error(f"获取基金排行数据失败: {e}")
            return None

    def parse_fund_data(self, df: pd.DataFrame) -> List[FundData]:
        """
        解析基金数据

        :param df: 基金排行DataFrame
        :return: 基金数据列表
        """
        try:
            fund_data_list = []

            for index, row in df.iterrows():
                try:
                    # 判断是否为星级基金 (前100名)
                    is_star = 1 if index < self.star_threshold else 0

                    fund_data = FundData(
                        code=str(row['基金代码']),
                        name=str(row['基金简称']),
                        worth=float(row.get('单位净值', 0.0)),
                        day_rate=float(row.get('日增长率', 0.0)),
                        year_rate=float(row.get('今年来', 0.0)),
                        star=is_star
                    )
                    fund_data_list.append(fund_data)

                except (ValueError, KeyError) as e:
                    logger.warning(f"解析基金数据失败，跳过: {e}")
                    continue

            logger.info(f"基金数据解析完成: {len(fund_data_list)} 只基金")
            return fund_data_list

        except Exception as e:
            logger.error(f"解析基金数据失败: {e}")
            return []

    def clear_star_flags(self) -> bool:
        """
        清除所有基金的星级标记

        :return: 是否成功
        """
        try:
            logger.info("清除基金星级标记")

            with db_manager.get_session() as session:
                session.query(Fund).filter(Fund.star == 1).update({"star": 0})
                session.commit()

            logger.info("基金星级标记清除完成")
            return True

        except Exception as e:
            logger.error(f"清除基金星级标记失败: {e}")
            return False

    def save_fund_data(self, fund_data_list: List[FundData]) -> bool:
        """
        保存基金数据到数据库

        :param fund_data_list: 基金数据列表
        :return: 是否成功
        """
        try:
            if not fund_data_list:
                logger.warning("没有基金数据需要保存")
                return False

            with db_manager.get_session() as session:
                success_count = 0

                for fund_data in fund_data_list:
                    try:
                        item = {
                            'code': fund_data.code,
                            'name': fund_data.name,
                            'star': fund_data.star,
                            'worth': fund_data.worth,
                            'dayRate': fund_data.day_rate,
                            'yearRate': fund_data.year_rate
                        }

                        insert_stmt = insert(Fund).values(**item)
                        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)

                        session.execute(on_duplicate_key_stmt)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"保存基金 {fund_data.code} 数据失败: {e}")
                        continue

                session.commit()

            logger.info(f"基金数据保存完成: 成功 {success_count}/{len(fund_data_list)} 只基金")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存基金数据失败: {e}")
            return False

    def get_fund_holdings(self, stock_code: str, cutoff_date: str = "2021-09-30") -> Optional[List[FundHoldingData]]:
        """
        获取基金持股数据

        :param stock_code: 股票代码
        :param cutoff_date: 截止日期
        :return: 基金持股数据列表
        """
        try:
            logger.debug(f"获取股票 {stock_code} 的基金持股数据")

            # 获取基金持股数据
            df = ak.stock_fund_stock_holder(stock=stock_code)

            if df.empty:
                logger.debug(f"股票 {stock_code} 没有基金持股数据")
                return []

            # 过滤截止日期
            df = df[df['截止日期'] >= cutoff_date]

            if df.empty:
                logger.debug(f"股票 {stock_code} 在 {cutoff_date} 之后没有基金持股数据")
                return []

            # 解析持股数据
            holdings = []
            for _, row in df.iterrows():
                try:
                    holding = FundHoldingData(
                        fund_code=str(row['基金代码']),
                        fund_name=str(row['基金名称']),
                        stock_code=stock_code,
                        amount=float(row['持股市值（元）']),
                        percent=float(row['占流通股比例(%)'])
                    )
                    holdings.append(holding)
                except (ValueError, KeyError):
                    continue

            logger.debug(f"股票 {stock_code} 基金持股数据: {len(holdings)} 条记录")
            return holdings

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 基金持股数据失败: {e}")
            return []

    @monitor_performance
    def update_fund_rankings(self, symbol: str = "全部") -> bool:
        """
        更新基金排行数据

        :param symbol: 基金类型
        :return: 是否成功
        """
        try:
            logger.info("开始更新基金排行数据")

            # 清除星级标记
            if not self.clear_star_flags():
                logger.error("清除星级标记失败")
                return False

            # 获取基金排行数据
            df = self.get_fund_rank_data(symbol)
            if df is None:
                return False

            # 解析基金数据
            fund_data_list = self.parse_fund_data(df)
            if not fund_data_list:
                return False

            # 保存基金数据
            success = self.save_fund_data(fund_data_list)

            if success:
                star_count = sum(1 for f in fund_data_list if f.star == 1)
                logger.info(f"✅ 基金排行数据更新完成: 总计 {len(fund_data_list)} 只基金，"
                           f"其中星级基金 {star_count} 只")
            else:
                logger.error("❌ 基金排行数据更新失败")

            return success

        except Exception as e:
            logger.error(f"更新基金排行数据失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def query_em_funds(symbol: str = "全部") -> bool:
    """东方财富基金数据查询 - 优化版本"""
    with EMFundProcessor() as processor:
        return processor.update_fund_rankings(symbol)

@monitor_performance
def main():
    """
    主函数 - 执行东方财富基金数据查询系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始东方财富基金数据查询系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="东方财富基金数据查询系统 (优化版本)")
        arg_parser.add_argument("-s", "--symbol", required=False, type=str,
                               default="全部", help="基金类型")
        args = arg_parser.parse_args()

        symbol = args.symbol

        logger.info(f"执行参数: 基金类型={symbol}")

        # 执行基金数据查询
        success = query_em_funds(symbol)

        # 更新监控状态
        if success:
            quant_monitor(28, True)
            logger.info("✅ 东方财富基金数据查询系统执行完成")
        else:
            quant_monitor(28, False)
            logger.error("❌ 东方财富基金数据查询系统执行失败")

        return success

    except Exception as e:
        logger.error(f"东方财富基金数据查询系统执行失败: {e}")
        quant_monitor(28, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)