# -*- coding: UTF-8 -*-
"""
净值计算系统 (优化版本)

0 0 18  * * 1-5

主要功能:
1. 每日净值计算
2. 持仓市值统计
3. 申购赎回处理
4. 份额管理
5. 收益率计算

优化特性:
- 完善的错误处理和日志记录
- 性能监控和数据验证
- 模块化设计和类型提示
- 资源管理和连接池
- 详细的计算日志
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import argparse
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Worth, WorthInout

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 数据获取
from db.fetch import is_trade_date, quant_monitor

@dataclass
class PositionData:
    """持仓数据"""
    code: str
    amount: float
    cost: float
    value: float
    current_price: float
    market_value: float

@dataclass
class WorthData:
    """净值数据"""
    date: str
    net_worth: float
    total_amount: float
    total_share: float
    turnover: float
    return_rate: float = 0.0

@dataclass
class InOutData:
    """申购赎回数据"""
    date: str
    type: str  # 'in' or 'out'
    money: float

class WorthCalculator:
    """净值计算器"""

    def __init__(self):
        self.settings = get_settings()

        # 精度设置
        self.worth_precision = 4  # 净值精度
        self.amount_precision = 2  # 金额精度

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_position_data(self) -> List[PositionData]:
        """
        获取持仓数据

        :return: 持仓数据列表
        """
        try:
            logger.info("获取持仓数据")

            sql = '''
                SELECT
                    a.code,
                    a.amount,
                    a.cost,
                    a.value,
                    COALESCE(b.f2, 0) as f2
                FROM position a
                LEFT JOIN source b ON b.code = a.code
            '''

            positions = []
            with engine.connect() as conn:
                result = conn.execute(sql)

                for row in result:
                    # 计算市值
                    if row['code'] in ['cash', 'financ']:
                        # 现金和理财产品按原值计算
                        market_value = float(row['value']) if row['value'] else 0.0
                    elif row['f2'] and float(row['f2']) > 0:
                        # 有实时价格的股票按实时价格计算
                        market_value = float(row['amount']) * float(row['f2'])
                    else:
                        # 没有实时价格的按成本价计算
                        market_value = float(row['amount']) * float(row['cost'])

                    position = PositionData(
                        code=str(row['code']),
                        amount=float(row['amount']) if row['amount'] else 0.0,
                        cost=float(row['cost']) if row['cost'] else 0.0,
                        value=float(row['value']) if row['value'] else 0.0,
                        current_price=float(row['f2']) if row['f2'] else 0.0,
                        market_value=market_value
                    )
                    positions.append(position)

            total_value = sum(p.market_value for p in positions)
            logger.info(f"持仓数据获取完成: {len(positions)} 个持仓，总市值: {total_value:.2f}")
            return positions

        except Exception as e:
            logger.error(f"获取持仓数据失败: {e}")
            return []

    def get_last_worth(self, exclude_date: str) -> Optional[Dict]:
        """
        获取最近一次净值记录

        :param exclude_date: 排除的日期
        :return: 净值记录字典
        """
        try:
            with db_manager.get_session() as session:
                last_worth = session.query(Worth).filter(
                    Worth.totalShare > 0,
                    Worth.date != exclude_date
                ).order_by(Worth.date.desc()).first()

                if last_worth:
                    # 将对象转换为字典，避免会话绑定问题
                    worth_dict = {
                        'date': last_worth.date,
                        'worths': last_worth.worths,
                        'totalAmount': last_worth.totalAmount,
                        'totalShare': last_worth.totalShare
                    }
                    logger.debug(f"获取最近净值记录: 日期={worth_dict['date']}, 净值={worth_dict['worths']}")
                    return worth_dict
                else:
                    logger.info("没有找到历史净值记录，将初始化净值")
                    return None

        except Exception as e:
            logger.error(f"获取最近净值记录失败: {e}")
            return None

    def get_today_inouts(self, target_date: str) -> List[InOutData]:
        """
        获取当日申购赎回数据

        :param target_date: 目标日期
        :return: 申购赎回数据列表
        """
        try:
            logger.debug(f"获取当日申购赎回数据: {target_date}")

            with db_manager.get_session() as session:
                inouts = session.query(WorthInout).filter(
                    WorthInout.date == target_date
                ).all()

                inout_data = []
                for inout in inouts:
                    data = InOutData(
                        date=str(inout.date),
                        type=str(inout.type),
                        money=float(inout.money) if inout.money else 0.0
                    )
                    inout_data.append(data)

                logger.debug(f"当日申购赎回数据: {len(inout_data)} 条记录")
                return inout_data

        except Exception as e:
            logger.error(f"获取申购赎回数据失败: {e}")
            return []

    def calculate_turnover(self, inouts: List[InOutData]) -> float:
        """
        计算申购赎回净额

        :param inouts: 申购赎回数据列表
        :return: 净申购赎回金额
        """
        try:
            turnover = 0.0

            for inout in inouts:
                if inout.type == 'out':
                    turnover -= inout.money
                elif inout.type == 'in':
                    turnover += inout.money

            logger.debug(f"申购赎回净额: {turnover:.2f}")
            return turnover

        except Exception as e:
            logger.error(f"计算申购赎回净额失败: {e}")
            return 0.0

    def calculate_net_worth(self, total_value: float, last_worth: Optional[Dict],
                          turnover: float) -> WorthData:
        """
        计算净值

        :param total_value: 总市值
        :param last_worth: 上次净值记录字典
        :param turnover: 申购赎回净额
        :return: 净值数据
        """
        try:
            target_date = time.strftime('%Y-%m-%d', time.localtime())

            # 第一次申购时，净值初始化为1
            if not last_worth or last_worth.get('totalShare', 0) == 0:
                logger.info("初始化净值计算")

                worth_data = WorthData(
                    date=target_date,
                    net_worth=1.0,
                    total_amount=turnover,
                    total_share=turnover,
                    turnover=turnover
                )

                logger.info(f"初始净值: {worth_data.net_worth}, 初始金额: {worth_data.total_amount}")
                return worth_data

            # 正常净值计算
            logger.info("正常净值计算")

            # 先计算净值 (基于昨日份额)
            last_total_share = last_worth['totalShare']
            last_net_worth = last_worth['worths']

            net_worth = round(total_value / last_total_share, self.worth_precision)

            # 再计算申购赎回后的总金额和份额
            total_amount = round(total_value + turnover, self.amount_precision)
            total_share = round(last_total_share + turnover / net_worth, self.amount_precision)

            # 计算收益率
            return_rate = round((net_worth - last_net_worth) / last_net_worth * 100, 4)

            worth_data = WorthData(
                date=target_date,
                net_worth=net_worth,
                total_amount=total_amount,
                total_share=total_share,
                turnover=turnover,
                return_rate=return_rate
            )

            logger.info(f"净值计算完成: 净值={net_worth}, 收益率={return_rate}%")
            return worth_data

        except Exception as e:
            logger.error(f"计算净值失败: {e}")
            raise

    def save_worth_data(self, worth_data: WorthData) -> bool:
        """
        保存净值数据

        :param worth_data: 净值数据
        :return: 是否成功
        """
        try:
            with db_manager.get_session() as session:
                # 检查是否已存在当日记录
                existing = session.query(Worth).filter(
                    Worth.date == worth_data.date
                ).first()

                if existing:
                    # 更新现有记录
                    existing.worths = worth_data.net_worth
                    existing.totalAmount = worth_data.total_amount
                    existing.totalShare = worth_data.total_share
                    logger.info("更新现有净值记录")
                else:
                    # 创建新记录
                    new_worth = Worth(
                        date=worth_data.date,
                        worths=worth_data.net_worth,
                        totalAmount=worth_data.total_amount,
                        totalShare=worth_data.total_share
                    )
                    session.add(new_worth)
                    logger.info("创建新净值记录")

                session.commit()

            logger.info("净值数据保存成功")
            return True

        except Exception as e:
            logger.error(f"保存净值数据失败: {e}")
            return False

    def print_worth_summary(self, worth_data: WorthData, total_value: float):
        """
        打印净值摘要

        :param worth_data: 净值数据
        :param total_value: 总市值
        """
        try:
            logger.info("=" * 50)
            logger.info(f"📊 净值计算摘要 - {worth_data.date}")
            logger.info("=" * 50)
            logger.info(f"💰 总市值: {total_value:,.2f}")
            logger.info(f"💱 申购赎回: {worth_data.turnover:,.2f}")
            logger.info(f"📈 净值: {worth_data.net_worth:.4f}")
            logger.info(f"💼 总金额: {worth_data.total_amount:,.2f}")
            logger.info(f"📊 总份额: {worth_data.total_share:,.2f}")
            if worth_data.return_rate != 0:
                logger.info(f"📈 日收益率: {worth_data.return_rate:+.4f}%")
            logger.info("=" * 50)

        except Exception as e:
            logger.error(f"打印净值摘要失败: {e}")

    @monitor_performance
    def calculate_daily_worth(self, target_date: str = None) -> bool:
        """
        计算每日净值

        :param target_date: 目标日期
        :return: 是否成功
        """
        try:
            if not target_date:
                target_date = time.strftime('%Y%m%d', time.localtime())

            target_date_formatted = time.strftime('%Y-%m-%d', time.localtime())

            logger.info(f"开始计算每日净值: {target_date}")

            # 检查是否为交易日
            if not is_trade_date(target_date):
                logger.warning(f"日期 {target_date} 不是交易日，跳过净值计算")
                return True

            # 获取持仓数据
            positions = self.get_position_data()
            if not positions:
                logger.error("获取持仓数据失败")
                return False

            # 计算总市值
            total_value = sum(p.market_value for p in positions)

            # 获取最近净值记录
            last_worth = self.get_last_worth(target_date_formatted)

            # 获取申购赎回数据
            inouts = self.get_today_inouts(target_date_formatted)
            turnover = self.calculate_turnover(inouts)

            # 计算净值
            worth_data = self.calculate_net_worth(total_value, last_worth, turnover)

            # 保存净值数据
            success = self.save_worth_data(worth_data)

            if success:
                # 打印摘要
                self.print_worth_summary(worth_data, total_value)
                logger.info("✅ 每日净值计算完成")
            else:
                logger.error("❌ 每日净值计算失败")

            return success

        except Exception as e:
            logger.error(f"计算每日净值失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def calculate_worths(target_date: str = None) -> bool:
    """净值计算 - 优化版本"""
    with WorthCalculator() as calculator:
        return calculator.calculate_daily_worth(target_date)

@monitor_performance
def main():
    """
    主函数 - 执行净值计算系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始净值计算系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="净值计算系统 (优化版本)")
        arg_parser.add_argument("-d", "--date", required=False, type=str,
                               help="目标日期 YYYYMMDD，默认为今日")
        args = arg_parser.parse_args()

        target_date = args.date or time.strftime('%Y%m%d', time.localtime())

        logger.info(f"执行参数: 目标日期={target_date}")

        # 执行净值计算
        success = calculate_worths(target_date)

        # 更新监控状态
        if success:
            quant_monitor(30, True)
            logger.info("✅ 净值计算系统执行完成")
        else:
            quant_monitor(30, False)
            logger.error("❌ 净值计算系统执行失败")

        return success

    except Exception as e:
        logger.error(f"净值计算系统执行失败: {e}")
        quant_monitor(30, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)

