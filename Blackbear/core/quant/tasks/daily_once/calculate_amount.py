# -*- coding: UTF-8 -*-
"""
板块成交量计算系统 (优化版本)

0 0 21  * * 1-5

主要功能:
1. 计算一级板块每日成交量统计
2. 计算二级板块每日成交量统计
3. 板块资金流向分析
4. 板块活跃度监控
5. 板块涨跌幅统计

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import argparse
import time
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Amount, Source

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 数据获取
from db.fetch import is_trade_date, quant_monitor

@dataclass
class SectorAmount:
    """板块成交量数据"""
    date: str
    first_level_id: int
    second_level_id: Optional[int] = None
    sector_name: str = ""
    total_amount: float = 0.0
    avg_change: float = 0.0
    stock_count: int = 0

class AmountCalculator:
    """板块成交量计算器"""

    def __init__(self):
        self.settings = get_settings()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def get_first_level_sector_data(self, target_date: str) -> List[SectorAmount]:
        """
        获取一级板块成交量数据

        :param target_date: 目标日期
        :return: 一级板块数据列表
        """
        try:
            logger.info(f"获取一级板块成交量数据: {target_date}")

            sql = '''
                SELECT
                    c.name,
                    a.firstLevelId,
                    SUM(b.f6) as sum,
                    COUNT(b.f6) as count,
                    AVG(b.f3) as avg
                FROM stock a
                JOIN source b ON b.code = a.code
                JOIN sortFir c ON a.firstLevelId = c.id
                GROUP BY a.firstLevelId
                ORDER BY sum DESC
            '''

            sector_data = []
            with engine.connect() as conn:
                result = conn.execute(sql)

                for row in result:
                    sector_amount = SectorAmount(
                        date=target_date,
                        first_level_id=int(row['firstLevelId']),
                        sector_name=str(row['name']),
                        total_amount=float(row['sum']) if row['sum'] else 0.0,
                        avg_change=float(row['avg']) if row['avg'] else 0.0,
                        stock_count=int(row['count']) if row['count'] else 0
                    )
                    sector_data.append(sector_amount)

            logger.info(f"获取一级板块数据完成: {len(sector_data)} 个板块")
            return sector_data

        except Exception as e:
            logger.error(f"获取一级板块数据失败: {e}")
            return []

    def get_second_level_sector_data(self, target_date: str) -> List[SectorAmount]:
        """
        获取二级板块成交量数据

        :param target_date: 目标日期
        :return: 二级板块数据列表
        """
        try:
            logger.info(f"获取二级板块成交量数据: {target_date}")

            sql = '''
                SELECT
                    c.name,
                    a.firstLevelId,
                    a.secondLevelId,
                    SUM(b.f6) as sum,
                    COUNT(b.f6) as count,
                    AVG(b.f3) as avg
                FROM stock a
                JOIN source b ON b.code = a.code
                JOIN sortSec c ON a.secondLevelId = c.id
                GROUP BY a.secondLevelId
                ORDER BY sum DESC
            '''

            sector_data = []
            with engine.connect() as conn:
                result = conn.execute(sql)

                for row in result:
                    sector_amount = SectorAmount(
                        date=target_date,
                        first_level_id=int(row['firstLevelId']),
                        second_level_id=int(row['secondLevelId']),
                        sector_name=str(row['name']),
                        total_amount=float(row['sum']) if row['sum'] else 0.0,
                        avg_change=float(row['avg']) if row['avg'] else 0.0,
                        stock_count=int(row['count']) if row['count'] else 0
                    )
                    sector_data.append(sector_amount)

            logger.info(f"获取二级板块数据完成: {len(sector_data)} 个板块")
            return sector_data

        except Exception as e:
            logger.error(f"获取二级板块数据失败: {e}")
            return []

    def save_sector_amounts(self, sector_data: List[SectorAmount]) -> bool:
        """
        保存板块成交量数据

        :param sector_data: 板块数据列表
        :return: 是否成功
        """
        try:
            if not sector_data:
                logger.warning("没有板块数据需要保存")
                return True

            with db_manager.get_session() as session:
                success_count = 0

                for sector in sector_data:
                    try:
                        amount = Amount(
                            date=sector.date,
                            firstLevelId=sector.first_level_id,
                            secondLevelId=sector.second_level_id,
                            sum=sector.total_amount,
                            avg=sector.avg_change,
                            count=sector.stock_count
                        )
                        session.merge(amount)
                        success_count += 1

                    except Exception as e:
                        logger.error(f"保存板块 {sector.sector_name} 数据失败: {e}")
                        continue

                session.commit()

            logger.info(f"板块成交量数据保存完成: 成功 {success_count}/{len(sector_data)} 个板块")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存板块成交量数据失败: {e}")
            return False

    def get_sector_summary(self, sector_data: List[SectorAmount]) -> Dict[str, Any]:
        """
        获取板块统计摘要

        :param sector_data: 板块数据列表
        :return: 统计摘要
        """
        try:
            if not sector_data:
                return {}

            total_amount = sum(s.total_amount for s in sector_data)
            total_stocks = sum(s.stock_count for s in sector_data)
            avg_change = sum(s.avg_change for s in sector_data) / len(sector_data)

            # 找出最活跃的板块
            most_active = max(sector_data, key=lambda x: x.total_amount)

            # 找出涨幅最大的板块
            best_performer = max(sector_data, key=lambda x: x.avg_change)

            summary = {
                'total_sectors': len(sector_data),
                'total_amount': total_amount,
                'total_stocks': total_stocks,
                'avg_change': avg_change,
                'most_active_sector': {
                    'name': most_active.sector_name,
                    'amount': most_active.total_amount
                },
                'best_performer': {
                    'name': best_performer.sector_name,
                    'change': best_performer.avg_change
                }
            }

            return summary

        except Exception as e:
            logger.error(f"计算板块统计摘要失败: {e}")
            return {}

    @monitor_performance
    def calculate_sector_amounts(self, target_date: str = None) -> bool:
        """
        计算板块成交量

        :param target_date: 目标日期
        :return: 是否成功
        """
        try:
            if not target_date:
                target_date = time.strftime('%Y%m%d', time.localtime())

            logger.info(f"开始计算板块成交量: {target_date}")

            # 检查是否为交易日
            if not is_trade_date(target_date):
                logger.warning(f"日期 {target_date} 不是交易日，跳过计算")
                return True

            # 获取一级板块数据
            first_level_data = self.get_first_level_sector_data(target_date)
            if not first_level_data:
                logger.error("获取一级板块数据失败")
                return False

            # 获取二级板块数据
            second_level_data = self.get_second_level_sector_data(target_date)
            if not second_level_data:
                logger.error("获取二级板块数据失败")
                return False

            # 保存数据
            all_data = first_level_data + second_level_data
            success = self.save_sector_amounts(all_data)

            if success:
                # 生成统计摘要
                first_summary = self.get_sector_summary(first_level_data)
                second_summary = self.get_sector_summary(second_level_data)

                logger.info(f"✅ 板块成交量计算完成:")
                logger.info(f"  一级板块: {first_summary.get('total_sectors', 0)} 个, "
                           f"总成交额: {first_summary.get('total_amount', 0):.2e}")
                logger.info(f"  二级板块: {second_summary.get('total_sectors', 0)} 个, "
                           f"总成交额: {second_summary.get('total_amount', 0):.2e}")

                if first_summary.get('most_active_sector'):
                    logger.info(f"  最活跃一级板块: {first_summary['most_active_sector']['name']}")
                if second_summary.get('most_active_sector'):
                    logger.info(f"  最活跃二级板块: {second_summary['most_active_sector']['name']}")
            else:
                logger.error("❌ 板块成交量计算失败")

            return success

        except Exception as e:
            logger.error(f"计算板块成交量失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def calculate_amount(target_date: str = None) -> bool:
    """板块成交量计算 - 优化版本"""
    with AmountCalculator() as calculator:
        return calculator.calculate_sector_amounts(target_date)

@monitor_performance
def main():
    """
    主函数 - 执行板块成交量计算系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始板块成交量计算系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="板块成交量计算系统 (优化版本)")
        arg_parser.add_argument("-d", "--date", required=False, type=str,
                               help="目标日期 YYYYMMDD，默认为今日")
        args = arg_parser.parse_args()

        target_date = args.date or time.strftime('%Y%m%d', time.localtime())

        logger.info(f"执行参数: 目标日期={target_date}")

        # 执行板块成交量计算
        success = calculate_amount(target_date)

        # 更新监控状态
        if success:
            quant_monitor(31, True)
            logger.info("✅ 板块成交量计算系统执行完成")
        else:
            quant_monitor(31, False)
            logger.error("❌ 板块成交量计算系统执行失败")

        return success

    except Exception as e:
        logger.error(f"板块成交量计算系统执行失败: {e}")
        quant_monitor(31, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)