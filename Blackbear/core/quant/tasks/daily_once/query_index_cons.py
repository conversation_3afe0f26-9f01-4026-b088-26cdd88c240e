# -*- coding: UTF-8 -*-
"""
指数成分股查询系统 (优化版本)

0 0 23 * * 1-5

主要功能:
1. 获取各大指数成分股信息
2. 股票标签管理和更新
3. 基金重仓股识别
4. 专精特新企业标记
5. 成分股变动跟踪

优化特性:
- 完善的错误处理和日志记录
- 性能监控和批量处理
- 数据验证和清理
- 模块化设计和类型提示
- 资源管理和连接池
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import argparse
import os
from typing import List, Dict, Optional, Set
from dataclasses import dataclass

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock

# 工具类
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler, 
    register_cleanup, 
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from utils.helper import check_bool
from config.settings import get_settings

# 第三方库
import akshare as ak
import pandas as pd

# 数据获取
from db.fetch import quant_monitor

@dataclass
class IndexConfig:
    """指数配置"""
    name: str
    symbol: str
    type: int  # 1: 中证指数, 2: 其他指数
    description: str = ""

@dataclass
class IndexConstituent:
    """指数成分股"""
    index_name: str
    stock_code: str
    stock_name: str
    weight: float = 0.0

class IndexConsProcessor:
    """指数成分股处理器"""

    def __init__(self):
        self.settings = get_settings()

        # 指数配置
        self.index_configs = [
            IndexConfig('中证A50', '930050', 1, '中证A50指数'),
            IndexConfig('中证A100', '000903', 1, '中证A100指数'),
            IndexConfig('中证A500', '000510', 1, '中证A500指数'),
            IndexConfig('上证50', '000016', 1, '上证50指数'),
            IndexConfig('沪深300', '000300', 1, '沪深300指数'),
            IndexConfig('创业板50', '399673', 2, '创业板50指数'),
            IndexConfig('科创50', '000688', 1, '科创50指数'),
            IndexConfig('中证500', '000905', 1, '中证500指数'),
            IndexConfig('中证1000', '000852', 1, '中证1000指数'),
            IndexConfig('中证2000', '932000', 1, '中证2000指数'),
            IndexConfig('大盘成长', '399372', 2, '大盘成长指数'),
            IndexConfig('大盘价值', '399373', 2, '大盘价值指数'),
            IndexConfig('中盘成长', '399374', 2, '中盘成长指数'),
            IndexConfig('中盘价值', '399375', 2, '中盘价值指数'),
            IndexConfig('小盘成长', '399376', 2, '小盘成长指数'),
            IndexConfig('小盘价值', '399377', 2, '小盘价值指数'),
        ]

        # 基金重仓阈值 (10亿元)
        self.fund_threshold = 1000000000

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源"""
        pass

    def delete_tag(self, tag: str) -> bool:
        """
        删除指定标签

        :param tag: 要删除的标签
        :return: 是否成功
        """
        try:
            logger.info(f"删除标签: {tag}")

            with db_manager.get_session() as session:
                stocks = session.query(Stock).filter(Stock.tags.contains(tag)).all()

                update_count = 0
                for stock in stocks:
                    try:
                        if not stock.tags:
                            continue

                        tag_arr = stock.tags.split(',')
                        if tag in tag_arr:
                            tag_arr.remove(tag)
                            stock.tags = ','.join(tag_arr) if tag_arr else None
                            update_count += 1
                    except Exception as e:
                        logger.error(f"删除股票 {stock.code} 标签失败: {e}")
                        continue

                session.commit()

            logger.info(f"标签 {tag} 删除完成: 更新 {update_count} 只股票")
            return True

        except Exception as e:
            logger.error(f"删除标签 {tag} 失败: {e}")
            return False

    def get_index_constituents_csindex(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        从中证指数网获取成分股数据

        :param symbol: 指数代码
        :return: 成分股DataFrame
        """
        try:
            logger.debug(f"从中证指数网获取成分股: {symbol}")

            df = ak.index_stock_cons_csindex(symbol=symbol)

            if df.empty:
                logger.warning(f"指数 {symbol} 成分股数据为空")
                return None

            logger.debug(f"指数 {symbol} 成分股数据: {len(df)} 只股票")
            return df

        except Exception as e:
            logger.error(f"获取指数 {symbol} 成分股数据失败: {e}")
            return None

    def get_index_constituents_other(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        从其他数据源获取成分股数据

        :param symbol: 指数代码
        :return: 成分股DataFrame
        """
        try:
            logger.debug(f"从其他数据源获取成分股: {symbol}")

            df = ak.index_stock_cons(symbol=symbol)

            if df.empty:
                logger.warning(f"指数 {symbol} 成分股数据为空")
                return None

            logger.debug(f"指数 {symbol} 成分股数据: {len(df)} 只股票")
            return df

        except Exception as e:
            logger.error(f"获取指数 {symbol} 成分股数据失败: {e}")
            return None

    def update_stock_tags(self, stock_code: str, tag: str) -> bool:
        """
        更新股票标签

        :param stock_code: 股票代码
        :param tag: 标签
        :return: 是否成功
        """
        try:
            with db_manager.get_session() as session:
                stock = session.query(Stock).get(str(stock_code))

                if not stock:
                    logger.warning(f"股票 {stock_code} 不存在")
                    return False

                # 获取现有标签
                existing_tags = stock.tags.split(',') if stock.tags else []

                # 添加新标签 (去重)
                tags = list(set(existing_tags + [tag]))

                # 更新标签
                stock.tags = ','.join(tags)
                session.commit()

                return True

        except Exception as e:
            logger.error(f"更新股票 {stock_code} 标签失败: {e}")
            return False

    @monitor_performance
    def process_index_constituents(self, config: IndexConfig) -> bool:
        """
        处理指数成分股

        :param config: 指数配置
        :return: 是否成功
        """
        try:
            logger.info(f"开始处理指数成分股: {config.name} ({config.symbol})")

            # 删除旧标签
            self.delete_tag(config.name)

            # 获取成分股数据
            if config.type == 1:
                df = self.get_index_constituents_csindex(config.symbol)
                code_column = '成分券代码'
            else:
                df = self.get_index_constituents_other(config.symbol)
                code_column = '品种代码'

            if df is None:
                logger.error(f"指数 {config.name} 成分股数据获取失败")
                return False

            # 更新股票标签
            success_count = 0
            for _, row in df.iterrows():
                try:
                    stock_code = str(row[code_column])
                    if self.update_stock_tags(stock_code, config.name):
                        success_count += 1
                except Exception as e:
                    logger.error(f"处理成分股 {row.get(code_column, 'unknown')} 失败: {e}")
                    continue

            logger.info(f"✅ 指数 {config.name} 成分股处理完成: 成功 {success_count}/{len(df)} 只股票")
            return success_count > 0

        except Exception as e:
            logger.error(f"处理指数 {config.name} 成分股失败: {e}")
            return False

    def get_fund_heavy_stocks(self) -> List[str]:
        """
        获取基金重仓股

        :return: 基金重仓股代码列表
        """
        try:
            logger.info("获取基金重仓股")

            sql = '''
                SELECT code, SUM(amount) as total
                FROM fundstock
                GROUP BY code
                HAVING total > %s
                ORDER BY total DESC
            '''

            with engine.connect() as conn:
                result = conn.execute(sql, (self.fund_threshold,))
                heavy_stocks = [str(row['code']) for row in result if row['total']]

            logger.info(f"找到 {len(heavy_stocks)} 只基金重仓股")
            return heavy_stocks

        except Exception as e:
            logger.error(f"获取基金重仓股失败: {e}")
            return []

    def process_fund_heavy_stocks(self) -> bool:
        """
        处理基金重仓股标签

        :return: 是否成功
        """
        try:
            logger.info("开始处理基金重仓股标签")

            # 删除旧标签
            self.delete_tag('基金重仓')

            # 获取基金重仓股
            heavy_stocks = self.get_fund_heavy_stocks()

            if not heavy_stocks:
                logger.warning("没有找到基金重仓股")
                return True

            # 更新标签
            success_count = 0
            for stock_code in heavy_stocks:
                if self.update_stock_tags(stock_code, '基金重仓'):
                    success_count += 1

            logger.info(f"✅ 基金重仓股标签处理完成: 成功 {success_count}/{len(heavy_stocks)} 只股票")
            return success_count > 0

        except Exception as e:
            logger.error(f"处理基金重仓股标签失败: {e}")
            return False

    def process_special_stocks_from_file(self, file_path: str, tag: str) -> bool:
        """
        从文件处理特殊股票标签

        :param file_path: 文件路径
        :param tag: 标签名称
        :return: 是否成功
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"文件 {file_path} 不存在，跳过处理")
                return True

            logger.info(f"从文件 {file_path} 处理 {tag} 标签")

            # 删除旧标签
            self.delete_tag(tag)

            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                stock_codes = [line.strip() for line in f if line.strip()]

            if not stock_codes:
                logger.warning(f"文件 {file_path} 为空")
                return True

            # 更新标签
            success_count = 0
            for stock_code in stock_codes:
                if self.update_stock_tags(stock_code, tag):
                    success_count += 1

            logger.info(f"✅ {tag} 标签处理完成: 成功 {success_count}/{len(stock_codes)} 只股票")
            return success_count > 0

        except Exception as e:
            logger.error(f"从文件处理 {tag} 标签失败: {e}")
            return False

    @monitor_performance
    def process_all_index_constituents(self, include_fund_heavy: bool = True,
                                     special_file: str = None) -> bool:
        """
        处理所有指数成分股

        :param include_fund_heavy: 是否包含基金重仓股
        :param special_file: 特殊股票文件路径
        :return: 是否成功
        """
        try:
            logger.info("开始处理所有指数成分股")

            success_count = 0
            total_count = len(self.index_configs)

            # 处理指数成分股
            for config in self.index_configs:
                if self.process_index_constituents(config):
                    success_count += 1

            # 处理基金重仓股
            if include_fund_heavy:
                if self.process_fund_heavy_stocks():
                    success_count += 1
                total_count += 1

            # 处理特殊股票文件
            if special_file:
                if self.process_special_stocks_from_file(special_file, '专精特新'):
                    success_count += 1
                total_count += 1

            logger.info(f"指数成分股处理完成: 成功 {success_count}/{total_count} 个任务")
            return success_count > 0

        except Exception as e:
            logger.error(f"处理所有指数成分股失败: {e}")
            return False

# 兼容性函数 - 保持原有接口
def query_index_cons(include_fund_heavy: bool = True, special_file: str = None) -> bool:
    """指数成分股查询 - 优化版本"""
    with IndexConsProcessor() as processor:
        return processor.process_all_index_constituents(include_fund_heavy, special_file)

@monitor_performance
def main():
    """
    主函数 - 执行指数成分股查询系统 (优化版本)

    :return: 是否成功
    """
    try:
        logger.info("🚀 开始指数成分股查询系统 (优化版本)")

        # 解析命令行参数
        arg_parser = argparse.ArgumentParser(description="指数成分股查询系统 (优化版本)")
        arg_parser.add_argument("-f", "--fund", required=False, type=check_bool,
                               nargs='?', const=True, default=True,
                               help="是否包含基金重仓股处理")
        arg_parser.add_argument("-s", "--special", required=False, type=str,
                               help="特殊股票文件路径 (如专精特新)")
        args = arg_parser.parse_args()

        include_fund_heavy = args.fund
        special_file = args.special

        logger.info(f"执行参数: 基金重仓股={include_fund_heavy}, 特殊文件={special_file}")

        # 执行指数成分股查询
        success = query_index_cons(include_fund_heavy, special_file)

        # 更新监控状态
        if success:
            quant_monitor(29, True)
            logger.info("✅ 指数成分股查询系统执行完成")
        else:
            quant_monitor(29, False)
            logger.error("❌ 指数成分股查询系统执行失败")

        return success

    except Exception as e:
        logger.error(f"指数成分股查询系统执行失败: {e}")
        quant_monitor(29, False)
        return False

if __name__ == '__main__':
    # 设置信号处理器
    setup_signal_handler()

    try:
        # 执行主函数
        success = main()

        # 根据执行结果退出
        exit_code = 0 if success else 1
        logger.info(f"脚本执行完成，退出码: {exit_code}")

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        exit_code = 130  # 标准的SIGINT退出码

    except Exception as e:
        logger.error(f"脚本执行异常: {e}")
        exit_code = 1

    finally:
        # 清理并退出
        cleanup_and_exit(exit_code)