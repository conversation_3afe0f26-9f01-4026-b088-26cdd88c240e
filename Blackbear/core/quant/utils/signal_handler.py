#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
公共信号处理模块

提供统一的信号处理功能，支持优雅退出和资源清理
"""

import signal
import threading
import sys
from typing import Callable, Optional, List
from utils.logger import logger

class SignalHandler:
    """信号处理器类"""
    
    def __init__(self):
        self._exit_flag = threading.Event()
        self._cleanup_handlers: List[Callable] = []
        self._original_handlers = {}
        
    def register_cleanup_handler(self, handler: Callable):
        """注册清理处理器"""
        self._cleanup_handlers.append(handler)
        
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        logger.info(f"收到信号 {signum}，准备退出程序...")
        self._exit_flag.set()
        # 执行清理操作后正常退出，而不是强制退出
        try:
            self.cleanup()
        except Exception as e:
            logger.error(f"清理失败: {e}")
        finally:
            sys.exit(0)  # 使用正常退出而不是os._exit()
        
    def is_exit_requested(self) -> bool:
        """检查是否请求退出"""
        return self._exit_flag.is_set()
        
    def wait_for_exit(self, timeout: Optional[float] = None) -> bool:
        """等待退出信号"""
        return self._exit_flag.wait(timeout)
        
    def setup(self):
        """设置信号处理器"""
        try:
            # 保存原始处理器
            self._original_handlers[signal.SIGINT] = signal.signal(signal.SIGINT, self.signal_handler)
            self._original_handlers[signal.SIGTERM] = signal.signal(signal.SIGTERM, self.signal_handler)
            logger.debug("信号处理器设置完成")
        except Exception as e:
            logger.warning(f"设置信号处理器失败: {e}")
            
    def cleanup(self):
        """执行清理操作"""
        try:
            logger.info("开始执行清理操作...")
            
            # 执行所有注册的清理处理器
            for handler in self._cleanup_handlers:
                try:
                    handler()
                except Exception as e:
                    logger.error(f"执行清理处理器失败: {e}")
                    
            # 恢复原始信号处理器
            for signum, original_handler in self._original_handlers.items():
                try:
                    signal.signal(signum, original_handler)
                except Exception as e:
                    logger.warning(f"恢复信号处理器失败: {e}")
                    
            logger.info("清理操作完成")
            
        except Exception as e:
            logger.error(f"清理操作失败: {e}")
            
    def reset(self):
        """重置退出标志"""
        self._exit_flag.clear()

# 全局信号处理器实例
signal_handler = SignalHandler()

def setup_signal_handler():
    """设置全局信号处理器"""
    signal_handler.setup()

def register_cleanup(handler: Callable):
    """注册清理处理器"""
    signal_handler.register_cleanup_handler(handler)

def is_exit_requested() -> bool:
    """检查是否请求退出"""
    return signal_handler.is_exit_requested()

def wait_for_exit(timeout: Optional[float] = None) -> bool:
    """等待退出信号"""
    return signal_handler.wait_for_exit(timeout)

def cleanup_and_exit(exit_code: int = 0):
    """清理并退出"""
    try:
        signal_handler.cleanup()
    except Exception as e:
        logger.error(f"清理失败: {e}")
    finally:
        # 确保进程能够正常退出
        try:
            sys.exit(exit_code)
        except SystemExit:
            # 如果sys.exit()被拦截，使用os._exit()强制退出
            import os
            os._exit(exit_code)

# 便捷的上下文管理器
class SignalContext:
    """信号处理上下文管理器"""
    
    def __init__(self, cleanup_handlers: Optional[List[Callable]] = None):
        self.cleanup_handlers = cleanup_handlers or []
        
    def __enter__(self):
        setup_signal_handler()
        for handler in self.cleanup_handlers:
            register_cleanup(handler)
        return signal_handler
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        cleanup_and_exit(1 if exc_type else 0)

# 装饰器：自动处理信号
def with_signal_handling(cleanup_handlers: Optional[List[Callable]] = None):
    """装饰器：为函数添加信号处理"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with SignalContext(cleanup_handlers):
                return func(*args, **kwargs)
        return wrapper
    return decorator

# 便捷函数：检查退出标志
def check_exit_flag():
    """检查退出标志，如果请求退出则抛出异常"""
    if is_exit_requested():
        raise KeyboardInterrupt("收到退出信号")

# 便捷函数：带超时的等待
def wait_with_timeout(timeout: float, check_interval: float = 0.1):
    """带超时的等待，定期检查退出标志"""
    import time
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if is_exit_requested():
            return True
        time.sleep(check_interval)
    
    return False 