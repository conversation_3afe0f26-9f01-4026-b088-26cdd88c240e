# -*- coding: utf-8 -*-
"""
BaoStock数据源接口 - 优化版本

主要功能:
1. 股票历史数据查询
2. 财务数据查询
3. 成长性数据查询
4. 估值数据查询
5. 盈利能力数据查询
6. 数据清洗和格式化

优化特性:
- 完善的错误处理和重试机制
- 自动登录和连接管理
- 数据质量验证
- 统一的数据格式
- 资源自动清理
"""

import time
import threading
from typing import Optional, Dict, List, Any, Union
from datetime import datetime
import pandas as pd
import baostock as bs

from utils.logger import logger
from utils.performance import monitor_performance
from utils.helper import sz_or_sh, safe_float, safe_int, validate_date_format


class BaostockDataSource:
    """BaoStock数据源类 - 优化版本"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, auto_login: bool = True):
        """单例模式，确保全局只有一个BaoStock实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(BaostockDataSource, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, auto_login: bool = True):
        # 避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.is_logged_in = False
        self.login_info = None
        self._lock = threading.Lock()  # 实例级别的锁，用于线程安全
        self._initialized = True

        if auto_login:
            self.login()

    def __enter__(self):
        if not self.is_logged_in:
            self.login()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """自动登出"""
        self.logout()

    def login(self, max_retries: int = 3) -> bool:
        """登录BaoStock，带重试机制和线程安全"""
        with self._lock:
            # 如果已经登录，直接返回
            if self.is_logged_in:
                return True

            for attempt in range(max_retries):
                try:
                    # 先尝试登出，确保清理之前的连接
                    try:
                        bs.logout()
                    except:
                        pass

                    # 短暂延迟，避免频繁重连
                    if attempt > 0:
                        time.sleep(1)

                    self.login_info = bs.login()

                    if self.login_info.error_code == '0':
                        self.is_logged_in = True
                        logger.info(f"BaoStock登录成功 (尝试 {attempt + 1}/{max_retries})")
                        return True
                    else:
                        logger.warning(f"BaoStock登录失败 (尝试 {attempt + 1}/{max_retries}): {self.login_info.error_msg}")
                        if attempt == max_retries - 1:
                            logger.error(f"BaoStock登录最终失败: {self.login_info.error_msg}")

                except Exception as e:
                    logger.warning(f"BaoStock登录异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt == max_retries - 1:
                        logger.error(f"BaoStock登录最终异常: {e}")

            self.is_logged_in = False
            return False

    def logout(self):
        """登出BaoStock，线程安全"""
        with self._lock:
            try:
                if self.is_logged_in:
                    bs.logout()
                    self.is_logged_in = False
                    logger.info("BaoStock登出成功")
            except Exception as e:
                logger.warning(f"BaoStock登出异常: {e}")
                # 即使登出异常，也要重置状态
                self.is_logged_in = False

    def _ensure_logged_in(self):
        """确保已登录，包含重连机制"""
        # 首先检查本地登录状态
        if not self.is_logged_in:
            if not self.login():
                raise ConnectionError("无法连接到BaoStock服务")
            return

        # 即使本地状态显示已登录，也要验证连接是否有效
        # 通过一个简单的查询来验证连接状态
        try:
            # 使用一个轻量级的查询来测试连接
            test_rs = bs.query_trade_dates(start_date="2024-01-01", end_date="2024-01-01")
            if test_rs.error_code != '0':
                # 连接无效，需要重新登录
                logger.warning(f"BaoStock连接已断开，尝试重新登录: {test_rs.error_msg}")
                self.is_logged_in = False
                if not self.login():
                    raise ConnectionError("重新连接BaoStock服务失败")
        except Exception as e:
            # 连接测试失败，重新登录
            logger.warning(f"BaoStock连接测试失败，尝试重新登录: {e}")
            self.is_logged_in = False
            if not self.login():
                raise ConnectionError("重新连接BaoStock服务失败")

    def _format_stock_code(self, stock_code: str) -> str:
        """格式化股票代码为BaoStock格式"""
        if not stock_code:
            raise ValueError("股票代码不能为空")

        # 如果已经是完整格式，直接返回
        if '.' in stock_code:
            return stock_code

        # 根据代码判断市场
        market = sz_or_sh(stock_code)
        return f"{market}.{stock_code}"

    def _extract_stock_code(self, full_code: str) -> str:
        """从完整代码中提取股票代码"""
        if '.' in full_code:
            return full_code.split('.')[1]
        return full_code

    @monitor_performance
    def get_stock_history(self, stock_code: str, start_date: str, end_date: str,
                         frequency: str = "d", adjust_flag: str = "2") -> pd.DataFrame:
        """
        获取股票历史数据

        :param stock_code: 股票代码
        :param start_date: 开始日期 YYYY-MM-DD
        :param end_date: 结束日期 YYYY-MM-DD
        :param frequency: 频率 ("d":日, "w":周, "m":月)
        :param adjust_flag: 复权类型 ("1":后复权, "2":前复权, "3":不复权)
        :return: 股票历史数据DataFrame
        """
        try:
            self._ensure_logged_in()

            # 参数验证
            if not validate_date_format(start_date) or not validate_date_format(end_date):
                raise ValueError("无效的日期格式")

            # 格式化股票代码
            formatted_code = self._format_stock_code(stock_code)

            logger.debug(f"获取股票 {stock_code} 历史数据: {start_date} 至 {end_date}")

            # 查询字段
            fields = "date,code,open,high,low,close,volume,amount,turn,pctChg,peTTM,pbMRQ,psTTM,pcfNcfTTM"

            # 执行查询，带重试机制
            max_retries = 2
            for attempt in range(max_retries):
                rs = bs.query_history_k_data_plus(
                    formatted_code,
                    fields,
                    start_date=start_date,
                    end_date=end_date,
                    frequency=frequency,
                    adjustflag=adjust_flag
                )

                if rs.error_code == '0':
                    break
                elif rs.error_msg and "用户未登录" in rs.error_msg:
                    # 登录状态失效，重新登录后重试
                    logger.warning(f"检测到登录状态失效，重新登录后重试 (尝试 {attempt + 1}/{max_retries})")
                    self.is_logged_in = False
                    self._ensure_logged_in()
                    if attempt == max_retries - 1:
                        logger.error(f"查询股票 {stock_code} 历史数据失败: {rs.error_msg}")
                        return pd.DataFrame()
                else:
                    logger.error(f"查询股票 {stock_code} 历史数据失败: {rs.error_msg}")
                    return pd.DataFrame()

            # 收集数据
            data_list = []
            while rs.next():
                data_list.append(rs.get_row_data())

            if not data_list:
                logger.warning(f"股票 {stock_code} 无历史数据")
                return pd.DataFrame()

            # 创建DataFrame
            df = pd.DataFrame(data_list, columns=rs.fields)

            # 数据清洗
            df = self._clean_stock_data(df)

            logger.debug(f"股票 {stock_code} 历史数据获取完成: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 历史数据失败: {e}")
            return pd.DataFrame()

    def _clean_stock_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗股票数据"""
        try:
            if df.empty:
                return df

            # 生成交易日期字段
            df['tradedate'] = df['date'].str.replace('-', '')

            # 提取股票代码
            df['code'] = df['code'].apply(self._extract_stock_code)

            # 数值类型转换
            numeric_columns = [
                'pctChg', 'turn', 'volume', 'amount', 'open', 'high', 'low', 'close',
                'peTTM', 'pcfNcfTTM', 'pbMRQ', 'psTTM'
            ]

            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 删除不需要的列
            columns_to_drop = ['psTTM', 'pcfNcfTTM']
            df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

            # 数值精度处理
            for col in df.select_dtypes(include=['float64']).columns:
                df[col] = df[col].round(4)

            # 数据验证
            df = df.dropna(subset=['date', 'code'])

            return df

        except Exception as e:
            logger.error(f"清洗股票数据失败: {e}")
            return df

    @monitor_performance
    def get_growth_data(self, stock_code: str, year: int, quarter: int) -> List[Dict[str, Any]]:
        """
        获取成长能力数据

        :param stock_code: 股票代码
        :param year: 年份
        :param quarter: 季度 (1,2,3,4)
        :return: 成长能力数据列表
        """
        try:
            self._ensure_logged_in()

            # 格式化股票代码
            formatted_code = self._format_stock_code(stock_code)

            logger.debug(f"获取股票 {stock_code} 成长数据: {year}年第{quarter}季度")

            # 执行查询
            rs = bs.query_growth_data(formatted_code, year, quarter)

            if rs.error_code != '0':
                logger.error(f"查询股票 {stock_code} 成长数据失败: {rs.error_msg}")
                return []

            # 收集数据
            growth_list = []
            while rs.next():
                row_data = rs.get_row_data()
                growth_dict = dict(zip(rs.fields, row_data))
                growth_list.append(growth_dict)

            logger.debug(f"股票 {stock_code} 成长数据获取完成: {len(growth_list)} 条记录")
            return growth_list

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 成长数据失败: {e}")
            return []

    @monitor_performance
    def get_profit_data(self, stock_code: str, year: int, quarter: int) -> List[Dict[str, Any]]:
        """
        获取盈利能力数据

        :param stock_code: 股票代码
        :param year: 年份
        :param quarter: 季度
        :return: 盈利能力数据列表
        """
        try:
            self._ensure_logged_in()

            formatted_code = self._format_stock_code(stock_code)

            logger.debug(f"获取股票 {stock_code} 盈利数据: {year}年第{quarter}季度")

            rs = bs.query_profit_data(formatted_code, year, quarter)

            if rs.error_code != '0':
                logger.error(f"查询股票 {stock_code} 盈利数据失败: {rs.error_msg}")
                return []

            profit_list = []
            while rs.next():
                row_data = rs.get_row_data()
                profit_dict = dict(zip(rs.fields, row_data))
                profit_list.append(profit_dict)

            logger.debug(f"股票 {stock_code} 盈利数据获取完成: {len(profit_list)} 条记录")
            return profit_list

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 盈利数据失败: {e}")
            return []

    @monitor_performance
    def get_operation_data(self, stock_code: str, year: int, quarter: int) -> List[Dict[str, Any]]:
        """
        获取营运能力数据

        :param stock_code: 股票代码
        :param year: 年份
        :param quarter: 季度
        :return: 营运能力数据列表
        """
        try:
            self._ensure_logged_in()

            formatted_code = self._format_stock_code(stock_code)

            logger.debug(f"获取股票 {stock_code} 营运数据: {year}年第{quarter}季度")

            rs = bs.query_operation_data(formatted_code, year, quarter)

            if rs.error_code != '0':
                logger.error(f"查询股票 {stock_code} 营运数据失败: {rs.error_msg}")
                return []

            operation_list = []
            while rs.next():
                row_data = rs.get_row_data()
                operation_dict = dict(zip(rs.fields, row_data))
                operation_list.append(operation_dict)

            logger.debug(f"股票 {stock_code} 营运数据获取完成: {len(operation_list)} 条记录")
            return operation_list

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 营运数据失败: {e}")
            return []

    @monitor_performance
    def get_balance_data(self, stock_code: str, year: int, quarter: int) -> List[Dict[str, Any]]:
        """
        获取偿债能力数据

        :param stock_code: 股票代码
        :param year: 年份
        :param quarter: 季度
        :return: 偿债能力数据列表
        """
        try:
            self._ensure_logged_in()

            formatted_code = self._format_stock_code(stock_code)

            logger.debug(f"获取股票 {stock_code} 偿债数据: {year}年第{quarter}季度")

            rs = bs.query_balance_data(formatted_code, year, quarter)

            if rs.error_code != '0':
                logger.error(f"查询股票 {stock_code} 偿债数据失败: {rs.error_msg}")
                return []

            balance_list = []
            while rs.next():
                row_data = rs.get_row_data()
                balance_dict = dict(zip(rs.fields, row_data))
                balance_list.append(balance_dict)

            logger.debug(f"股票 {stock_code} 偿债数据获取完成: {len(balance_list)} 条记录")
            return balance_list

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 偿债数据失败: {e}")
            return []

    @monitor_performance
    def get_cash_flow_data(self, stock_code: str, year: int, quarter: int) -> List[Dict[str, Any]]:
        """
        获取现金流量数据

        :param stock_code: 股票代码
        :param year: 年份
        :param quarter: 季度
        :return: 现金流量数据列表
        """
        try:
            self._ensure_logged_in()

            formatted_code = self._format_stock_code(stock_code)

            logger.debug(f"获取股票 {stock_code} 现金流数据: {year}年第{quarter}季度")

            rs = bs.query_cash_flow_data(formatted_code, year, quarter)

            if rs.error_code != '0':
                logger.error(f"查询股票 {stock_code} 现金流数据失败: {rs.error_msg}")
                return []

            cash_flow_list = []
            while rs.next():
                row_data = rs.get_row_data()
                cash_flow_dict = dict(zip(rs.fields, row_data))
                cash_flow_list.append(cash_flow_dict)

            logger.debug(f"股票 {stock_code} 现金流数据获取完成: {len(cash_flow_list)} 条记录")
            return cash_flow_list

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 现金流数据失败: {e}")
            return []


# 全局实例管理
_global_baostock_instance = None
_global_lock = threading.Lock()

def get_baostock_instance() -> BaostockDataSource:
    """获取全局BaoStock实例"""
    global _global_baostock_instance
    if _global_baostock_instance is None:
        with _global_lock:
            if _global_baostock_instance is None:
                _global_baostock_instance = BaostockDataSource(auto_login=True)
                logger.info('全局BaoStock实例初始化完成')
    return _global_baostock_instance

# 兼容性类名
class Baostock(BaostockDataSource):
    """兼容性类，保持原有接口"""

    def __init__(self):
        # 使用全局实例，避免多实例问题
        global _global_baostock_instance
        if _global_baostock_instance is None:
            super().__init__(auto_login=True)
            _global_baostock_instance = self
            logger.info('BaoStock量化数据源初始化')
        else:
            # 如果全局实例已存在，复制其状态
            self.__dict__ = _global_baostock_instance.__dict__

    def query_history_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """兼容性方法"""
        return self.get_stock_history(stock_code, start_date, end_date)

    def query_growth_data(self, stock_code: str, year: int, quarter: int) -> List[Dict[str, Any]]:
        """兼容性方法"""
        return self.get_growth_data(stock_code, year, quarter)