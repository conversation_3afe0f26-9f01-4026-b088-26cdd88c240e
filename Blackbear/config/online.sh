#!/bin/bash

# CUSTOMER SETTING
# 使用环境变量 source online.sh

# global
export TZ=Asia/Shanghai
export BB_VERSION=V6.0.0
# export DOCKER_DEFAULT_PLATFORM=linux/amd64

# mysql
export BB_MYSQL_HOST=rm-8vbi3iof9aug3vo89.mysql.zhangbei.rds.aliyuncs.com
export BB_MYSQL_DBNAME=blackbear
export BB_MYSQL_DBNAME2=blackbear-deploy
export BB_MYSQL_USER=mizhdi
export BB_MYSQL_PASSWD=mizdiI38193857OI
export BB_MYSQL_PORT=3306

# redis
export BB_REDIS_HOST=r-8vbgj5rh2xed7oropq.redis.zhangbei.rds.aliyuncs.com
export BB_REDIS_PORT=6379
export BB_REDIS_PASSWORD=mizdiI38193857OI
export BB_REDIS_DB0=0
export BB_WS_REDIS_DB3=3

# 各个项目地址
export BB_PROXY_URL=http://***********:5555
export BB_PROXY_URL2=http://**************:5010
export BB_SCRAPY_URL=http://***********:6800
export BB_TASK_URL=http://***********:5920
export BB_FRONTEND_URL=https://admin.finevent.top
export BB_API_URL=https://api.finevent.top
export BB_WEB_URL=https://finevent.top
export BB_ASSETS_URL=https://assets.finevent.top

# 根据oss切换同 ossHost
export BB_IMAGE_URL=https://image.finevent.top

# crawl
export BB_SCRAPY_PROXY=True
export BB_SCRAPY_DEVELOPMENT=FALSE 

#  --------- backend -------------------
# aliyun oss (0)和minio(1)切换
export BB_UseOSS=0
export BB_BB_OssBucketName=blackbear
# 统一oss地址
export BB_OssHost=$BB_IMAGE_URL
# minio
export BB_MinioEndPoint=**************
export BB_MinioPort=9000
export BB_MinioAccessKey=xabALv8xA6q9HBqB6NbT
export BB_MinioSecretKey=bCSy5VQSrluC0KAkKbLyzNHHldFgU7j9dgBFEiT8

# aliyun oss
export BB_OssAccessKeyId=LTAI5t8GvEDoDmDMWEcks6Yo
export BB_OssAccessKeySecret=******************************
export BB_OssBucket=finevent-images
export BB_OssEndpoint=oss-cn-zhangjiakou.aliyuncs.com

# mail
export BB_MailerHost=smtp.qq.com
export BB_MailerPort=465
export BB_MailerAuthUser=<EMAIL>
export BB_MailerAuthPass=xasyimamhyowecgf

# alinode
export BB_AlinodeAppid=82535
export BB_AlinodeSecret=fe8c9ef71d388154332a14700b347d51b4d74534

# wechat
export BB_WechatApiAppId=wx053f8547616de1ed
export BB_WechatApiAppSecret=72b907c5a7e15f2c5c21e9df5760f5dc

# weibo
export BB_PassportWeiboKey=*********
export BB_PassportWeiboSecret=e0febb7ae543ddae0483299759da5515
export BB_WeiboPassportRedirect=http://$BB_FRONTEND_URL/#/loginsuccess

export BB_BingIndexNowKey=********************************
export BB_SystemUserId=fetadmin-xxxx-oooo
export BB_AdminUsername=admin.top

# alisms
export BB_ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5t8GvEDoDmDMWEcks6Yo
export BB_ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************

export BB_ENABLE_NODE_LOG=YES
export BB_AKToolsHost=http://***********:8888
export BB_CACHE_ENABLED=false

# ==================== 新增优化配置 (Online生产环境) ====================

# JWT配置（生产环境使用强密钥）
export BB_JWT_SECRET=blackbear-production-jwt-secret-key-CHANGE-IN-PRODUCTION-2025

# 安全配置（实际使用的，生产环境最严格）
export BB_ENHANCED_SECURITY_ENABLED=true
export BB_XSS_FILTER_ENABLED=true
export BB_SQL_INJECTION_FILTER_ENABLED=true

# 监控配置（实际使用的，生产环境全面监控）
export BB_APM_ENABLED=true

# 性能配置（实际使用的）
export BB_RESPONSE_COMPRESSION_ENABLED=true
export BB_CACHE_OPTIMIZATION_ENABLED=true

# 日志配置（生产环境减少日志级别）
export BB_LOG_LEVEL=WARN
export BB_CONSOLE_LOG_LEVEL=ERROR

# 限流配置（实际使用的，生产环境严格限流）
export BB_RATE_LIMIT_ENABLED=true
export BB_RATE_LIMIT_MAX=500
export BB_RATE_LIMIT_DURATION=900000

# 生产环境配置
export NODE_ENV=production
export EGG_SERVER_ENV=prod

# 端口配置
export BB_PORT=7001
export BB_HOSTNAME=0.0.0.0

# Redis连接优化（实际使用的）
export BB_REDIS_CONNECT_TIMEOUT=5000
export BB_REDIS_COMMAND_TIMEOUT=3000
export BB_REDIS_RETRY_DELAY=50
export BB_REDIS_MAX_RETRIES=5

# 文件上传配置（生产环境限制更严格）
export BB_UPLOAD_FILE_SIZE=5mb

# CORS配置（实际使用的，生产环境严格控制）
export BB_CORS_ORIGINS="$BB_FRONTEND_URL,$BB_WEB_URL,https://admin.finevent.top,https://finevent.top"

# 会话配置（生产环境安全设置）
export BB_SESSION_MAX_AGE=43200000

# 缓存配置（实际使用的，生产环境更长缓存时间）
export BB_CACHE_DEFAULT_TTL=1800
export BB_CACHE_L1_TTL=300
export BB_CACHE_L2_TTL=1800
export BB_CACHE_L3_TTL=7200

# 性能监控配置
export BB_PERFORMANCE_MONITORING=true
export BB_SLOW_QUERY_THRESHOLD=500
export BB_MEMORY_USAGE_WARNING=0.85

# 安全响应头配置（生产环境全面启用）
export BB_SECURITY_HEADERS_ENABLED=true
export BB_CONTENT_SECURITY_POLICY_ENABLED=true

# IP白名单（生产环境严格控制）
export BB_IP_WHITELIST="***********,*************,127.0.0.1"

# IP黑名单（生产环境）
export BB_IP_BLACKLIST=""

# 调试配置（生产环境关闭调试）
export BB_DEBUG_ENABLED=false
export BB_TRACE_ENABLED=false

echo "✅ Blackbear优化配置已加载 (Online生产环境)"
echo "🔧 环境: production"
echo "🗄️  数据库: $BB_MYSQL_HOST:$BB_MYSQL_PORT/$BB_MYSQL_DBNAME"
echo "📦 Redis: $BB_REDIS_HOST:$BB_REDIS_PORT"
echo "🌐 API地址: $BB_API_URL"
echo "🛡️  安全增强: 已启用 (生产级别)"
echo "📊 监控系统: 已启用"
echo "🔒 安全模式: 已启用"