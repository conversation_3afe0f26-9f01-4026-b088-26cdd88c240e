#!/bin/bash

# 环境切换脚本
# 用于快速切换不同环境的配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Blackbear 环境切换脚本${NC}"
    echo ""
    echo "用法: $0 [环境名称]"
    echo ""
    echo "可用环境:"
    echo -e "  ${GREEN}test${NC}    - 测试环境 (开发测试用)"
    echo -e "  ${GREEN}local${NC}   - 本地环境 (本地开发用)"
    echo -e "  ${GREEN}online${NC}  - 生产环境 (线上部署用)"
    echo ""
    echo "示例:"
    echo "  $0 test     # 切换到测试环境"
    echo "  $0 local    # 切换到本地环境"
    echo "  $0 online   # 切换到生产环境"
    echo ""
    echo "环境特点:"
    echo -e "  ${YELLOW}Test环境${NC}:   平衡配置，适合功能测试"
    echo -e "  ${YELLOW}Local环境${NC}:  宽松配置，便于开发调试"
    echo -e "  ${YELLOW}Online环境${NC}: 严格配置，生产级安全"
}

# 检查环境文件是否存在
check_env_file() {
    local env=$1
    local file="$env.sh"
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ 错误: 环境文件 $file 不存在${NC}"
        exit 1
    fi
}

# 显示环境信息
show_env_info() {
    local env=$1
    
    case $env in
        "test")
            echo -e "${BLUE}📋 测试环境信息:${NC}"
            echo -e "  🎯 用途: 开发测试"
            echo -e "  🗄️  数据库: 本地MySQL (127.0.0.1:3308)"
            echo -e "  📦 Redis: 本地Redis (127.0.0.1:6380)"
            echo -e "  🛡️  安全级别: 中等"
            echo -e "  📊 限流: 1000 req/15min"
            echo -e "  📁 上传限制: 10MB"
            echo -e "  📝 日志级别: INFO"
            ;;
        "local")
            echo -e "${BLUE}📋 本地环境信息:${NC}"
            echo -e "  🎯 用途: 本地开发"
            echo -e "  🗄️  数据库: 内网MySQL (172.16.238.100:3306)"
            echo -e "  📦 Redis: 内网Redis (172.16.238.101:6379)"
            echo -e "  🛡️  安全级别: 宽松"
            echo -e "  📊 限流: 2000 req/15min"
            echo -e "  📁 上传限制: 20MB"
            echo -e "  📝 日志级别: DEBUG"
            echo -e "  🔍 调试模式: 启用"
            ;;
        "online")
            echo -e "${BLUE}📋 生产环境信息:${NC}"
            echo -e "  🎯 用途: 生产部署"
            echo -e "  🗄️  数据库: 阿里云RDS"
            echo -e "  📦 Redis: 阿里云Redis"
            echo -e "  🛡️  安全级别: 最高"
            echo -e "  📊 限流: 500 req/15min"
            echo -e "  📁 上传限制: 5MB"
            echo -e "  📝 日志级别: WARN"
            echo -e "  🔒 HTTPS会话: 启用"
            echo -e "  🚫 调试模式: 禁用"
            ;;
    esac
}

# 加载环境配置
load_environment() {
    local env=$1
    local file="$env.sh"
    
    echo -e "${YELLOW}📋 正在加载 $env 环境配置...${NC}"
    
    # 检查文件是否存在
    check_env_file $env
    
    # 加载配置文件
    source "$file"
    
    echo -e "${GREEN}✅ $env 环境配置加载成功${NC}"
    echo ""
    
    # 显示环境信息
    show_env_info $env
    echo ""
    
    # 显示关键配置
    echo -e "${BLUE}🔧 关键配置信息:${NC}"
    echo -e "  环境类型: ${NODE_ENV:-未设置}"
    echo -e "  数据库: $BB_MYSQL_HOST:$BB_MYSQL_PORT/$BB_MYSQL_DBNAME"
    echo -e "  Redis: $BB_REDIS_HOST:$BB_REDIS_PORT"
    echo -e "  API地址: $BB_API_URL"
    echo -e "  安全增强: ${BB_ENHANCED_SECURITY_ENABLED:-false}"
    echo -e "  监控系统: ${BB_MONITORING_ENABLED:-false}"
    echo -e "  限流最大值: ${BB_RATE_LIMIT_MAX:-未设置}"
    echo ""
}

# 验证环境配置
verify_environment() {
    local env=$1
    
    echo -e "${YELLOW}🔍 验证环境配置...${NC}"
    
    # 检查必需的环境变量
    local required_vars=(
        "BB_MYSQL_HOST"
        "BB_MYSQL_PORT" 
        "BB_MYSQL_DBNAME"
        "BB_REDIS_HOST"
        "BB_REDIS_PORT"
        "BB_API_URL"
        "BB_ENHANCED_SECURITY_ENABLED"
        "BB_MONITORING_ENABLED"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 环境配置验证通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 环境配置验证失败${NC}"
        echo -e "${RED}缺少以下环境变量:${NC}"
        for var in "${missing_vars[@]}"; do
            echo -e "  - $var"
        done
        return 1
    fi
}

# 显示后续操作建议
show_next_steps() {
    local env=$1
    
    echo -e "${BLUE}📋 后续操作建议:${NC}"
    echo ""
    
    case $env in
        "test"|"local")
            echo -e "1. 启动开发服务器:"
            echo -e "   ${GREEN}cd Blackbear/core/backend${NC}"
            echo -e "   ${GREEN}npm run dev${NC}"
            echo ""
            echo -e "2. 检查健康状态:"
            echo -e "   ${GREEN}curl http://localhost:7001/api/health${NC}"
            echo ""
            echo -e "3. 查看详细配置:"
            echo -e "   ${GREEN}curl http://localhost:7001/api/health/detailed${NC}"
            ;;
        "online")
            echo -e "1. 启动生产服务器:"
            echo -e "   ${GREEN}cd Blackbear/core/backend${NC}"
            echo -e "   ${GREEN}npm start${NC}"
            echo ""
            echo -e "2. 检查健康状态:"
            echo -e "   ${GREEN}curl https://api.finevent.top/api/health${NC}"
            echo ""
            echo -e "⚠️  ${YELLOW}生产环境注意事项:${NC}"
            echo -e "   - 确保JWT密钥已更改"
            echo -e "   - 验证IP白名单设置"
            echo -e "   - 检查HTTPS证书"
            echo -e "   - 监控系统资源使用"
            ;;
    esac
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    local env=$1
    
    # 检查环境名称是否有效
    case $env in
        "test"|"local"|"online")
            ;;
        "-h"|"--help"|"help")
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 错误: 无效的环境名称 '$env'${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    echo -e "${BLUE}🚀 Blackbear 环境切换工具${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    
    # 加载环境配置
    load_environment $env
    
    # 验证环境配置
    if verify_environment $env; then
        echo ""
        show_next_steps $env
        echo ""
        echo -e "${GREEN}🎉 环境切换完成！${NC}"
    else
        echo ""
        echo -e "${RED}❌ 环境切换失败，请检查配置文件${NC}"
        exit 1
    fi
}

# 执行主函数
main "$@"
