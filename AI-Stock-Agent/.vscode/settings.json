{"github.copilot.chat.codeGeneration.useInstructionFiles": true, "github.copilot.chat.agent.thinkingTool": true, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/__pycache__": true, "**/*.pyc": true}, "python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "docker.showStartPage": false, "yaml.schemas": {"https://raw.githubusercontent.com/compose-spec/compose-spec/master/schema/compose-spec.json": ["docker-compose.yml", "docker-<PERSON>.yaml"]}}