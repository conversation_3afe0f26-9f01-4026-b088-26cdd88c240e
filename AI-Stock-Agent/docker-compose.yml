# version: "3.8"
services:

  # open-webui:
  #   image: ghcr.io/open-webui/open-webui:main
  #   container_name: open-webui
  #   restart: always
  #   privileged: true
  #   ports:
  #    - 7070:8080/tcp
  #   environment:
  #    - OLLAMA_API_BASE_URL=http://ollama:11434/api
  #    # uncomment the following if you are running ollama on the docker host and remove the ollama service below
  #   #  - OLLAMA_API_BASE_URL=http://host.docker.internal:11434/api 
  #   volumes:
  #     - ./config/open-webui:/app/backend/data
  #   # depends_on:
  #   #  - ollama
  #   networks:
  #     - llm_network

  ollama:
    image: ollama/ollama
    container_name: ollama
    restart: always
    privileged: true
    ports:
     - 11434:11434/tcp
    healthcheck:
      test: ollama --version || exit 1
    volumes:
      - ./config/ollama:/root/.ollama
    networks:
      - llm_network
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # anythingllm:
  #   image: mintplexlabs/anythingllm
  #   container_name: anythingllm
  #   restart: always
  #   cap_add:
  #     - SYS_ADMIN
  #   privileged: true
  #   ports:
  #     - "3001:3001"
  #   volumes:
  #     # chmd 777 anythingllm
  #     - ./config/anythingllm/data:/app/server/storage
  #     # - ./config/anythingllm/.env:/app/server/.env
  #   depends_on:
  #     - ollama
  #   networks:
  #     - llm_network

  n8n:
    image: n8nio/n8n
    container_name: n8n
    restart: always
    privileged: true
    ports:
      - "5678:5678"
    dns:
      - *******
    env_file:
      - .env
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - NODE_ENV=production
      - N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true
    volumes:
      - ./config/n8n:/home/<USER>/.n8n
      # - ./config/files:/files
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - llm_network

  n8n-mcp-server:
    image: ghcr.io/czlonkowski/n8n-mcp:latest
    # build:
    #   context: ./n8n-mcp-server-main
    #   dockerfile: Dockerfile
    container_name: n8n-mcp-server
    restart: always
    ports:
      - "5679:3000"
    env_file:
      - .env
    environment:
      - LOG_LEVEL=info
      - DISABLE_CONSOLE_OUTPUT=false
      - MCP_MODE=http
      - AUTH_TOKEN=${N8N_MCP_AUTH_TOKEN}
      - N8N_API_URL=http://n8n:5678/api/v1
      - N8N_API_KEY=${N8N_API_KEY}
      - N8N_WEBHOOK_USERNAME=${N8N_WEBHOOK_USERNAME}
      - N8N_WEBHOOK_PASSWORD=${N8N_WEBHOOK_PASSWORD}
      - NODE_ENV=production
    volumes:
      - ./config/n8n-mcp-server:/app/data
    depends_on:
      n8n:
        condition: service_started
    networks:
      - llm_network


  # crawl4ai:
  #   image: unclecode/crawl4ai:all
  #   container_name: crawl4ai
  #   restart: always
  #   privileged: true
  #   ports:
  #     - "11235:11235"
  #   environment:
  #     - CRAWL4AI_API_TOKEN=12345  # Optional API security
  #     # - MAX_CONCURRENT_TASKS=5
  #     # LLM Provider Keys
  #     # - OPENAI_API_KEY=${OPENAI_API_KEY:-}
  #     # - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
  #   platform: linux/arm64/v8
  #   volumes:
  #     - ./config/crawl4ai:/dev/shm
  #   networks:
  #     - llm_network
    # deploy:
    #   resources:
    #     limits:
    #       memory: 4G
    #     reservations:
    #       memory: 1G
  # changedetection:
  #   image: ghcr.io/dgtlmoon/changedetection.io
  #   container_name: changedetection
  #   restart: always
  #   volumes:
  #     - ./config/changedetection:/datastore
  #   ports:
  #     - 5000:5000

  # huginn:
  #   image: ghcr.io/huginn/huginn
  #   container_name: huginn
  #   restart: always
  #   platform: linux/amd64
  #   volumes:
  #     # 持久化存储数据
  #     - ./config/huginn/data:/var/lib/mysql
  #     - ./config/huginn/config:/etc/huginn
  #     - ./config/huginn/logs:/var/log/huginn
  #   ports:
  #     - 4750:3000
networks:
  llm_network:
    driver: bridge