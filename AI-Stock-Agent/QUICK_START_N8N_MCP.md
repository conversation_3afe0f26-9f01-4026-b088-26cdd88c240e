# n8n-MCP 快速启动指南

## 🚀 快速启动

### 1. 启动服务
```bash
# 使用便捷脚本启动
./scripts/start-n8n-mcp.sh start

# 或者使用 docker-compose
docker-compose up -d n8n n8n-mcp-server
```

### 2. 验证服务
```bash
# 运行测试脚本
./scripts/test-n8n-mcp.sh
```

### 3. VS Code 配置

#### 必需设置
1. 安装 GitHub Copilot 扩展
2. 在设置中启用：
   - `GitHub > Copilot > Chat > Code Generation: Use Instruction Files` ✅
   - `GitHub > Copilot > Chat > Agent: Thinking Tool` ✅ (可选)
3. 切换到 Agent 模式

#### 认证 Token
当 VS Code 提示输入 n8n-MCP token 时，使用：
```
n8n-mcp-secure-token-2024-very-long-secure-key-for-authentication
```

## 📋 服务信息

| 服务 | 地址 | 说明 |
|------|------|------|
| n8n Web UI | http://localhost:5678 | n8n 工作流管理界面 |
| n8n-MCP API | http://localhost:5679/mcp | MCP 协议端点 |
| 健康检查 | http://localhost:5679/health | 服务状态检查 |

## 🔧 配置文件

| 文件 | 用途 |
|------|------|
| `.vscode/mcp.json` | VS Code MCP 服务器配置 |
| `.github/copilot-instructions.md` | GitHub Copilot 指令 |
| `.env` | 环境变量配置 |
| `docker-compose.yml` | Docker 服务配置 |

## 💡 使用示例

在 VS Code 中使用 GitHub Copilot Chat：

```
#fetch https://blog.n8n.io/rag-chatbot/ 
use #sequentialthinking and #n8n-mcp tools to build a new n8n workflow step-by-step following the guidelines in the blog. In the end, please deploy a fully-functional n8n workflow.
```

## 🛠️ 管理命令

```bash
# 启动服务
./scripts/start-n8n-mcp.sh start

# 停止服务
./scripts/start-n8n-mcp.sh stop

# 重启服务
./scripts/start-n8n-mcp.sh restart

# 查看状态
./scripts/start-n8n-mcp.sh status

# 查看日志
./scripts/start-n8n-mcp.sh logs

# 测试服务
./scripts/test-n8n-mcp.sh
```

## ⚠️ 故障排除

### 服务无法启动
```bash
# 检查端口占用
lsof -i :5678
lsof -i :5679

# 重启服务
./scripts/start-n8n-mcp.sh restart
```

### 认证失败
1. 确认 `.env` 文件中的 `N8N_MCP_AUTH_TOKEN` 正确
2. 检查 VS Code 中输入的 token 是否匹配
3. 重启 n8n-mcp-server 服务

### VS Code 无法连接
1. 确认 GitHub Copilot 扩展已安装并启用
2. 检查 `.vscode/mcp.json` 配置
3. 确认已启用指令文件设置

## 📚 更多信息

- 详细配置指南：[docs/N8N_MCP_SETUP.md](docs/N8N_MCP_SETUP.md)
- n8n-MCP 官方文档：https://github.com/czlonkowski/n8n-mcp
- VS Code MCP 配置：https://code.visualstudio.com/mcp

## ✅ 配置完成检查清单

- [ ] Docker 服务正常运行
- [ ] n8n Web UI 可访问 (http://localhost:5678)
- [ ] n8n-MCP 服务可访问 (http://localhost:5679/health)
- [ ] VS Code 已安装 GitHub Copilot 扩展
- [ ] VS Code 已启用指令文件设置
- [ ] VS Code 已切换到 Agent 模式
- [ ] 认证 token 配置正确
- [ ] 测试脚本运行成功

🎉 **恭喜！n8n-MCP 服务已成功配置并可以使用了！**
