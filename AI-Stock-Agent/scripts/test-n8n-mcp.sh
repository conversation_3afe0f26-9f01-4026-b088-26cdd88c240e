#!/bin/bash

# n8n-MCP 服务测试脚本
# 用于验证 n8n-MCP 服务是否正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 从 .env 文件读取 AUTH_TOKEN
get_auth_token() {
    if [ -f ".env" ]; then
        AUTH_TOKEN=$(grep "N8N_MCP_AUTH_TOKEN" .env | cut -d '=' -f2)
        if [ -z "$AUTH_TOKEN" ]; then
            log_error "无法从 .env 文件中读取 N8N_MCP_AUTH_TOKEN"
            exit 1
        fi
    else
        log_error ".env 文件不存在"
        exit 1
    fi
}

# 测试服务连接
test_service_connection() {
    log_info "测试 n8n-MCP 服务连接..."
    
    # 测试基本连接
    if curl -s -f http://localhost:5679/health > /dev/null 2>&1; then
        log_success "n8n-MCP 服务连接正常"
    else
        log_warning "n8n-MCP 健康检查端点不可用，尝试其他端点..."
    fi
    
    # 测试 MCP 端点认证
    response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -X POST \
        http://localhost:5679/mcp -o /dev/null)

    if [ "$response" = "200" ] || [ "$response" = "400" ]; then
        log_success "n8n-MCP API 端点可访问，认证正常 (HTTP $response)"
    elif [ "$response" = "401" ]; then
        log_error "n8n-MCP API 认证失败 (HTTP $response)"
        return 1
    else
        log_warning "n8n-MCP API 端点响应异常 (HTTP $response)"
    fi
}

# 测试 n8n 服务
test_n8n_service() {
    log_info "测试 n8n 服务连接..."
    
    response=$(curl -s -w "%{http_code}" http://localhost:5678 -o /dev/null)
    
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        log_success "n8n 服务连接正常 (HTTP $response)"
    else
        log_error "n8n 服务不可访问 (HTTP $response)"
        return 1
    fi
}

# 测试 Docker 容器状态
test_docker_containers() {
    log_info "检查 Docker 容器状态..."
    
    # 检查 n8n 容器
    if docker-compose ps n8n | grep -q "Up"; then
        log_success "n8n 容器运行正常"
    else
        log_error "n8n 容器未运行"
        docker-compose ps n8n
        return 1
    fi
    
    # 检查 n8n-mcp-server 容器
    if docker-compose ps n8n-mcp-server | grep -q "Up"; then
        log_success "n8n-mcp-server 容器运行正常"
    else
        log_error "n8n-mcp-server 容器未运行"
        docker-compose ps n8n-mcp-server
        return 1
    fi
}

# 显示服务信息
show_service_info() {
    log_info "服务信息:"
    echo "  n8n Web UI: http://localhost:5678"
    echo "  n8n-MCP API: http://localhost:5679/mcp"
    echo "  认证 Token: $AUTH_TOKEN"
    echo ""
    log_info "VS Code 配置:"
    echo "  MCP 配置文件: .vscode/mcp.json"
    echo "  Copilot 指令: .github/copilot-instructions.md"
    echo ""
    log_info "使用说明:"
    echo "  1. 在 VS Code 中启用 GitHub Copilot"
    echo "  2. 切换到 Agent 模式"
    echo "  3. 当提示输入 token 时，使用: $AUTH_TOKEN"
}

# 主测试函数
run_tests() {
    log_info "开始 n8n-MCP 服务测试..."
    echo ""
    
    # 获取认证 token
    get_auth_token
    
    # 运行测试
    test_docker_containers
    echo ""
    
    test_n8n_service
    echo ""
    
    test_service_connection
    echo ""
    
    show_service_info
    echo ""
    
    log_success "所有测试完成！n8n-MCP 服务配置正确。"
}

# 显示帮助信息
show_help() {
    echo "n8n-MCP 服务测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "此脚本将测试:"
    echo "  - Docker 容器状态"
    echo "  - n8n 服务连接"
    echo "  - n8n-MCP API 连接"
    echo "  - 认证配置"
}

# 主函数
main() {
    case "${1:-test}" in
        "test"|"")
            run_tests
            ;;
        "-h"|"--help"|"help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
