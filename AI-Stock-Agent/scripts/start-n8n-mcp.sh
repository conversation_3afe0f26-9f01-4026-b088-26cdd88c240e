#!/bin/bash

# n8n-MCP 服务启动脚本
# 用于启动和管理 n8n 和 n8n-mcp-server 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    log_success "Docker 运行正常"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        log_error ".env 文件不存在，请先创建环境变量文件"
        exit 1
    fi
    log_success "环境变量文件存在"
}

# 检查端口占用
check_ports() {
    local ports=("5678" "5679")
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            log_warning "端口 $port 已被占用"
            read -p "是否继续？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    done
}

# 启动服务
start_services() {
    log_info "启动 n8n 和 n8n-mcp-server 服务..."
    
    # 启动 n8n 服务
    docker-compose up -d n8n
    
    # 等待 n8n 启动
    log_info "等待 n8n 服务启动..."
    sleep 10
    
    # 启动 n8n-mcp-server
    docker-compose up -d n8n-mcp-server
    
    log_success "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查 n8n 服务
    if docker-compose ps n8n | grep -q "Up"; then
        log_success "n8n 服务运行正常 (http://localhost:5678)"
    else
        log_error "n8n 服务未运行"
    fi
    
    # 检查 n8n-mcp-server 服务
    if docker-compose ps n8n-mcp-server | grep -q "Up"; then
        log_success "n8n-mcp-server 服务运行正常 (http://localhost:5679)"
    else
        log_error "n8n-mcp-server 服务未运行"
    fi
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f n8n n8n-mcp-server
}

# 停止服务
stop_services() {
    log_info "停止 n8n 和 n8n-mcp-server 服务..."
    docker-compose stop n8n n8n-mcp-server
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 n8n 和 n8n-mcp-server 服务..."
    docker-compose restart n8n n8n-mcp-server
    log_success "服务已重启"
}

# 显示帮助信息
show_help() {
    echo "n8n-MCP 服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    检查服务状态"
    echo "  logs      显示服务日志"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务"
    echo "  $0 status   # 检查状态"
    echo "  $0 logs     # 查看日志"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_docker
            check_env_file
            check_ports
            start_services
            sleep 5
            check_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            sleep 5
            check_services
            ;;
        "status")
            check_services
            ;;
        "logs")
            show_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
