# n8n-MCP 服务配置指南

本文档说明如何在 AI-Stock-Agent 项目中配置和使用 n8n-MCP 服务。

## 概述

n8n-MCP (Model Context Protocol) 服务允许您通过 VS Code + GitHub Copilot 与 n8n 工作流进行智能交互。

## 服务配置

### 1. Docker 服务

n8n-MCP 服务已在 `docker-compose.yml` 中配置：

- **端口**: 5679 (映射到容器内部的 3000)
- **模式**: HTTP
- **认证**: Bearer Token
- **n8n API**: 连接到本地 n8n 实例

### 2. 环境变量

敏感信息存储在 `.env` 文件中：

```bash
# n8n-mcp-server configuration
N8N_MCP_AUTH_TOKEN=n8n-mcp-secure-token-2024
N8N_API_KEY=your-n8n-api-key
N8N_BASIC_AUTH_USER=mizhdi
N8N_BASIC_AUTH_PASSWORD=13819385701
N8N_WEBHOOK_USERNAME=mizhdi
N8N_WEBHOOK_PASSWORD=13819385701
```

### 3. VS Code 配置

#### MCP 服务器配置 (`.vscode/mcp.json`)

```json
{
  "inputs": [
    {
      "type": "promptString",
      "id": "n8n-mcp-token",
      "description": "Your n8n-MCP AUTH_TOKEN",
      "password": true
    }
  ],
  "servers": {
    "n8n-mcp": {
      "type": "http",
      "url": "http://localhost:5679/mcp",
      "headers": {
        "Authorization": "Bearer ${input:n8n-mcp-token}"
      }
    }
  }
}
```

#### GitHub Copilot 指令 (`.github/copilot-instructions.md`)

包含详细的 n8n 工作流开发指导，包括：
- 节点发现和配置
- 验证策略
- 部署流程
- 最佳实践

## 使用步骤

### 1. 启动服务

```bash
cd AI-Stock-Agent
docker-compose up -d n8n n8n-mcp-server
```

### 2. 验证服务状态

```bash
# 检查服务状态
docker-compose ps

# 查看 n8n-mcp-server 日志
docker-compose logs n8n-mcp-server
```

### 3. VS Code 配置

1. 确保安装了 GitHub Copilot 扩展
2. 在 VS Code 设置中启用：
   - `GitHub > Copilot > Chat > Code Generation: Use Instruction Files`
   - `GitHub > Copilot > Chat > Agent: Thinking Tool` (可选)
3. 切换到 Agent 模式

### 4. 使用示例

在 VS Code 中使用 GitHub Copilot Chat：

```
#fetch https://blog.n8n.io/rag-chatbot/ 
use #sequentialthinking and #n8n-mcp tools to build a new n8n workflow step-by-step following the guidelines in the blog. In the end, please deploy a fully-functional n8n workflow.
```

## 服务端点

- **n8n Web UI**: http://localhost:5678
- **n8n-MCP API**: http://localhost:5679/mcp
- **n8n API**: http://localhost:5678/api/v1

## 认证信息

当 VS Code 提示输入 n8n-MCP token 时，使用：
```
n8n-mcp-secure-token-2024-very-long-secure-key-for-authentication
```

## 故障排除

### 1. 服务无法启动

```bash
# 检查端口占用
lsof -i :5679

# 重启服务
docker-compose restart n8n-mcp-server
```

### 2. 认证失败

- 确认 `.env` 文件中的 `N8N_MCP_AUTH_TOKEN` 正确
- 检查 VS Code 中输入的 token 是否匹配

### 3. n8n API 连接失败

- 确认 n8n 服务正在运行
- 检查 `N8N_API_KEY` 是否有效
- 验证 n8n 的 API 访问权限

## 高级配置

### 安装 Sequential Thinking MCP 服务器

为了获得更好的结果，建议安装 Sequential Thinking MCP 服务器：

```bash
# 参考官方文档
# https://github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking
```

### 自定义工作流模板

可以在 `config/n8n-mcp-server/` 目录中添加自定义模板和配置。

## 参考资源

- [n8n-MCP 官方文档](https://github.com/czlonkowski/n8n-mcp)
- [VS Code MCP 配置指南](https://code.visualstudio.com/mcp)
- [n8n 官方文档](https://docs.n8n.io/)
